<template>
  <div class="coupon-payment-container">
    <div class="coupon-section">
      <!-- 已选择的优惠券 -->
      <div v-if="selectedCoupons.length > 0" class="selected-coupons">
        <h5 class="section-subtitle">已选择优惠券</h5>
        <div class="selected-list">
          <div v-for="coupon in selectedCoupons" :key="coupon.couponId" class="selected-coupon">
            <div class="coupon-content">
              <div class="coupon-info">
                <div class="coupon-name">{{ coupon.name }}</div>
                <div class="coupon-value">
                  <span v-if="coupon.type === 'amount'" class="amount-value">
                    ¥{{ formatAmount(coupon.value) }}
                  </span>
                  <span v-else class="discount-value"> {{ formatDiscount(coupon.value) }}折 </span>
                </div>
              </div>
              <div class="coupon-actions">
                <a-button
                  type="text"
                  size="small"
                  status="danger"
                  @click="handleRemoveCoupon(coupon.couponId)"
                >
                  <template #icon>
                    <icon-close />
                  </template>
                  移除
                </a-button>
              </div>
            </div>
            <div class="coupon-discount">
              优惠金额：¥{{ formatAmount(calculateCouponDiscount(coupon)) }}
            </div>
          </div>
        </div>
      </div>

      <!-- 可用优惠券列表 -->
      <div class="available-coupons">
        <div class="section-header">
          <h5 class="section-subtitle">可用优惠券 ({{ availableCouponsFiltered.length }})</h5>
          <a-button
            v-if="availableCoupons.length > showLimit"
            type="text"
            size="small"
            @click="toggleShowAll"
          >
            {{ showAll ? '收起' : `查看全部${availableCoupons.length}张` }}
          </a-button>
        </div>

        <div v-if="availableCouponsFiltered.length === 0" class="empty-coupons">
          <a-empty description="暂无可用优惠券">
            <template #image>
              <icon-gift style="font-size: 48px; color: #c9cdd4" />
            </template>
          </a-empty>
        </div>

        <div v-else class="coupons-list">
          <div
            v-for="coupon in displayedCoupons"
            :key="coupon.couponId"
            class="coupon-item"
            :class="{
              selected: isSelected(coupon.couponId),
              disabled: !coupon.applicable,
            }"
            @click="handleSelectCoupon(coupon)"
          >
            <div class="coupon-card">
              <div class="coupon-left">
                <div class="coupon-value-display">
                  <span v-if="coupon.type === 'amount'" class="value-amount">
                    ¥{{ formatAmount(coupon.value) }}
                  </span>
                  <span v-else class="value-discount">
                    {{ formatDiscount(coupon.value) }}<small>折</small>
                  </span>
                </div>
                <div class="coupon-type">
                  {{ coupon.type === 'amount' ? '立减券' : '折扣券' }}
                </div>
              </div>

              <div class="coupon-right">
                <div class="coupon-details">
                  <div class="coupon-name">{{ coupon.name }}</div>
                  <div class="coupon-description">{{ coupon.description }}</div>
                  <div class="coupon-conditions">
                    <span class="condition-item">
                      满¥{{ formatAmount(coupon.minOrderAmount) }}可用
                    </span>
                    <span v-if="coupon.maxDiscountAmount" class="condition-item">
                      最高优惠¥{{ formatAmount(coupon.maxDiscountAmount) }}
                    </span>
                  </div>
                  <div class="coupon-expire">
                    有效期至：{{ formatExpireTime(coupon.expireTime) }}
                  </div>
                </div>

                <div class="coupon-action">
                  <a-checkbox
                    :model-value="isSelected(coupon.couponId)"
                    :disabled="!coupon.applicable"
                    @click.stop
                    @change="handleSelectCoupon(coupon)"
                  />
                </div>
              </div>
            </div>

            <!-- 不可用原因 -->
            <div v-if="!coupon.applicable" class="disabled-reason">
              <a-tag color="red" size="small">
                {{ getDisabledReason(coupon) }}
              </a-tag>
            </div>
          </div>
        </div>
      </div>

      <!-- 优惠券使用说明 -->
      <div v-if="availableCoupons.length > 0" class="coupon-tips">
        <a-alert type="info" size="small" :show-icon="false">
          <template #icon>
            <icon-info-circle />
          </template>
          <div class="tips-content">
            <p>• 优惠券可与其他支付方式叠加使用</p>
            <p>• 同类型优惠券不可叠加，系统会自动选择最优方案</p>
            <p>• 优惠券使用后不可退回，请谨慎选择</p>
          </div>
        </a-alert>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { computed, ref } from 'vue';
  import { IconClose, IconGift, IconInfoCircle } from '@arco-design/web-vue/es/icon';
  import type { CouponInfo } from '@/http/api/payment/types';

  // Props
  interface Props {
    availableCoupons: CouponInfo[];
    selectedCoupons: CouponInfo[];
    orderAmount: number;
  }

  const props = defineProps<Props>();

  // Emits
  const emit = defineEmits(['select', 'remove']);

  // 本地状态
  const showAll = ref(false);
  const showLimit = 3;

  // 计算属性
  const availableCouponsFiltered = computed(() =>
    props.availableCoupons.filter(
      coupon => !props.selectedCoupons.some(selected => selected.couponId === coupon.couponId)
    )
  );

  const displayedCoupons = computed(() => {
    if (showAll.value) {
      return availableCouponsFiltered.value;
    }
    return availableCouponsFiltered.value.slice(0, showLimit);
  });

  // 方法
  function isSelected(couponId: string): boolean {
    return props.selectedCoupons.some(coupon => coupon.couponId === couponId);
  }

  function handleSelectCoupon(coupon: CouponInfo) {
    if (!coupon.applicable) return;
    emit('select', coupon);
  }

  function handleRemoveCoupon(couponId: string) {
    emit('remove', couponId);
  }

  function calculateCouponDiscount(coupon: CouponInfo): number {
    if (coupon.type === 'amount') {
      return Math.min(coupon.value, props.orderAmount);
    } else if (coupon.type === 'discount') {
      const discountAmount = props.orderAmount * (1 - coupon.value);
      return Math.min(discountAmount, coupon.maxDiscountAmount || discountAmount);
    }
    return 0;
  }

  function getDisabledReason(coupon: CouponInfo): string {
    if (props.orderAmount < coupon.minOrderAmount) {
      return `需满¥${formatAmount(coupon.minOrderAmount)}`;
    }

    const expireTime = new Date(coupon.expireTime).getTime();
    const currentTime = new Date().getTime();

    if (currentTime > expireTime) {
      return '已过期';
    }

    return '不适用';
  }

  function toggleShowAll() {
    showAll.value = !showAll.value;
  }

  function formatAmount(amount: number): string {
    return amount.toFixed(2);
  }

  function formatDiscount(discount: number): string {
    return (discount * 10).toFixed(1);
  }

  function formatExpireTime(timeString: string): string {
    const date = new Date(timeString);
    return date.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
    });
  }
</script>

<style scoped lang="scss">
  .coupon-payment-container {
    width: 100%;
  }

  .coupon-section {
    display: flex;
    flex-direction: column;
    gap: 16px;
  }

  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .section-subtitle {
    font-size: 14px;
    font-weight: 600;
    color: $text-color;
    margin: 0;
  }

  .selected-coupons {
    .selected-list {
      display: flex;
      flex-direction: column;
      gap: 8px;
      margin-top: 12px;
    }

    .selected-coupon {
      background-color: #e8f3ff;
      border: 1px solid #d1e7ff;
      border-radius: 6px;
      padding: 12px;

      .coupon-content {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 8px;
      }

      .coupon-info {
        flex: 1;

        .coupon-name {
          font-size: 14px;
          font-weight: 500;
          color: $text-color;
          margin-bottom: 4px;
        }

        .coupon-value {
          .amount-value {
            color: $primary-color;
            font-weight: 600;
          }

          .discount-value {
            color: $warning-color;
            font-weight: 600;
          }
        }
      }

      .coupon-discount {
        font-size: 12px;
        color: $success-color;
        font-weight: 500;
      }
    }
  }

  .available-coupons {
    .empty-coupons {
      padding: 40px 20px;
      text-align: center;
    }

    .coupons-list {
      display: flex;
      flex-direction: column;
      gap: 12px;
      margin-top: 12px;
    }
  }

  .coupon-item {
    border: 1px solid #e5e6eb;
    border-radius: 8px;
    background-color: #ffffff;
    cursor: pointer;
    transition: all 0.3s ease;
    overflow: hidden;

    &:hover:not(.disabled) {
      border-color: $primary-color;
      box-shadow: 0 2px 8px rgba(22, 93, 255, 0.1);
    }

    &.selected {
      border-color: $primary-color;
      background-color: #f0f8ff;
    }

    &.disabled {
      background-color: #f7f8fa;
      border-color: #e5e6eb;
      cursor: not-allowed;
      opacity: 0.6;
    }
  }

  .coupon-card {
    display: flex;
    padding: 16px;
  }

  .coupon-left {
    flex-shrink: 0;
    width: 80px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #ff7d00 0%, #ff9a33 100%);
    border-radius: 6px;
    color: white;
    margin-right: 16px;

    .value-amount {
      font-size: 18px;
      font-weight: 700;
      line-height: 1;
    }

    .value-discount {
      font-size: 16px;
      font-weight: 700;
      line-height: 1;

      small {
        font-size: 12px;
        font-weight: 500;
      }
    }

    .coupon-type {
      font-size: 10px;
      margin-top: 4px;
      opacity: 0.9;
    }
  }

  .coupon-right {
    flex: 1;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .coupon-details {
    flex: 1;

    .coupon-name {
      font-size: 15px;
      font-weight: 500;
      color: $text-color;
      margin-bottom: 6px;
      line-height: 1.3;
    }

    .coupon-description {
      font-size: 13px;
      color: $text-color-secondary;
      margin-bottom: 8px;
      line-height: 1.4;
    }

    .coupon-conditions {
      display: flex;
      gap: 8px;
      margin-bottom: 6px;
      flex-wrap: wrap;

      .condition-item {
        font-size: 12px;
        color: $text-color-tertiary;
        background-color: #f2f3f5;
        padding: 2px 6px;
        border-radius: 3px;
      }
    }

    .coupon-expire {
      font-size: 12px;
      color: $text-color-tertiary;
    }
  }

  .coupon-action {
    flex-shrink: 0;
    margin-left: 12px;
  }

  .disabled-reason {
    padding: 8px 16px;
    border-top: 1px solid #f2f3f5;
  }

  .coupon-tips {
    background-color: #f0f8ff;
    border-radius: 6px;
    padding: 12px;

    .tips-content {
      p {
        margin: 0 0 4px 0;
        font-size: 12px;
        color: $text-color-secondary;

        &:last-child {
          margin-bottom: 0;
        }
      }
    }

    :deep(.arco-alert) {
      background-color: transparent;
      border: none;
      padding: 0;
    }

    :deep(.arco-alert-icon) {
      color: $primary-color;
    }
  }

  // 响应式设计
  @media (max-width: 768px) {
    .coupon-card {
      padding: 12px;
    }

    .coupon-left {
      width: 70px;
      margin-right: 12px;

      .value-amount {
        font-size: 16px;
      }

      .value-discount {
        font-size: 14px;
      }
    }

    .coupon-details {
      .coupon-name {
        font-size: 14px;
      }

      .coupon-description {
        font-size: 12px;
      }

      .coupon-conditions {
        gap: 6px;

        .condition-item {
          font-size: 11px;
        }
      }
    }

    .selected-coupon {
      padding: 10px;
    }

    .coupon-tips {
      padding: 10px;
    }
  }
</style>
