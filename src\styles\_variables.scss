// ==================== 主色调设计体系 ====================

// 品牌主色 - 文创链蓝色系
$primary-color: #165dff !default; // 主品牌色
$primary-light: #4080ff !default; // 浅色主色
$primary-lighter: #7aa3ff !default; // 更浅主色
$primary-lightest: #e8f3ff !default; // 最浅主色
$primary-dark: #0f58d3 !default; // 深色主色
$primary-darker: #0a4bb8 !default; // 更深主色
$primary-darkest: #083e9d !default; // 最深主色

// 辅助色系
$secondary-color: #385087 !default; // 辅助色
$secondary-light: #5a7aa8 !default; // 浅辅助色
$secondary-dark: #2a3f66 !default; // 深辅助色

// 功能色系
$success-color: #00b42a !default; // 成功色
$success-light: #23c343 !default; // 浅成功色
$success-dark: #009a29 !default; // 深成功色

$warning-color: #ff7d00 !default; // 警告色
$warning-light: #ff9a33 !default; // 浅警告色
$warning-dark: #d25f00 !default; // 深警告色

$error-color: #f53f3f !default; // 错误色
$error-light: #f76560 !default; // 浅错误色
$error-dark: #cb2634 !default; // 深错误色

$info-color: #165dff !default; // 信息色（使用主色）
$info-light: #4080ff !default; // 浅信息色
$info-dark: #0f58d3 !default; // 深信息色

// 中性色系
$text-color: #1d2129 !default; // 主文本色
$text-color-secondary: #4e5969 !default; // 次要文本色
$text-color-tertiary: #86909c !default; // 第三级文本色
$text-color-disabled: #c9cdd4 !default; // 禁用文本色
$text-color-inverse: #ffffff !default; // 反色文本

// 背景色系
$background-color: #ffffff !default; // 主背景色
$background-color-secondary: #f7f8fa !default; // 次要背景色
$background-color-tertiary: #f2f3f5 !default; // 第三级背景色
$background-color-dark: #17171a !default; // 深色背景
$background-color-mask: rgba(0, 0, 0, 0.6) !default; // 遮罩背景

// 边框色系
$border-color: #e5e6eb !default; // 主边框色
$border-color-light: #f2f3f5 !default; // 浅边框色
$border-color-dark: #c9cdd4 !default; // 深边框色
$border-color-focus: $primary-color !default; // 聚焦边框色

// 链接色系
$link-color: $primary-color !default; // 链接色
$link-hover-color: $primary-light !default; // 链接悬停色
$link-active-color: $primary-dark !default; // 链接激活色
$link-visited-color: #722ed1 !default; // 已访问链接色

// 布局相关变量
$header-height: 60px !default;
$footer-height: 120px !default;
$sidebar-width: 240px !default;
$container-width: 1200px !default;

// 响应式断点
$screen-xs: 480px !default;
$screen-sm: 576px !default;
$screen-md: 768px !default;
$screen-lg: 992px !default;
$screen-xl: 1200px !default;
$screen-xxl: 1600px !default;

// 字体
$font-family:
  -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans',
  sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji' !default;
$font-size-base: 14px !default;
$font-size-sm: 12px !default;
$font-size-lg: 16px !default;
$font-size-xl: 18px !default;
$font-size-xxl: 24px !default;

// 边距
$spacing-xs: 4px !default;
$spacing-sm: 8px !default;
$spacing-md: 16px !default;
$spacing-lg: 24px !default;
$spacing-xl: 32px !default;

// 圆角
$border-radius-base: 4px !default;
$border-radius-sm: 2px !default;
$border-radius-lg: 8px !default;
$border-radius-circle: 50% !default;

// 阴影
$box-shadow-base: 0 2px 8px rgba(0, 0, 0, 0.15) !default;
$box-shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.1) !default;
$box-shadow-lg: 0 4px 12px rgba(0, 0, 0, 0.2) !default;

// z-index
$zindex-dropdown: 1000 !default;
$zindex-sticky: 1020 !default;
$zindex-fixed: 1030 !default;
$zindex-modal-backdrop: 1040 !default;
$zindex-modal: 1050 !default;
$zindex-popover: 1060 !default;
$zindex-tooltip: 1070 !default;
