<template>
  <div class="page-container">
    <div class="page-header">
      <h1 class="page-title">软件著作权登记公示列表</h1>
      <p class="page-description">查看所有正在公示中的软件著作权登记申请</p>
    </div>

    <div class="content-wrapper">
      <div class="filter-section">
        <a-form :model="filterForm" layout="inline">
          <a-form-item field="keyword">
            <a-input v-model="filterForm.keyword" placeholder="请输入关键词" allow-clear />
          </a-form-item>
          <a-form-item field="status">
            <a-select
              v-model="filterForm.status"
              placeholder="公示状态"
              style="width: 120px"
              allow-clear
            >
              <a-option value="ongoing"> 公示中 </a-option>
              <a-option value="finished"> 已结束 </a-option>
            </a-select>
          </a-form-item>
          <a-form-item>
            <a-button type="primary" @click="handleSearch">
              <icon-search />
              搜索
            </a-button>
          </a-form-item>
        </a-form>
      </div>

      <a-table
        :data="announcementList"
        :loading="loading"
        :pagination="pagination"
        @page-change="onPageChange"
      >
        <template #columns>
          <a-table-column title="软件名称" data-index="title" />
          <a-table-column title="申请编号" data-index="applicationNumber" />
          <a-table-column title="申请主体" data-index="applicant" />
          <a-table-column title="公示开始日期" data-index="startDate" />
          <a-table-column title="公示截止日期" data-index="endDate" />
          <a-table-column title="状态" data-index="status">
            <template #cell="{ record }">
              <a-tag color="blue">
                {{ record.status }}
              </a-tag>
            </template>
          </a-table-column>
          <a-table-column title="操作">
            <template #cell="{ record }">
              <a-button type="text" @click="viewDetails(record.id)"> 详情 </a-button>
            </template>
          </a-table-column>
        </template>
      </a-table>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref, reactive } from 'vue';
  import { useRouter } from 'vue-router';
  import { IconSearch } from '@arco-design/web-vue/es/icon';
  import { getSoftwareCopyrightList } from '@/http/api/software';

  // 定义软件著作权公示项目接口
  interface SoftwareAnnouncement {
    id: string;
    title: string;
    applicationNumber: string;
    startDate: string;
    endDate: string;
    applicant: string;
    status: string;
  }

  // 定义API响应接口
  interface ApiResponse<T> {
    list: T[];
    total?: number;
    [key: string]: any;
  }

  const router = useRouter();
  const loading = ref(false);
  const announcementList = ref<SoftwareAnnouncement[]>([]);

  // 筛选表单
  const filterForm = reactive({
    keyword: '',
    status: '',
  });

  // 分页
  const pagination = reactive({
    current: 1,
    pageSize: 10,
    total: 0,
  });

  // 获取公示列表数据
  const fetchAnnouncementList = async () => {
    try {
      loading.value = true;

      // 使用示例数据
      // 在实际项目中取消注释以下代码，并注释掉mock数据部分
      /*
      const params = {
        page: pagination.current,
        size: pagination.pageSize,
        keyword: filterForm.keyword,
        status: filterForm.status
      };
      
      const response = await getSoftwareCopyrightList(params);
      if (response && response.list) {
        announcementList.value = response.list;
        pagination.total = response.total || 0;
      }
      */

      // 模拟数据 - 在实际项目中替换为上面的API调用
      announcementList.value = [
        {
          id: '1',
          title: '某企业管理系统V1.0',
          applicationNumber: 'SW2023-0001',
          startDate: '2023-12-15',
          endDate: '2023-12-31',
          applicant: '某科技有限公司',
          status: '公示中',
        },
        {
          id: '2',
          title: '智能分析平台V2.1',
          applicationNumber: 'SW2023-0002',
          startDate: '2023-12-10',
          endDate: '2023-12-25',
          applicant: '某信息技术有限公司',
          status: '公示中',
        },
        {
          id: '3',
          title: '在线学习软件V3.0',
          applicationNumber: 'SW2023-0003',
          startDate: '2023-12-05',
          endDate: '2023-12-20',
          applicant: '某教育科技有限公司',
          status: '公示中',
        },
      ];
      pagination.total = 3;
    } catch (error) {
      console.error('获取公示列表失败:', error);
    } finally {
      loading.value = false;
    }
  };

  // 页码变化
  const onPageChange = (page: number) => {
    pagination.current = page;
    fetchAnnouncementList();
  };

  // 搜索
  const handleSearch = () => {
    pagination.current = 1;
    fetchAnnouncementList();
  };

  // 查看详情
  const viewDetails = (id: string) => {
    router.push(`/software/page/details/${id}`);
  };

  // 初始化
  fetchAnnouncementList();
</script>

<style lang="scss" scoped>
  .page-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 40px 20px;
  }

  .page-header {
    text-align: center;
    margin-bottom: 40px;

    .page-title {
      font-size: 2rem;
      font-weight: bold;
      margin-bottom: 12px;
      color: #1d2129;
    }

    .page-description {
      font-size: 1.1rem;
      color: #4e5969;
    }
  }

  .content-wrapper {
    background: #fff;
    border-radius: 8px;
    padding: 24px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  }

  .filter-section {
    margin-bottom: 24px;
  }
</style>
