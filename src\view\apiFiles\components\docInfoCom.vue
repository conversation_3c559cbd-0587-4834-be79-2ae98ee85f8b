<template>
  <div v-if="apiType">
    <a-descriptions :data="contDataIchi" :title="docInfo.summary" bordered />
    <h3>接口描述</h3>
    <div class="doc-overview" v-html="docInfo.description || docInfo.title" />
    <a-descriptions :data="contDataNi" :title="docInfo.description || docInfo.title" bordered />
    <h3>请求方法</h3>
    <div class="doc-request-method">
      {{ docInfo.httpMethodList && docInfo.httpMethodList.join(' / ').toUpperCase() }}
    </div>
    <h2>请求参数</h2>
    <h3>公共请求参数</h3>
    <a-table :columns="contDataSann" :data="commonParams" />
    <h3>业务响应参数</h3>
    <a-table :columns="contDataJshi" :data="docInfo.requestParameters" />
    <h3>响应示例</h3>
    <pre class="normal-text">{{ JSON.stringify(responseSuccessExample, null, 4) }}</pre>
    <h3>错误示例</h3>
    <pre class="normal-text">{{ JSON.stringify(responseErrorExample, null, 4) }}</pre>
    <h2>业务错误码</h2>
    <a-table :columns="contDataRoku" :data="docInfo.bizCodeList" />
  </div>
</template>

<script lang="ts" setup>
  import { ref } from 'vue';
  import { getDocItem } from '@/http/api/apiFiles';
  import params from '@/utils/params';
  const commonParams = params.commonParams;
  const responseSuccessExample = ref<any>();
  const responseErrorExample = ref<any>({
    error_response: {
      request_id: '0d27836fcac345729176359388aeeb74',
      code: '40004',
      msg: '业务处理失败',
      sub_code: 'isv.name-error',
      sub_msg: '姓名错误',
    },
  });
  const apiType = ref<boolean>(false);
  const docInfo = ref<any>({});
  const docUrl = ref<any>('');
  const contDataIchi = ref<any>([]);
  const contDataNi = ref<any>([]);
  const contDataSann = ref<any>([
    {
      title: '名称',
      dataIndex: 'name',
      width: '200',
    },
    {
      title: '类型',
      dataIndex: 'type',
      width: '100',
    },
    {
      title: '必须',
      width: '60',
      dataIndex: 'must',
    },
    {
      title: '描述',
      dataIndex: 'description',
    },
    {
      title: '示例值',
      dataIndex: 'example',
    },
  ]);
  const contDataJshi = ref<any>([
    {
      title: '名称',
      dataIndex: 'name',
      width: '200',
    },
    {
      title: '类型',
      dataIndex: 'type',
      width: '100',
    },
    {
      title: '必须',
      width: '80',
      dataIndex: 'required',
    },
    {
      title: '最大长度',
      width: '100',
      dataIndex: 'maxLength',
    },
    {
      title: '描述',
      dataIndex: 'description',
    },
    {
      title: '示例值',
      width: '100',
      dataIndex: 'paramExample',
    },
  ]);
  const contDataRoku = ref<any>([
    {
      title: 'sub_code（错误码）',
      dataIndex: 'code',
      width: '200',
    },
    {
      title: 'sub_msg（错误描述）',
      dataIndex: 'msg',
    },
    {
      title: '解决方案',
      dataIndex: 'solution',
    },
  ]);
  const getApiInfo = async (name, url) => {
    docUrl.value = url;
    docInfo.value = await getDocItem({
      name,
    });
    createResponseExample();
    intiApiDoc();
    return docInfo.value;
  };

  const intiApiDoc = async () => {
    contDataIchi.value = [
      {
        label: '接口名(method)',
        value: docInfo.value.name,
      },
      {
        label: '版本号(version)',
        value: docInfo.value.version,
      },
    ];
    contDataNi.value = [
      {
        label: '环境',
        value: '正式环境',
      },
      {
        label: '请求地址',
        value: docUrl.value,
      },
    ];
    apiType.value = true;
  };

  const createResponseExample = async () => {
    const ret = {};
    const responseData = {
      request_id: '4b8e7ca9cbcb448491df2f0120e49b9d',
      code: '10000',
      msg: 'success',
    };
    ret[getResponseNodeName()] = responseData;
    const bizRet: any = createExample(docInfo.value.responseParameters);
    console.log(bizRet);
    for (const key in bizRet) {
      responseData[key] = bizRet[key];
    }
    responseSuccessExample.value = ret;
  };

  const createExample = params => {
    console.log(params);
    const responseJson = {};
    for (let i = 0; i < params.length; i++) {
      const row = params[i];
      if (row.in === 'header') {
        continue;
      }
      let val;
      // 如果有子节点
      if (row.refs && row.refs.length > 0) {
        const childrenValue = createExample(row.refs);
        // 如果是数组
        if (row.type === 'array') {
          val = [childrenValue];
        } else {
          val = childrenValue;
        }
      } else {
        // 单值
        val = row.paramExample;
      }
      responseJson[row.name] = val;
    }
    const isOneArray =
      Object.keys(responseJson).length === 1 && isArray(Object.values(responseJson)[0]);
    if (isOneArray) {
      return Object.values(responseJson)[0];
    }
    return responseJson;
  };

  const isArray = obj => {
    return Object.prototype.toString.call(obj) === '[object Array]';
  };

  const getResponseNodeName = () => {
    const name = docInfo.value.name;
    return name.replace(/\./g, '_') + '_response';
  };

  defineExpose({
    getApiInfo,
  });
  // intiApiDoc()
</script>

<style scoped>
  .doc-overview {
    margin-top: 20px;
    margin-bottom: 30px;
    color: #666;
    font-size: 14px;
  }
</style>
