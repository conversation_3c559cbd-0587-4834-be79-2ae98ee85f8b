<template>
  <div class="p-4 bg-grey">
    <a-page-header
      :show-back="false"
      :style="{ background: 'var(--color-bg-2)' }"
      title="用量查询"
    />
    <a-card :bordered="false">
      <a-row class="pb-4">
        <a-col :flex="1">
          <a-form :model="searchParams" label-align="right" layout="inline">
            <a-space size="large">
              <a-form-item field="name" label="接口名称">
                <a-input v-model="searchParams.typeKey" placeholder="请输入.." />
              </a-form-item>
              <a-form-item field="createdTime" label="日期">
                <a-range-picker v-model="rangeValue" />
              </a-form-item>
            </a-space>
          </a-form>
        </a-col>
        <a-divider style="height: 84px" direction="vertical" />
        <a-col :flex="'86px'" style="text-align: right">
          <a-space direction="vertical" :size="18">
            <a-button type="primary" @click="search">
              <template #icon>
                <icon-search />
              </template>
              查询
            </a-button>
            <a-button @click="reset">
              <template #icon>
                <icon-refresh />
              </template>
              重置
            </a-button>
          </a-space>
        </a-col>
      </a-row>
      <a-table
        :loading="loading"
        size="large"
        :columns="columns as [TableColumnData]"
        :scroll="{
          x: 1300,
          y: 480,
        }"
        :scrollbar="true"
        :data="list"
        :bordered="false"
        :pagination="pagination"
        :sticky-header="true"
        :table-layout-fixed="true"
        @page-change="getList"
      >
        <template #status="{ record }">
          <a-tag v-if="record.status == 1" color="green"> 成功 </a-tag>
          <a-tag v-else-if="record.status == 0" color="red"> 失败 </a-tag>
        </template>
      </a-table>
    </a-card>
  </div>
</template>

<script lang="ts" setup>
  import { ref, reactive } from 'vue';
  import { IconSearch, IconRefresh, IconDownload } from '@arco-design/web-vue/es/icon';
  import { getBlockchainList } from '@/http/api/user';
  import { Message } from '@arco-design/web-vue';

  const loading = ref(false);
  const pagination = ref({ pageSize: 10, total: 0 });
  const list = ref([]);
  const searchParams = ref({
    pageIndex: 1,
    pageSize: pagination.value.pageSize,
    typeKey: '',
    startTime: '',
    endTime: '',
  });
  const rangeValue = ref([]); // 选择的日期
  const getList = (pageIndex = 1) => {
    let params = { ...searchParams.value, pageIndex };
    if (rangeValue.value.length) {
      params.startTime = rangeValue.value[0] + ' 00:00:00';
      params.endTime = rangeValue.value[1] + ' 23:59:59';
    }
    loading.value = true;
    getBlockchainList(params).then((res: any) => {
      list.value = res.list as [];
      pagination.value.total = res.total;
      loading.value = false;
    });
  };
  getList();

  const search = () => {
    getList();
  };
  const reset = () => {
    searchParams.value = {
      pageIndex: 1,
      pageSize: pagination.value.pageSize,
      typeKey: '',
      startTime: '',
      endTime: '',
    };
    rangeValue.value = [];
    getList();
  };

  interface TableColumnData {
    dataIndex?: string;
    title?: string;
    width?: number;
    align?: 'left' | 'center' | 'right';
    fixed?: 'left' | 'right';
    ellipsis?: boolean;
    tooltip?: boolean | Record<string, any>;
    slotName?: string;
    titleSlotName?: string;
    isLastLeftFixed?: boolean;
    isFirstRightFixed?: boolean;
    colSpan?: number;
    rowSpan?: number;
    index?: number;
    parent?: TableColumnData;
  }
  const columns = [
    {
      title: '接口名称',
      dataIndex: 'typeName',
      slotName: 'typeName',
      align: 'center',
      width: 260,
    },
    {
      title: '请求参数',
      dataIndex: 'failRequest',
      slotName: 'failRequest',
      ellipsis: true, // 文本超出省略
      tooltip: true, // 显示省略号时显示文本提示
      width: 260,
    },
    {
      title: '请求状态',
      dataIndex: 'status',
      slotName: 'status',
      align: 'center',
      width: 150,
    },
    {
      title: '响应数据',
      dataIndex: 'responseParam',
      slotName: 'responseParam',
      ellipsis: true, // 文本超出省略
      tooltip: true, // 显示省略号时显示文本提示
      width: 260,
    },
    {
      title: '消耗套餐',
      dataIndex: 'remark',
      slotName: 'remark',
      width: 260,
    },
    {
      title: '创建时间',
      dataIndex: 'createDate',
      width: 200,
    },
  ];
</script>
<style lang="scss" scoped></style>
