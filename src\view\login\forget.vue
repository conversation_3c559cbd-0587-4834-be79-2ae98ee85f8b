<template>
  <div class="login-wrapper bg-white" :style="{ backgroundImage: `url(${loginBg})` }">
    <div class="login-container">
      <div class="text-4xl font-semibold">找回账号</div>
      <div class="text-4xl font-semibold mt-1">文创链开放平台</div>
      <div class="mt-6 mb-10">
        <a-space>
          <span class="text-gray-500">没有账号吗?</span>
          <span class="cursor-pointer" @click="pageJump('register')">注册新账号</span>
        </a-space>
      </div>
      <a-form ref="formRef" size="large" :model="form" @submit-success="handleSubmit">
        <a-form-item
          field="mobile"
          :hide-label="true"
          :hide-asterisk="true"
          :validate-trigger="['change', 'input']"
        >
          <a-input
            v-model="form.mobile"
            :style="{ width: '320px' }"
            placeholder="请输入手机号"
            allow-clear
          >
            <template #prepend> +86 </template>
          </a-input>
        </a-form-item>
        <a-form-item
          field="code"
          :hide-label="true"
          :hide-asterisk="true"
          :rules="[{ required: true, message: '必填' }]"
          :validate-trigger="['change', 'input']"
        >
          <a-input
            v-model="form.code"
            :style="{ width: '220px' }"
            placeholder="请输入图形验证码"
            allow-clear
          />
          <a-image class="pl-2" width="100" :src="code.src" :preview="false" @click="refreshCode" />
        </a-form-item>
        <a-form-item
          field="smsCode"
          :hide-label="true"
          :hide-asterisk="true"
          :rules="[
            { required: true, message: '必填' },
            { match: /^\d{6}$/, message: '请输入六位数字验证码！' },
          ]"
          :validate-trigger="['change', 'input']"
        >
          <a-input
            v-model="form.smsCode"
            :style="{ width: '220px' }"
            placeholder="请输入手机验证码"
            allow-clear
          />
          <a-button v-if="countdown == 60" type="primary" @click="sendSmsCode">
            发送验证码
          </a-button>
          <a-button v-else type="primary">
            {{ countdown }}
          </a-button>
        </a-form-item>
        <a-form-item
          field="password"
          :hide-label="true"
          :hide-asterisk="true"
          :rules="[
            { required: true, message: '必填' },
            {
              match:
                /^(?![0-9]+$)(?![a-zA-Z]+$)(?![0-9a-zA-Z]+$)(?![0-9\W]+$)(?![a-zA-Z\W]+$)[0-9A-Za-z\W]{8,16}$/,
              message: '密码至少8位字符，且必须同时包含数字、大小写字母和除空格外的特殊字符。',
            },
          ]"
          :validate-trigger="['change', 'input']"
        >
          <a-input-password
            v-model="form.password"
            :style="{ width: '320px' }"
            placeholder="请输入密码"
            allow-clear
          />
        </a-form-item>
        <a-form-item
          field="passwordAgain"
          :hide-label="true"
          :hide-asterisk="true"
          :rules="[
            { required: true, message: '必填' },
            {
              match:
                /^(?![0-9]+$)(?![a-zA-Z]+$)(?![0-9a-zA-Z]+$)(?![0-9\W]+$)(?![a-zA-Z\W]+$)[0-9A-Za-z\W]{8,16}$/,
              message: '密码至少8位字符，且必须同时包含数字、大小写字母和除空格外的特殊字符。',
            },
          ]"
          :validate-trigger="['change', 'input']"
        >
          <a-input-password
            v-model="form.passwordAgain"
            :style="{ width: '320px' }"
            placeholder="请再次输入密码"
            allow-clear
          />
        </a-form-item>
        <a-form-item :hide-label="true" :hide-asterisk="true">
          <a-button type="primary" long html-type="submit"> 确 定 </a-button>
        </a-form-item>
      </a-form>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { getAssetsFile } from '@/utils/tool';
  import { log } from 'console';
  import { ref, reactive } from 'vue';
  import { Message } from '@arco-design/web-vue';
  import Encrypt from 'encryptlong';
  import { memberSendResetPasswordSmsCode, resetMemberPassword } from '@/http/api/login/index';
  import { getPublicKey } from '@/http/api/global/index';
  import { useRouter } from 'vue-router';
  // import Footer from "@/components/footer/index.vue";
  // import LoginBanner from "./components/banner.vue";
  // import LoginForm from "./components/login-form.vue";
  let imgUrl = getAssetsFile('logo-3.png');
  let loginBg = getAssetsFile('login-bg.png');
  const router = useRouter();
  // 登录框表单绑定
  const form = reactive({
    password: '',
    passwordAgain: '',
    smsCode: '',
    mobile: '',
    code: '',
    randomStr: '',
    loginWithCode: true,
    publicKey: '',
  });
  // 图形验证码
  const code = reactive({
    src: '/api/code',
    value: '',
    len: 4,
    type: 'image',
  });
  const countdown = ref(60); // 验证码倒计时
  // 发送验证码
  const sendSmsCode = () => {
    if (!form.mobile) {
      Message.error('请输入手机号');
      return;
    }
    if (!form.code) {
      Message.error('请输入图形验证码');
      return;
    }
    let params = {
      mobile: form.mobile,
      check: 1,
      randomStr: form.randomStr,
      code: form.code,
      loginWithCode: true,
    };
    memberSendResetPasswordSmsCode({
      mobile: form.mobile,
    })
      .then((res: any) => {
        console.log(res);
        if (res == true || res.code == 0) {
          // 提示成功
          Message.success('发送成功！注意查收');
          setTime();
        } else {
          // 提示报错
          Message.error(res.msg || '发送失败，请重试');
          refreshCode();
        }
      })
      .catch((error: any) => {
        Message.error(error.msg || '发送失败，请重试');
        refreshCode();
      });
  };
  // 提交方法
  const handleSubmit = (val: object) => {
    let params = {
      mobile: form.mobile,
      password: form.password,
      smsCode: form.smsCode,
    };

    // 使用会员重置密码接口
    resetMemberPassword(params)
      .then((data: any) => {
        if (data == true || data.code == 0) {
          Message.success('密码重置成功！');
          router.replace('/login');
        } else {
          Message.error(data.msg || '重置失败，请重试');
          refreshCode();
        }
      })
      .catch((error: any) => {
        Message.error(error.msg || '重置失败，请重试');
        refreshCode();
      });
  };
  // 倒计时
  const setTime = () => {
    if (countdown.value == 1) {
      countdown.value = 60;
      return;
    } else {
      countdown.value--;
    }
    setTimeout(() => {
      setTime();
    }, 1000);
  };
  // 生成随机数
  const randomLenNum = (len: number, date: boolean = true) => {
    let random = '';
    random = Math.ceil(Math.random() * 100000000000000)
      .toString()
      .substr(0, len || 4);
    if (date) random = random + Date.now();
    return random;
  };
  const pageJump = (d: string) => {
    router.replace('/' + d);
  };
  // 刷新图形验证码
  const refreshCode = () => {
    form.code = '';
    form.randomStr = randomLenNum(code.len, true);
    code.type === 'text'
      ? (code.value = randomLenNum(code.len))
      : (code.src = `/api/code?randomStr=${form.randomStr}`);
  };
  refreshCode();
</script>

<style lang="scss" scoped>
  .login-wrapper {
    height: 100vh;
    display: flex;
    flex-direction: column;
    background-size: cover;
    background-position: 100%;
    position: relative;
  }

  .login-container {
    position: absolute;
    top: 22%;
    left: 6%;
    min-height: 500px;
  }
</style>
