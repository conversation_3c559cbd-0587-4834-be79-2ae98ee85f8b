<template>
  <div class="page-container">
    <div class="page-header">
      <h1 class="page-title">作品著作权存证</h1>
      <p class="page-description">通过区块链技术为您的作品提供可信存证服务，保障作品创作权益</p>
    </div>

    <div class="content-wrapper">
      <a-steps :current="currentStep" class="mb-8">
        <a-step title="填写基本信息" />
        <a-step title="上传作品材料" />
        <a-step title="提交存证申请" />
        <a-step title="完成" />
      </a-steps>

      <div class="form-container">
        <div v-if="currentStep === 0">
          <h2 class="form-section-title">作品基本信息</h2>
          <a-form :model="formData" layout="vertical">
            <a-form-item field="worksTitle" label="作品名称" required>
              <a-input v-model="formData.worksTitle" placeholder="请输入作品名称" />
            </a-form-item>
            <a-form-item field="worksType" label="作品类型" required>
              <a-select v-model="formData.worksType" placeholder="请选择作品类型">
                <a-option value="literary">文字作品</a-option>
                <a-option value="music">音乐作品</a-option>
                <a-option value="art">美术作品</a-option>
                <a-option value="photography">摄影作品</a-option>
                <a-option value="film">影视作品</a-option>
                <a-option value="graphic">图形作品</a-option>
                <a-option value="architecture">建筑作品</a-option>
                <a-option value="other">其他作品</a-option>
              </a-select>
            </a-form-item>
            <a-form-item field="authorName" label="作者姓名" required>
              <a-input v-model="formData.authorName" placeholder="请输入作者姓名" />
            </a-form-item>
            <a-form-item field="creationDate" label="创作完成日期" required>
              <a-date-picker v-model="formData.creationDate" placeholder="请选择创作完成日期" />
            </a-form-item>
            <a-form-item field="worksDescription" label="作品简介" required>
              <a-textarea
                v-model="formData.worksDescription"
                placeholder="请简要描述作品的主要内容和特点"
                :max-length="500"
                show-word-limit
              />
            </a-form-item>
          </a-form>
          <div class="step-actions">
            <a-button type="primary" @click="nextStep"> 下一步 </a-button>
          </div>
        </div>

        <div v-if="currentStep === 1">
          <h2 class="form-section-title">上传作品材料</h2>
          <p class="upload-description">请上传作品文件进行区块链存证：</p>

          <a-form :model="formData" layout="vertical">
            <a-form-item field="evidenceFiles" label="作品文件" required>
              <a-upload
                action="/"
                :custom-request="handleFileUpload"
                :file-list="formData.evidenceFiles"
                accept=".pdf,.doc,.docx,.jpg,.jpeg,.png,.mp3,.mp4,.avi,.txt"
                multiple
                @change="handleEvidenceFilesChange"
              >
                <template #upload-button>
                  <a-button>
                    <template #icon>
                      <icon-upload />
                    </template>
                    上传作品文件
                  </a-button>
                </template>
                <template #extra>
                  <div class="upload-tip">
                    支持多种格式文件，单个文件大小不超过100MB，可上传多个文件
                  </div>
                </template>
              </a-upload>
            </a-form-item>

            <a-form-item field="additionalInfo" label="补充说明">
              <a-textarea
                v-model="formData.additionalInfo"
                placeholder="如有需要，请提供作品的补充说明信息"
                :max-length="300"
                show-word-limit
              />
            </a-form-item>
          </a-form>

          <div class="step-actions">
            <a-button @click="prevStep"> 上一步 </a-button>
            <a-button type="primary" :disabled="!canProceedToStep2" @click="nextStep">
              下一步
            </a-button>
          </div>
        </div>

        <div v-if="currentStep === 2">
          <h2 class="form-section-title">确认存证信息</h2>

          <div class="confirmation-section">
            <h3>作品信息</h3>
            <div class="info-grid">
              <div class="info-item">
                <span class="label">作品名称：</span>
                <span class="value">{{ formData.worksTitle }}</span>
              </div>
              <div class="info-item">
                <span class="label">作品类型：</span>
                <span class="value">{{ getWorksTypeText(formData.worksType) }}</span>
              </div>
              <div class="info-item">
                <span class="label">作者姓名：</span>
                <span class="value">{{ formData.authorName }}</span>
              </div>
              <div class="info-item">
                <span class="label">创作日期：</span>
                <span class="value">{{ formData.creationDate }}</span>
              </div>
              <div class="info-item full-width">
                <span class="label">作品简介：</span>
                <span class="value">{{ formData.worksDescription }}</span>
              </div>
            </div>
          </div>

          <div class="confirmation-section">
            <h3>上传文件</h3>
            <div class="file-list">
              <div v-for="file in formData.evidenceFiles" :key="file.uid" class="file-item">
                <icon-file />
                <span>{{ file.name }}</span>
                <span class="file-size">({{ formatFileSize(file.size) }})</span>
              </div>
            </div>
          </div>

          <div class="blockchain-info">
            <a-alert type="info">
              <template #message>
                <div class="blockchain-notice">
                  <h4>区块链存证说明</h4>
                  <ul>
                    <li>存证数据将通过区块链技术进行加密存储，确保数据不可篡改</li>
                    <li>存证完成后将生成唯一的区块链哈希值作为存证凭证</li>
                    <li>存证记录具有法律效力，可作为版权保护的重要证据</li>
                    <li>存证费用：{{ evidenceFee }}元/次</li>
                  </ul>
                </div>
              </template>
            </a-alert>
          </div>

          <div class="step-actions">
            <a-button @click="prevStep"> 上一步 </a-button>
            <a-button type="primary" :loading="submitting" @click="submitEvidence">
              确认存证
            </a-button>
          </div>
        </div>

        <div v-if="currentStep === 3">
          <div class="success-container">
            <icon-check-circle class="success-icon" />
            <h2 class="success-title">作品存证成功！</h2>
            <p class="success-message">
              您的作品已成功上链存证，区块链技术确保了作品的不可篡改性和时间戳证明。
            </p>
            <div class="success-info">
              <p>
                <span class="label">存证编号：</span><span class="value">{{ evidenceId }}</span>
              </p>
              <p>
                <span class="label">区块链哈希：</span
                ><span class="value">{{ blockchainHash }}</span>
              </p>
              <p>
                <span class="label">存证时间：</span><span class="value">{{ evidenceTime }}</span>
              </p>
              <p>
                <span class="label">区块高度：</span><span class="value">{{ blockHeight }}</span>
              </p>
            </div>
            <a-alert type="success" class="mt-4">
              <template #message>
                存证凭证已生成，您可以在"个人中心-存证记录"中查看和下载存证证书。存证记录具有法律效力，可作为版权保护的重要证据。
              </template>
            </a-alert>
            <div class="success-actions">
              <a-button type="primary" @click="goToEvidenceList"> 查看存证记录 </a-button>
              <a-button @click="downloadCertificate"> 下载存证证书 </a-button>
              <a-button @click="goToHome"> 返回首页 </a-button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref, computed } from 'vue';
  import { useRouter } from 'vue-router';
  import { Message } from '@arco-design/web-vue';
  import { IconUpload, IconCheckCircle, IconFile } from '@arco-design/web-vue/es/icon';
  import { createWorksEvidence } from '@/http/api/works';

  const router = useRouter();

  // 当前步骤
  const currentStep = ref(0);

  // 表单数据
  const formData = ref({
    worksTitle: '',
    worksType: '',
    authorName: '',
    creationDate: '',
    worksDescription: '',
    evidenceFiles: [] as any[],
    additionalInfo: '',
  });

  // 存证相关数据
  const submitting = ref(false);
  const evidenceFee = ref(50);
  const evidenceId = ref('');
  const blockchainHash = ref('');
  const evidenceTime = ref('');
  const blockHeight = ref('');

  // 计算属性
  const canProceedToStep2 = computed(() => {
    return formData.value.evidenceFiles.length > 0;
  });

  // 步骤控制方法
  const nextStep = () => {
    if (currentStep.value < 3) {
      currentStep.value++;
    }
  };

  const prevStep = () => {
    if (currentStep.value > 0) {
      currentStep.value--;
    }
  };

  // 文件上传处理
  const handleFileUpload = (option: any) => {
    // 这里应该实现实际的文件上传逻辑
    // 暂时模拟上传成功
    const timer = setTimeout(() => {
      option.onSuccess();
    }, 1000);

    // 返回包含 abort 方法的对象
    return {
      abort: () => {
        clearTimeout(timer);
      },
    };
  };

  const handleEvidenceFilesChange = (fileList: any[]) => {
    formData.value.evidenceFiles = fileList;
  };

  // 工具方法
  const getWorksTypeText = (type: string) => {
    const typeMap: Record<string, string> = {
      literary: '文字作品',
      music: '音乐作品',
      art: '美术作品',
      photography: '摄影作品',
      film: '影视作品',
      graphic: '图形作品',
      architecture: '建筑作品',
      other: '其他作品',
    };
    return typeMap[type] || type;
  };

  const formatFileSize = (size: number) => {
    if (size < 1024) return size + ' B';
    if (size < 1024 * 1024) return (size / 1024).toFixed(1) + ' KB';
    return (size / (1024 * 1024)).toFixed(1) + ' MB';
  };

  // 提交存证
  const submitEvidence = async () => {
    submitting.value = true;
    try {
      // 构建提交数据
      const submitData = {
        worksTitle: formData.value.worksTitle,
        worksType: formData.value.worksType,
        authorName: formData.value.authorName,
        worksDescription: formData.value.worksDescription,
        evidenceFiles: formData.value.evidenceFiles.map(file => file.name),
      };

      // 调用API提交存证
      const response = await createWorksEvidence(submitData);

      // 设置成功信息（模拟区块链数据）
      evidenceId.value = `EV${Date.now()}`;
      blockchainHash.value = `0x${Math.random().toString(16).substr(2, 64)}`;
      evidenceTime.value = new Date().toLocaleString();
      blockHeight.value = Math.floor(Math.random() * 1000000 + 500000).toString();

      // 跳转到成功页面
      nextStep();

      Message.success('作品存证成功！');
    } catch (error) {
      Message.error('存证失败，请重试');
      console.error('存证失败:', error);
    } finally {
      submitting.value = false;
    }
  };

  // 页面跳转方法
  const goToEvidenceList = () => {
    router.push('/user/evidence');
  };

  const downloadCertificate = () => {
    // 这里应该实现下载存证证书的逻辑
    Message.info('存证证书下载功能开发中...');
  };

  const goToHome = () => {
    router.push('/works/page');
  };
</script>

<style lang="scss" scoped>
  .page-container {
    width: 100%;
    min-height: 100vh;
    background-color: #f7f8fa;
    padding-bottom: 40px;
  }

  .page-header {
    background: linear-gradient(135deg, #165dff 0%, #4080ff 100%);
    color: white;
    padding: 40px 0;
    text-align: center;

    .page-title {
      font-size: 32px;
      font-weight: 600;
      margin-bottom: 12px;
    }

    .page-description {
      font-size: 16px;
      opacity: 0.9;
      margin: 0;
    }
  }

  .content-wrapper {
    max-width: 800px;
    margin: 0 auto;
    padding: 40px 24px;
  }

  .form-container {
    background: white;
    border-radius: 12px;
    padding: 32px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  }

  .form-section-title {
    font-size: 20px;
    font-weight: 600;
    color: #1d2129;
    margin-bottom: 24px;
    padding-bottom: 12px;
    border-bottom: 2px solid #e5e6eb;
  }

  .upload-description {
    color: #4e5969;
    margin-bottom: 24px;
    line-height: 1.6;
  }

  .upload-tip {
    color: #86909c;
    font-size: 12px;
    margin-top: 4px;
  }

  .step-actions {
    display: flex;
    justify-content: center;
    gap: 16px;
    margin-top: 32px;
    padding-top: 24px;
    border-top: 1px solid #e5e6eb;

    .arco-btn {
      min-width: 120px;
    }
  }

  .confirmation-section {
    margin-bottom: 32px;

    h3 {
      font-size: 16px;
      font-weight: 600;
      color: #1d2129;
      margin-bottom: 16px;
      padding-bottom: 8px;
      border-bottom: 1px solid #e5e6eb;
    }

    .info-grid {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 16px;

      .info-item {
        display: flex;
        align-items: flex-start;

        &.full-width {
          grid-column: 1 / -1;
          flex-direction: column;
          gap: 8px;
        }

        .label {
          color: #86909c;
          width: 80px;
          flex-shrink: 0;
          font-size: 14px;
        }

        .value {
          color: #1d2129;
          font-weight: 500;
          flex: 1;
        }
      }
    }

    .file-list {
      .file-item {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 12px;
        background: #f7f8fa;
        border-radius: 6px;
        margin-bottom: 8px;

        .arco-icon {
          color: #165dff;
          font-size: 16px;
        }

        .file-size {
          color: #86909c;
          font-size: 12px;
          margin-left: auto;
        }
      }
    }
  }

  .blockchain-info {
    margin-bottom: 32px;

    .blockchain-notice {
      h4 {
        font-size: 14px;
        font-weight: 600;
        color: #1d2129;
        margin-bottom: 12px;
      }

      ul {
        margin: 0;
        padding-left: 20px;
        color: #4e5969;
        line-height: 1.6;

        li {
          margin-bottom: 8px;

          &:last-child {
            margin-bottom: 0;
            font-weight: 500;
            color: #165dff;
          }
        }
      }
    }
  }

  .success-container {
    text-align: center;
    padding: 40px 0;

    .success-icon {
      font-size: 64px;
      color: #00b42a;
      margin-bottom: 24px;
    }

    .success-title {
      font-size: 24px;
      font-weight: 600;
      color: #1d2129;
      margin-bottom: 16px;
    }

    .success-message {
      font-size: 16px;
      color: #4e5969;
      margin-bottom: 32px;
      line-height: 1.6;
    }

    .success-info {
      background: #f7f8fa;
      border-radius: 8px;
      padding: 24px;
      margin-bottom: 24px;
      text-align: left;

      p {
        margin-bottom: 12px;
        display: flex;
        align-items: flex-start;
        gap: 8px;

        &:last-child {
          margin-bottom: 0;
        }

        .label {
          color: #86909c;
          width: 100px;
          flex-shrink: 0;
          font-size: 14px;
        }

        .value {
          color: #1d2129;
          font-weight: 500;
          font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
          word-break: break-all;
          flex: 1;
        }
      }
    }

    .success-actions {
      display: flex;
      justify-content: center;
      gap: 16px;
      margin-top: 32px;
      flex-wrap: wrap;

      .arco-btn {
        min-width: 140px;
      }
    }
  }

  :deep(.arco-form-item-label) {
    font-weight: 500;
    color: #1d2129;
  }

  :deep(.arco-steps) {
    margin-bottom: 40px;
  }

  :deep(.arco-upload-list-item) {
    border-radius: 6px;
  }

  :deep(.arco-upload-list-item-error) {
    border-color: #f53f3f;
    background-color: #ffece8;
  }

  :deep(.arco-upload-list-item-done) {
    border-color: #00b42a;
    background-color: #e8ffea;
  }

  @media (max-width: 768px) {
    .content-wrapper {
      padding: 24px 16px;
    }

    .form-container {
      padding: 24px 16px;
    }

    .page-header {
      padding: 32px 16px;

      .page-title {
        font-size: 24px;
      }

      .page-description {
        font-size: 14px;
      }
    }

    .step-actions {
      flex-direction: column;

      .arco-btn {
        width: 100%;
      }
    }

    .success-actions {
      flex-direction: column;

      .arco-btn {
        width: 100%;
      }
    }

    .confirmation-section .info-grid {
      grid-template-columns: 1fr;
    }
  }
</style>
