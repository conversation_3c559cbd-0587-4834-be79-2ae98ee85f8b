/** @format */

import * as FileApi from '@/http/api/global/proIndex';

/**
 * 调试文件上传功能
 */
export const debugUpload = () => {
  console.log('=== 文件上传调试信息 ===');

  // 检查环境变量
  console.log('环境变量:');
  console.log('- VITE_UPLOAD_TYPE:', import.meta.env.VITE_UPLOAD_TYPE);
  console.log('- VITE_PRO_API_TARGET:', import.meta.env.VITE_PRO_API_TARGET);

  // 检查上传模式判断
  const isClientUpload = 'client' === import.meta.env.VITE_UPLOAD_TYPE;
  console.log('- 是否客户端上传:', isClientUpload);
  console.log(
    '- 实际上传地址:',
    import.meta.env.VITE_PRO_API_TARGET + '/app-api/infra/file/upload'
  );

  // 检查API函数
  console.log('API函数:');
  console.log('- getFilePresignedUrl:', typeof FileApi.getFilePresignedUrl);
  console.log('- createFile:', typeof FileApi.createFile);
  console.log('- uploadFile:', typeof FileApi.uploadFile);

  // 检查 FileApi.uploadFile 的实际实现
  console.log('- FileApi.uploadFile 函数:', FileApi.uploadFile.toString());

  // 测试创建一个小文件
  const testFile = new File(['Hello World'], 'test.txt', { type: 'text/plain' });
  console.log('测试文件:', testFile);

  return {
    testFile,
    FileApi,
    isClientUpload,
    uploadUrl: import.meta.env.VITE_PRO_API_TARGET + '/app-api/infra/file/upload',
  };
};

/**
 * 测试后端上传
 */
export const testServerUpload = async () => {
  console.log('=== 测试后端上传 ===');

  try {
    const testFile = new File(['Hello World Test'], 'test.txt', { type: 'text/plain' });
    console.log('开始上传文件:', testFile);

    const result = await FileApi.uploadFile(testFile);
    console.log('上传结果:', result);

    return result;
  } catch (error) {
    console.error('上传失败:', error);
    throw error;
  }
};

/**
 * 测试前端直连上传
 */
export const testClientUpload = async () => {
  console.log('=== 测试前端直连上传 ===');

  try {
    // 1. 生成文件名
    const testFile = new File(['Hello World Test'], 'test.txt', { type: 'text/plain' });
    const fileName = `test_${Date.now()}.txt`;
    console.log('文件名:', fileName);

    // 2. 获取预签名URL
    console.log('获取预签名URL...');
    const presignedInfo = await FileApi.getFilePresignedUrl(fileName);
    console.log('预签名信息:', presignedInfo);

    return presignedInfo;
  } catch (error) {
    console.error('获取预签名URL失败:', error);
    throw error;
  }
};

// 将调试函数挂载到全局对象，方便在浏览器控制台调用
if (typeof window !== 'undefined') {
  (window as any).uploadDebug = {
    debugUpload,
    testServerUpload,
    testClientUpload,
    FileApi,
  };
}
