import { defineStore } from 'pinia';

export const useUserStore = defineStore('user', {
	persist: true, // 开启持久化存储
	state: () => ({
		user_info: null,
		user_token: null,
	}),
	getters: {
		getToken(state) {
			return state.user_token
		},
		getUserInfo(state) {
			return state.user_info
		}
	},
	actions: {
		setToken(userToken) {
			this.user_token = userToken
		},
		setUserInfo(userInfo) {
			this.user_info = userInfo
		},
		userOutLogin() {
			this.user_info = null;
			this.user_token = null;
			return true;
		}
	},
});
