import { defineStore } from 'pinia';

export const useUserStore = defineStore('user', {
  persist: true, // 开启持久化存储
  state: () => ({
    user_info: null,
    access_token: null,
    refresh_token: null,
    expires_time: null,
  }),
  getters: {
    // 兼容旧版本的getToken方法
    getToken(state) {
      return state.access_token;
    },
    getAccessToken(state) {
      return state.access_token;
    },
    getRefreshToken(state) {
      return state.refresh_token;
    },
    getExpiresTime(state) {
      return state.expires_time;
    },
    getUserInfo(state) {
      return state.user_info;
    },
    // 检查token是否即将过期（提前5分钟）
    isTokenExpiringSoon(state) {
      if (!state.expires_time) return false;
      const now = new Date().getTime();
      const expiresTime = new Date(state.expires_time).getTime();
      const fiveMinutes = 5 * 60 * 1000; // 5分钟
      return expiresTime - now <= fiveMinutes;
    },
    // 检查token是否已过期
    isTokenExpired(state) {
      if (!state.expires_time) return false;
      const now = new Date().getTime();
      const expiresTime = new Date(state.expires_time).getTime();
      return now >= expiresTime;
    },
  },
  actions: {
    // 兼容旧版本的setToken方法
    setToken(userToken) {
      this.access_token = userToken;
    },
    // 设置完整的token信息
    setTokenInfo(tokenInfo) {
      this.access_token = tokenInfo.accessToken || tokenInfo.access_token;
      this.refresh_token = tokenInfo.refreshToken || tokenInfo.refresh_token;
      this.expires_time = tokenInfo.expiresTime || tokenInfo.expires_time;
    },
    // 设置访问令牌
    setAccessToken(accessToken) {
      this.access_token = accessToken;
    },
    // 设置刷新令牌
    setRefreshToken(refreshToken) {
      this.refresh_token = refreshToken;
    },
    // 设置过期时间
    setExpiresTime(expiresTime) {
      this.expires_time = expiresTime;
    },
    setUserInfo(userInfo) {
      this.user_info = userInfo;
    },
    userOutLogin() {
      this.user_info = null;
      this.access_token = null;
      this.refresh_token = null;
      this.expires_time = null;
      return true;
    },
  },
});
