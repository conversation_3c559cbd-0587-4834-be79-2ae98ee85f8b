<template>
  <div class="p-4 bg-grey">
    <a-page-header
      :show-back="false"
      :style="{ background: 'var(--color-bg-2)' }"
      title="套餐购买"
      :subtitle="'共' + order.total + '款产品'"
    />
    <div class="py-4">
      <a-space wrap class="justify-between">
        <a-card
          v-for="(item, index) in order.list"
          :key="index"
          :style="{ width: '314px' }"
          :title="item.name"
        >
          <template #extra>
            <a-link @click="openBuyModel(item)"> 购买套餐 </a-link>
          </template>
          <a-descriptions :column="1" :data="data" bordered>
            <a-descriptions-item label="套餐用量">
              {{ item.num }}
            </a-descriptions-item>
            <a-descriptions-item label="套餐原价">
              <div style="text-decoration: line-through">
                {{ item.originalPrice }}
              </div>
            </a-descriptions-item>
            <a-descriptions-item label="套餐价格">
              {{ item.price }}
            </a-descriptions-item>
          </a-descriptions>
          <template #actions>
            <a-link @click="openModel(item)"> 套餐详情 </a-link>
          </template>
        </a-card>
      </a-space>
    </div>
    <a-modal v-model:visible="aModalType" :hide-cancel="true" fullscreen @ok="aModalType = false">
      <template #title> 套餐详情 </template>
      <div>
        <a-descriptions :column="1" :data="data" bordered>
          <a-descriptions-item
            v-for="item in orderItem"
            :key="item.interface_id || item.interface_name"
            :label="item.remark"
          >
            {{ item.interface_name }}
          </a-descriptions-item>
        </a-descriptions>
      </div>
    </a-modal>
    <a-modal v-model:visible="aModalBuyType" ok-text="确认购买" @ok="buySubmit">
      <template #title> 确认套餐 </template>
      <div>
        <a-descriptions :column="1" :data="data" bordered>
          <a-descriptions-item label="套餐名称">
            {{ buyItem.name }}
          </a-descriptions-item>
          <a-descriptions-item label="套餐用量">
            {{ buyItem.num }}
          </a-descriptions-item>
          <a-descriptions-item label="套餐原价">
            <div style="text-decoration: line-through">
              {{ buyItem.originalPrice }}
            </div>
          </a-descriptions-item>
          <a-descriptions-item label="套餐价格">
            {{ buyItem.price }}
          </a-descriptions-item>
          <a-descriptions-item label="购买数量">
            <a-input-number
              v-model="buyValue"
              :style="{ width: '320px' }"
              placeholder="购买数量"
              mode="button"
              size="large"
              class="input-demo"
            />
          </a-descriptions-item>
        </a-descriptions>
        <!-- <a-button type="primary">Primary</a-button> -->
      </div>
    </a-modal>
  </div>
</template>

<script lang="ts" setup>
  import { getAssetsFile } from '@/utils/tool';
  import { ref, reactive } from 'vue';
  import { getOrderList, getItemInfo, buyMeal } from '@/http/api/user/setmeal';
  import { useRouter } from 'vue-router';
  const router = useRouter();

  const searchData = reactive({
    pageIndex: 1,
    pageSize: 99,
    name: null,
  });
  const aModalType = ref(false);
  const aModalBuyType = ref(false);
  const order = ref<any>({
    list: [],
  });
  const orderItem = ref<any>({});
  const buyItem = ref<any>({});
  const buyValue = ref<any>(1);

  const data = ref([
    {
      label: '套餐用量',
      value: 'Socrates',
    },
    {
      label: '套餐原价',
      value: '123-1234-1234',
    },
    {
      label: '套餐价格',
      value: 'Beijing',
    },
  ]);

  const buySubmit = async () => {
    // let formData = new FormData();
    // formData.append("buyNum", buyValue.value)
    // formData.append("id", buyItem.value.id)
    // formData.append("returnUrl", window.location.href.split('/meal')[0] + '/meal/orderPageInfo')
    let buyUrl: any = await buyMeal({
      buyNum: buyValue.value,
      id: buyItem.value.id,
      returnUrl: window.location.href.split('/user')[0] + '/user/packagePurchase',
    });
    window.location.href = buyUrl;
  };

  const openModel = async (item: any) => {
    orderItem.value = await getItemInfo({ setMealId: item.id });
    aModalType.value = true;
    console.log(orderItem.value);
  };
  const openBuyModel = async (item: any) => {
    buyValue.value = 1;
    buyItem.value = item;
    aModalBuyType.value = true;
  };
  const pageInte = async () => {
    order.value = await getOrderList({ ...searchData });
    console.log(order.value);
  };
  pageInte();
</script>
<style lang="scss" scoped>
  .icon-hover {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    transition: all 0.1s;
  }

  .icon-hover:hover {
    background-color: rgb(var(--gray-2));
  }
</style>
