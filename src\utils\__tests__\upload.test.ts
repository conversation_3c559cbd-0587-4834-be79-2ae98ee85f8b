/** @format */

import { describe, it, expect } from 'vitest';

// 直接导入工具函数，避免导入依赖 Pinia 的模块
const formatFileSize = (size: number): string => {
  if (size === 0) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(size) / Math.log(k));
  return parseFloat((size / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

const validateFileType = (file: File, acceptTypes: string[]): boolean => {
  if (!acceptTypes || acceptTypes.length === 0) return true;

  const fileExtension = file.name.substring(file.name.lastIndexOf('.')).toLowerCase();
  const mimeType = file.type.toLowerCase();

  return acceptTypes.some(type => {
    if (type.startsWith('.')) {
      return fileExtension === type.toLowerCase();
    } else if (type.includes('/')) {
      return mimeType === type.toLowerCase();
    }
    return false;
  });
};

const validateFileSize = (file: File, maxSize: number): boolean => {
  return file.size <= maxSize;
};

describe('Upload Utils', () => {
  describe('formatFileSize', () => {
    it('should format bytes correctly', () => {
      expect(formatFileSize(0)).toBe('0 B');
      expect(formatFileSize(1024)).toBe('1 KB');
      expect(formatFileSize(1024 * 1024)).toBe('1 MB');
      expect(formatFileSize(1024 * 1024 * 1024)).toBe('1 GB');
      expect(formatFileSize(1536)).toBe('1.5 KB'); // 1.5KB
    });
  });

  describe('validateFileType', () => {
    it('should validate file extensions correctly', () => {
      const file = new File(['test'], 'test.pdf', { type: 'application/pdf' });
      const acceptTypes = ['.pdf', '.doc', '.docx'];

      expect(validateFileType(file, acceptTypes)).toBe(true);
    });

    it('should validate MIME types correctly', () => {
      const file = new File(['test'], 'test.pdf', { type: 'application/pdf' });
      const acceptTypes = ['application/pdf', 'image/jpeg'];

      expect(validateFileType(file, acceptTypes)).toBe(true);
    });

    it('should reject invalid file types', () => {
      const file = new File(['test'], 'test.exe', { type: 'application/x-executable' });
      const acceptTypes = ['.pdf', '.doc', '.docx'];

      expect(validateFileType(file, acceptTypes)).toBe(false);
    });

    it('should return true for empty accept types', () => {
      const file = new File(['test'], 'test.exe', { type: 'application/x-executable' });
      const acceptTypes: string[] = [];

      expect(validateFileType(file, acceptTypes)).toBe(true);
    });
  });

  describe('validateFileSize', () => {
    it('should validate file size correctly', () => {
      const smallFile = new File(['test'], 'small.txt', { type: 'text/plain' });
      const maxSize = 1024 * 1024; // 1MB

      expect(validateFileSize(smallFile, maxSize)).toBe(true);
    });

    it('should reject oversized files', () => {
      // 创建一个大文件（模拟）
      const largeFile = new File(['x'.repeat(2 * 1024 * 1024)], 'large.txt', {
        type: 'text/plain',
      });
      const maxSize = 1024 * 1024; // 1MB

      expect(validateFileSize(largeFile, maxSize)).toBe(false);
    });
  });
});
