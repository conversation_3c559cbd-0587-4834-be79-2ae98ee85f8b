<template>
  <div class="balance-payment-container">
    <div
      class="payment-method"
      :class="{
        selected: isSelected,
        disabled: !method.enabled || userBalance <= 0,
      }"
    >
      <div class="method-header" @click="handleToggle">
        <div class="method-content">
          <div class="method-icon">
            <icon-user class="icon" />
          </div>

          <div class="method-info">
            <div class="method-name">{{ method.name }}</div>
            <div class="method-description">可用余额：¥{{ formatAmount(userBalance) }}</div>
          </div>

          <div class="method-status">
            <a-switch
              :model-value="isSelected"
              :disabled="!method.enabled || userBalance <= 0"
              size="small"
              @change="handleToggle"
            />
          </div>
        </div>
      </div>

      <!-- 金额输入区域 -->
      <div v-if="isSelected" class="amount-input-section">
        <div class="amount-controls">
          <div class="input-group">
            <label class="input-label">使用金额</label>
            <div class="input-wrapper">
              <a-input-number
                :model-value="amount"
                :min="0"
                :max="maxAmount"
                :precision="2"
                :step="1"
                placeholder="请输入使用金额"
                class="amount-input"
                @update:model-value="handleAmountChange"
              >
                <template #prefix>¥</template>
              </a-input-number>
              <a-button type="text" size="small" class="max-button" @click="useMaxAmount">
                全部
              </a-button>
            </div>
          </div>

          <div class="amount-slider">
            <a-slider
              :model-value="sliderValue"
              :max="100"
              :step="1"
              :show-tooltip="false"
              @update:model-value="handleSliderChange"
            />
            <div class="slider-labels">
              <span>¥0</span>
              <span>¥{{ formatAmount(maxAmount) }}</span>
            </div>
          </div>
        </div>

        <!-- 余额信息 -->
        <div class="balance-info">
          <div class="info-row">
            <span class="info-label">当前余额：</span>
            <span class="info-value">¥{{ formatAmount(userBalance) }}</span>
          </div>
          <div class="info-row">
            <span class="info-label">使用金额：</span>
            <span class="info-value highlight">¥{{ formatAmount(amount) }}</span>
          </div>
          <div class="info-row">
            <span class="info-label">剩余余额：</span>
            <span class="info-value">¥{{ formatAmount(userBalance - amount) }}</span>
          </div>
        </div>

        <!-- 余额不足提示 -->
        <div v-if="amount > userBalance" class="insufficient-warning">
          <a-alert type="warning" size="small" :show-icon="false">
            <template #icon>
              <icon-exclamation-circle />
            </template>
            余额不足，请调整使用金额或选择其他支付方式
          </a-alert>
        </div>

        <!-- 快捷金额选择 -->
        <div v-if="quickAmounts.length > 0" class="quick-amounts">
          <div class="quick-label">快捷选择：</div>
          <div class="quick-buttons">
            <a-button
              v-for="quickAmount in quickAmounts"
              :key="quickAmount"
              size="small"
              type="outline"
              :class="{ active: amount === quickAmount }"
              @click="handleQuickAmountSelect(quickAmount)"
            >
              ¥{{ formatAmount(quickAmount) }}
            </a-button>
          </div>
        </div>
      </div>

      <!-- 不可用提示 -->
      <div v-if="!method.enabled || userBalance <= 0" class="disabled-reason">
        <a-tag color="gray" size="small">
          {{ getDisabledReason() }}
        </a-tag>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { computed, ref, watch } from 'vue';
  import { IconUser, IconExclamationCircle } from '@arco-design/web-vue/es/icon';
  import type { PaymentMethod } from '@/http/api/payment/types';

  // Props
  interface Props {
    method: PaymentMethod;
    userBalance: number;
    maxAmount: number;
    amount: number;
  }

  const props = defineProps<Props>();

  // Emits
  const emit = defineEmits(['update:amount', 'toggle']);

  // 本地状态
  const isSelected = ref(props.amount > 0);

  // 计算属性
  const sliderValue = computed(() => {
    if (props.maxAmount <= 0) return 0;
    return (props.amount / props.maxAmount) * 100;
  });

  const quickAmounts = computed(() => {
    const amounts: number[] = [];
    const max = Math.min(props.userBalance, props.maxAmount);

    if (max >= 10) amounts.push(10);
    if (max >= 50) amounts.push(50);
    if (max >= 100) amounts.push(100);
    if (max >= 200) amounts.push(200);

    // 添加最大可用金额
    if (max > 0 && !amounts.includes(max)) {
      amounts.push(max);
    }

    return amounts.filter(amount => amount <= max);
  });

  // 方法
  function handleToggle() {
    if (!props.method.enabled || props.userBalance <= 0) return;

    const newSelected = !isSelected.value;
    isSelected.value = newSelected;

    if (newSelected) {
      // 启用时设置默认金额
      const defaultAmount = Math.min(props.userBalance, props.maxAmount);
      emit('update:amount', defaultAmount);
    } else {
      // 禁用时清零
      emit('update:amount', 0);
    }

    emit('toggle', newSelected);
  }

  function handleAmountChange(value: number | undefined) {
    const amount = Math.max(0, Math.min(value || 0, props.maxAmount, props.userBalance));
    emit('update:amount', amount);

    // 如果金额为0，自动取消选择
    if (amount === 0) {
      isSelected.value = false;
      emit('toggle', false);
    } else if (!isSelected.value) {
      isSelected.value = true;
      emit('toggle', true);
    }
  }

  function handleSliderChange(value: number | [number, number]) {
    const numValue = Array.isArray(value) ? value[0] : value;
    const amount = (numValue / 100) * props.maxAmount;
    handleAmountChange(amount);
  }

  function useMaxAmount() {
    const maxAmount = Math.min(props.userBalance, props.maxAmount);
    handleAmountChange(maxAmount);
  }

  function handleQuickAmountSelect(amount: number) {
    handleAmountChange(amount);
  }

  function formatAmount(amount: number): string {
    return amount.toFixed(2);
  }

  function getDisabledReason(): string {
    if (!props.method.enabled) {
      return '余额支付暂不可用';
    }
    if (props.userBalance <= 0) {
      return '余额不足';
    }
    return '暂不可用';
  }

  // 监听外部金额变化
  watch(
    () => props.amount,
    newAmount => {
      isSelected.value = newAmount > 0;
    }
  );
</script>

<style scoped lang="scss">
  .balance-payment-container {
    width: 100%;
  }

  .payment-method {
    border: 1px solid #e5e6eb;
    border-radius: 8px;
    background-color: #ffffff;
    transition: all 0.3s ease;

    &:hover:not(.disabled) {
      border-color: $primary-color;
      box-shadow: 0 2px 8px rgba(22, 93, 255, 0.1);
    }

    &.selected {
      border-color: $primary-color;
      background-color: #f0f8ff;
    }

    &.disabled {
      background-color: #f7f8fa;
      border-color: #e5e6eb;
      opacity: 0.6;

      .method-content {
        opacity: 0.5;
      }
    }
  }

  .method-header {
    padding: 16px;
    cursor: pointer;
  }

  .method-content {
    display: flex;
    align-items: center;
    gap: 12px;
  }

  .method-icon {
    flex-shrink: 0;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 6px;
    background-color: #e8f3ff;

    .icon {
      font-size: 20px;
      color: $primary-color;
    }
  }

  .method-info {
    flex: 1;

    .method-name {
      font-size: 15px;
      font-weight: 500;
      color: $text-color;
      margin-bottom: 4px;
    }

    .method-description {
      font-size: 13px;
      color: $text-color-secondary;
    }
  }

  .method-status {
    flex-shrink: 0;
  }

  .amount-input-section {
    padding: 0 16px 16px;
    border-top: 1px solid #e8f3ff;
  }

  .amount-controls {
    margin-bottom: 16px;
  }

  .input-group {
    margin-bottom: 12px;

    .input-label {
      display: block;
      font-size: 13px;
      color: $text-color-secondary;
      margin-bottom: 8px;
    }

    .input-wrapper {
      display: flex;
      gap: 8px;
      align-items: center;

      .amount-input {
        flex: 1;
      }

      .max-button {
        color: $primary-color;
        font-size: 12px;
        padding: 4px 8px;

        &:hover {
          background-color: #e8f3ff;
        }
      }
    }
  }

  .amount-slider {
    .slider-labels {
      display: flex;
      justify-content: space-between;
      margin-top: 4px;
      font-size: 12px;
      color: $text-color-tertiary;
    }
  }

  .balance-info {
    background-color: #f7f8fa;
    border-radius: 6px;
    padding: 12px;
    margin-bottom: 12px;

    .info-row {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 6px;

      &:last-child {
        margin-bottom: 0;
      }

      .info-label {
        font-size: 13px;
        color: $text-color-secondary;
      }

      .info-value {
        font-size: 13px;
        color: $text-color;
        font-weight: 500;

        &.highlight {
          color: $primary-color;
          font-weight: 600;
        }
      }
    }
  }

  .insufficient-warning {
    margin-bottom: 12px;

    :deep(.arco-alert) {
      padding: 8px 12px;
    }
  }

  .quick-amounts {
    display: flex;
    align-items: center;
    gap: 8px;
    flex-wrap: wrap;

    .quick-label {
      font-size: 13px;
      color: $text-color-secondary;
      white-space: nowrap;
    }

    .quick-buttons {
      display: flex;
      gap: 6px;
      flex-wrap: wrap;

      .arco-btn {
        font-size: 12px;
        height: 28px;
        padding: 0 8px;

        &.active {
          background-color: $primary-color;
          border-color: $primary-color;
          color: white;
        }
      }
    }
  }

  .disabled-reason {
    padding: 8px 16px;
    border-top: 1px solid #f2f3f5;
  }

  // 响应式设计
  @media (max-width: 768px) {
    .method-header {
      padding: 12px;
    }

    .method-content {
      gap: 10px;
    }

    .method-icon {
      width: 36px;
      height: 36px;

      .icon {
        font-size: 18px;
      }
    }

    .amount-input-section {
      padding: 0 12px 12px;
    }

    .balance-info {
      padding: 10px;
    }

    .quick-amounts {
      flex-direction: column;
      align-items: flex-start;
      gap: 6px;

      .quick-buttons {
        width: 100%;
      }
    }
  }
</style>
