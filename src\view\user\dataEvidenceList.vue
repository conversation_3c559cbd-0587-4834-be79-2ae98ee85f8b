<template>
  <div class="p-4 bg-grey">
    <a-page-header
      :show-back="false"
      :style="{ background: 'var(--color-bg-2)' }"
      title="存证列表"
    />
    <div class="py-4">
      <a-card>
        <a-row class="mb-4">
          <a-col :flex="1">
            <a-form :model="formModel" layout="inline">
              <a-form-item field="dataNumber" label="数据编号">
                <a-input v-model="formModel.dataNumber" placeholder="请输入数据编号" />
              </a-form-item>
              <a-form-item field="dataName" label="数据名称">
                <a-input v-model="formModel.dataName" placeholder="请输入数据名称" />
              </a-form-item>
              <a-form-item field="certificationUnit" label="存证单位">
                <a-input v-model="formModel.certificationUnit" placeholder="请输入存证单位" />
              </a-form-item>
              <a-form-item field="dataClassification" label="数据分类">
                <a-select
                  v-model="formModel.dataClassification"
                  placeholder="请选择数据分类"
                  allow-clear
                  :loading="dataClassificationLoading"
                >
                  <a-option
                    v-for="item in dataClassificationOptions"
                    :key="item.value"
                    :value="item.value"
                  >
                    {{ item.label }}
                  </a-option>
                </a-select>
              </a-form-item>
              <a-form-item field="dataLevel" label="数据级别">
                <a-select
                  v-model="formModel.dataLevel"
                  placeholder="请选择数据级别"
                  allow-clear
                  :loading="dataLevelLoading"
                >
                  <a-option v-for="item in dataLevelOptions" :key="item.value" :value="item.value">
                    {{ item.label }}
                  </a-option>
                </a-select>
              </a-form-item>
              <a-form-item field="dataResourceForm" label="数据资源形态">
                <a-select
                  v-model="formModel.dataResourceForm"
                  placeholder="请选择数据资源形态"
                  allow-clear
                  :loading="dataResourceFormLoading"
                >
                  <a-option
                    v-for="item in dataResourceFormOptions"
                    :key="item.value"
                    :value="item.value"
                  >
                    {{ item.label }}
                  </a-option>
                </a-select>
              </a-form-item>
              <a-form-item field="statusCd" label="存证状态">
                <a-select
                  v-model="formModel.statusCd"
                  placeholder="请选择状态"
                  allow-clear
                  :loading="statusLoading"
                >
                  <a-option v-for="item in statusOptions" :key="item.value" :value="item.value">
                    {{ item.label }}
                  </a-option>
                </a-select>
              </a-form-item>
              <a-form-item>
                <a-button type="primary" @click="search"> 搜索 </a-button>
                <a-button style="margin-left: 8px" @click="reset"> 重置 </a-button>
              </a-form-item>
            </a-form>
          </a-col>
          <a-col :flex="'86px'">
            <a-button type="primary" @click="createEvidence"> 新增存证 </a-button>
          </a-col>
        </a-row>
        <a-table
          :loading="loading"
          :pagination="pagination"
          :columns="columns"
          :data="tableData"
          @page-change="onPageChange"
        >
          <template #dataClassification="{ record }">
            {{ getDataClassificationText(record.dataClassification) }}
          </template>
          <template #dataLevel="{ record }">
            {{ getDataLevelText(record.dataLevel) }}
          </template>
          <template #dataScale="{ record }">
            {{ getDataScaleText(record.dataScale, record.dataScaleUnit) }}
          </template>
          <template #dataResourceForm="{ record }">
            {{ getDataResourceFormText(record.dataResourceForm) }}
          </template>
          <template #dataResourceOwnership="{ record }">
            {{ getDataResourceOwnershipText(record.dataResourceOwnership) }}
          </template>
          <template #dataSampleFile="{ record }">
            <a-link
              v-if="record.dataSampleFile"
              :style="{ cursor: 'pointer' }"
              @click="openSampleFile(record.dataSampleFile)"
            >
              查看
            </a-link>
            <span v-else>-</span>
          </template>
          <template #dataBlockHashValue="{ record }">
            <span :title="record.dataBlockHashValue">
              {{ record.dataBlockHashValue || '-' }}
            </span>
          </template>
          <template #dataGenerationTime="{ record }">
            {{ formatTime(record.dataGenerationTime) }}
          </template>
          <template #createTime="{ record }">
            {{ formatTime(record.createTime) }}
          </template>
          <template #updateTime="{ record }">
            {{ formatTime(record.updateTime) }}
          </template>
          <template #statusCd="{ record }">
            <a-tag :color="getStatusColor(getLatestReviewStatus(record))">
              {{ getStatusText(getLatestReviewStatus(record)) }}
            </a-tag>
          </template>
          <template #operations="{ record }">
            <a-space>
              <!-- 已提交、审核中状态显示查看按钮 -->
              <a-button
                v-if="['A', 'B'].includes(getLatestReviewStatus(record))"
                type="text"
                @click="viewDetail(record)"
              >
                查看
              </a-button>
              <!-- <a-button type="text" @click="viewCertificate(record)"> 查看证书 </a-button> -->
              <!-- 审核通过状态显示查看、查看证书和下载证书按钮 -->
              <template v-if="getLatestReviewStatus(record) === 'C'">
                <a-button type="text" @click="viewDetail(record)"> 查看 </a-button>
                <a-button type="text" @click="viewCertificate(record)"> 查看证书 </a-button>
                <!-- <a-button type="text" @click="downloadCertificate(record)"> 下载证书 </a-button> -->
              </template>
              <!-- 审核拒绝状态显示查看和编辑按钮 -->
              <template v-if="getLatestReviewStatus(record) === 'D'">
                <a-button type="text" @click="viewDetail(record)"> 查看 </a-button>
                <a-button type="text" @click="editEvidence(record)"> 编辑 </a-button>
              </template>
            </a-space>
          </template>
        </a-table>
      </a-card>
    </div>

    <!-- 存证详情弹出层 -->
    <a-modal
      v-model:visible="detailVisible"
      :width="1000"
      title="存证详情"
      ok-text="关闭"
      hide-cancel
      @ok="detailVisible = false"
    >
      <a-scrollbar style="max-height: 70vh; overflow: auto">
        <a-descriptions :data="detailData" bordered :column="2">
          <template #value="{ data }">
            <a-link
              v-if="data.clickable && data.url"
              :style="{ cursor: 'pointer' }"
              @click="openSampleFile(data.url)"
            >
              {{ data.value }}
            </a-link>
            <span v-else>{{ data.value }}</span>
          </template>
        </a-descriptions>

        <div v-if="evidenceFiles.length" class="mt-4">
          <h3>相关文件</h3>
          <a-row :gutter="16">
            <a-col v-for="(file, index) in evidenceFiles" :key="index" :span="6">
              <a-card :style="{ marginBottom: '16px' }">
                <template #cover>
                  <div class="file-preview">
                    <icon-file-pdf v-if="file.type === 'pdf'" />
                    <icon-file-image v-else-if="file.type === 'image'" />
                    <icon-file-audio v-else-if="file.type === 'text'" />
                    <icon-folder v-else-if="file.type === 'archive'" />
                    <icon-file v-else />
                  </div>
                </template>
                <a-typography-text :ellipsis="{ rows: 2 }" :title="file.name">
                  {{ file.name }}
                </a-typography-text>
                <div class="mt-2">
                  <a-space>
                    <a-link @click="openSampleFile(file.url)"> 查看 </a-link>
                    <a-link @click="downloadFile(file)"> 下载 </a-link>
                  </a-space>
                </div>
              </a-card>
            </a-col>
          </a-row>
        </div>

        <!-- 审核详情 -->
        <div v-if="reviewDetails.length" class="mt-4">
          <h3>审核详情</h3>
          <a-timeline>
            <a-timeline-item
              v-for="review in reviewDetails"
              :key="review.id"
              :dot-color="getReviewStatusColor(review.statusCd)"
            >
              <template #dot>
                <icon-check-circle v-if="review.statusCd === 'C'" />
                <icon-close-circle v-else-if="review.statusCd === 'D'" />
                <icon-clock-circle v-else-if="review.statusCd === 'B'" />
                <icon-edit v-else />
              </template>
              <div class="review-item">
                <div class="review-header">
                  <a-space>
                    <a-tag :color="getReviewStatusColor(review.statusCd)">
                      {{ getStatusText(review.statusCd) }}
                    </a-tag>
                    <span class="review-time">{{ formatTime(review.reviewDate) }}</span>
                  </a-space>
                </div>
                <div v-if="review.reviewResults" class="review-content mt-2">
                  <strong>审核结果：</strong>{{ review.reviewResults }}
                </div>
                <div class="review-compliance mt-2">
                  <a-row :gutter="16">
                    <a-col :span="6">
                      <span>数据权益主体合规性：</span>
                      <a-tag :color="review.reviewDataSubjectComp === 1 ? 'green' : 'red'">
                        {{ review.reviewDataSubjectComp === 1 ? '合规' : '不合规' }}
                      </a-tag>
                    </a-col>
                    <a-col :span="6">
                      <span>数据来源合规性：</span>
                      <a-tag :color="review.reviewDataSourceComp === 1 ? 'green' : 'red'">
                        {{ review.reviewDataSourceComp === 1 ? '合规' : '不合规' }}
                      </a-tag>
                    </a-col>
                    <a-col :span="6">
                      <span>数据处理合规性：</span>
                      <a-tag :color="review.reviewDataProcessComp === 1 ? 'green' : 'red'">
                        {{ review.reviewDataProcessComp === 1 ? '合规' : '不合规' }}
                      </a-tag>
                    </a-col>
                    <a-col :span="6">
                      <span>数据内容合规性：</span>
                      <a-tag :color="review.reviewDataContentComp === 1 ? 'green' : 'red'">
                        {{ review.reviewDataContentComp === 1 ? '合规' : '不合规' }}
                      </a-tag>
                    </a-col>
                  </a-row>
                </div>
                <div v-if="review.remark" class="review-remark mt-2">
                  <strong>备注：</strong>{{ review.remark }}
                </div>
                <div class="review-footer mt-2">
                  <a-space>
                    <span class="text-gray">审核机构ID：{{ review.reviewOrgId }}</span>
                    <span class="text-gray">创建人：{{ review.creator }}</span>
                  </a-space>
                </div>
              </div>
            </a-timeline-item>
          </a-timeline>
        </div>
      </a-scrollbar>
    </a-modal>

    <!-- 存证证书弹窗 -->
    <CertificateModal
      v-model:visible="certificateVisible"
      :certificate-data="currentCertificateData"
    />
  </div>
</template>

<script lang="ts" setup>
  import { ref, reactive, onMounted } from 'vue';
  import { Message } from '@arco-design/web-vue';
  import { useRouter } from 'vue-router';
  import {
    IconFilePdf,
    IconFileImage,
    IconFile,
    IconFileAudio,
    IconFolder,
    IconCheckCircle,
    IconCloseCircle,
    IconClockCircle,
    IconEdit,
  } from '@arco-design/web-vue/es/icon';
  import {
    getCertificateInfoPageClient,
    getCertificateInfoClient,
    type CertificateInfoRespVO,
    type CertificateReviewRespVO,
  } from '@/http/api/data/index';
  import { getDictDataListByType } from '@/http/api/global/proIndex';
  import CertificateModal from '@/components/CertificateModal.vue';

  // 定义类型接口
  interface DictDataItem {
    id: number;
    label: string;
    value: string;
    dictType: string;
  }

  interface DictDataResponse {
    code: number;
    data?: DictDataItem[];
    msg?: string;
  }

  interface EvidenceRecord {
    id: number;
    dataNumber?: string;
    dataName?: string;
    certificationUnit?: string;
    dataClassification?: string;
    dataLevel?: string;
    dataGenerationTime?: string;
    dataScale?: string;
    dataScaleUnit?: string;
    dataResourceForm?: string;
    dataResourceOwnership?: string;
    dataSampleFile?: string;
    restrictionDescription?: string;
    otherDescription?: string;
    dataBlockHashValue?: string;
    dataAccessAddress?: string;
    extendedInfoHashValue?: string;
    extendedInfoAccessAddress?: string;
    dataSourceInfo?: string;
    dataSourceSpecificInfo?: string;
    dataSourceEvidenceMaterial?: string;
    effectiveControlEvidenceMaterial?: string;
    personalInfoCollectionEvidenceMaterial?: string;
    remark?: string;
    createTime: string;
    creator?: string;
    updater?: string;
    updateTime?: string;
    certificateReviews?: CertificateReviewRespVO[];
  }

  interface EvidenceFile {
    name: string;
    type: string;
    url: string;
  }

  interface FormModel {
    dataNumber: string;
    dataName: string;
    certificationUnit: string;
    dataClassification: string;
    dataLevel: string;
    dataResourceForm: string;
    statusCd: string;
  }

  interface PaginationModel {
    total: number;
    current: number;
    pageSize: number;
  }

  interface DetailItem {
    label: string;
    value: string;
    clickable?: boolean;
    url?: string;
  }

  const router = useRouter();

  // 数据分类字典选项
  const dataClassificationOptions = ref<{ label: string; value: string }[]>([]);
  const dataClassificationLoading = ref(false);

  // 数据级别字典选项
  const dataLevelOptions = ref<{ label: string; value: string }[]>([]);
  const dataLevelLoading = ref(false);

  // 状态字典选项
  const statusOptions = ref<{ label: string; value: string }[]>([]);
  const statusLoading = ref(false);

  // 数据规模单位字典选项
  const dataScaleUnitOptions = ref<{ label: string; value: string }[]>([]);
  const dataScaleUnitLoading = ref(false);

  // 数据资源形态字典选项
  const dataResourceFormOptions = ref<{ label: string; value: string }[]>([]);
  const dataResourceFormLoading = ref(false);

  // 数据资源权属字典选项
  const dataResourceOwnershipOptions = ref<{ label: string; value: string }[]>([]);
  const dataResourceOwnershipLoading = ref(false);

  // 获取最新审核状态
  const getLatestReviewStatus = (record: EvidenceRecord): string => {
    if (!record.certificateReviews || record.certificateReviews.length === 0) {
      return 'A'; // 默认为已提交状态
    }

    // 按创建时间排序，获取最新的审核记录
    const latestReview = record.certificateReviews.sort((a, b) => {
      const timeA = new Date(a.createTime || '').getTime();
      const timeB = new Date(b.createTime || '').getTime();
      return timeB - timeA;
    })[0];

    return latestReview.statusCd || 'A';
  };

  // 从详情数据中获取最新审核状态
  const getLatestReviewStatusFromDetail = (data: CertificateInfoRespVO): string => {
    if (!data.certificateReviews || data.certificateReviews.length === 0) {
      return 'A'; // 默认为已提交状态
    }

    // 按创建时间排序，获取最新的审核记录
    const latestReview = data.certificateReviews.sort((a, b) => {
      const timeA = new Date(a.createTime || '').getTime();
      const timeB = new Date(b.createTime || '').getTime();
      return timeB - timeA;
    })[0];

    return latestReview.statusCd || 'A';
  };

  // 状态文本转换函数
  const getStatusText = (statusCd?: string) => {
    if (!statusCd) return '未知';
    const statusItem = statusOptions.value.find(item => item.value === statusCd);
    return statusItem ? statusItem.label : '未知';
  };

  // 获取状态颜色
  const getStatusColor = (statusCd?: string) => {
    switch (statusCd) {
      case 'A': // 已提交
        return 'blue';
      case 'B': // 审核中
        return 'orange';
      case 'C': // 审核通过
        return 'green';
      case 'D': // 审核拒绝
        return 'red';
      default:
        return 'gray';
    }
  };

  // 获取审核状态颜色（用于时间线）
  const getReviewStatusColor = (statusCd?: string) => {
    switch (statusCd) {
      case 'A': // 已提交
        return '#1890ff';
      case 'B': // 审核中
        return '#fa8c16';
      case 'C': // 审核通过
        return '#52c41a';
      case 'D': // 审核拒绝
        return '#f5222d';
      default:
        return '#d9d9d9';
    }
  };

  // 获取数据分类文本
  const getDataClassificationText = (value?: string) => {
    if (!value) return '';
    const item = dataClassificationOptions.value.find(item => item.value === value);
    return item ? item.label : value;
  };

  // 获取数据级别文本
  const getDataLevelText = (value?: string) => {
    if (!value) return '';
    const item = dataLevelOptions.value.find(item => item.value === value);
    return item ? item.label : value;
  };

  // 获取数据规模单位文本
  const getDataScaleUnitText = (value?: string) => {
    if (!value) return '';
    const item = dataScaleUnitOptions.value.find(item => item.value === value);
    return item ? item.label : value;
  };

  // 获取数据规模显示文本
  const getDataScaleText = (dataScale?: string, dataScaleUnit?: string) => {
    if (!dataScale) return '';
    const unitText = getDataScaleUnitText(dataScaleUnit);
    return unitText ? `${dataScale} ${unitText}` : dataScale;
  };

  // 获取数据资源形态文本
  const getDataResourceFormText = (value?: string) => {
    if (!value) return '';
    const item = dataResourceFormOptions.value.find(item => item.value === value);
    return item ? item.label : value;
  };

  // 获取数据资源权属单个值的文本
  const getDataResourceOwnershipSingleText = (value?: string) => {
    if (!value) return '';
    const item = dataResourceOwnershipOptions.value.find(item => item.value === value);
    return item ? item.label : value;
  };

  // 获取数据资源权属文本（处理JSON数组）
  const getDataResourceOwnershipText = (value?: string) => {
    if (!value) return '';

    try {
      // 尝试解析JSON
      const parsedValue = JSON.parse(value);

      if (Array.isArray(parsedValue)) {
        // 如果是数组，映射每个值并用逗号连接
        return parsedValue
          .map(item => getDataResourceOwnershipSingleText(item))
          .filter(text => text) // 过滤空值
          .join('，');
      } else {
        // 如果不是数组，直接处理单个值
        return getDataResourceOwnershipSingleText(parsedValue);
      }
    } catch (error) {
      // 如果JSON解析失败，尝试作为单个值处理
      return getDataResourceOwnershipSingleText(value);
    }
  };

  // 时间格式化函数
  const formatTime = (timestamp?: number | string) => {
    if (!timestamp || timestamp === 0) return '';
    const date = new Date(Number(timestamp));
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
    });
  };

  // 根据文件URL获取文件类型
  const getFileType = (url?: string) => {
    if (!url) return 'file';
    const extension = url.split('.').pop()?.toLowerCase();
    switch (extension) {
      case 'pdf':
        return 'pdf';
      case 'jpg':
      case 'jpeg':
      case 'png':
      case 'gif':
      case 'bmp':
      case 'webp':
        return 'image';
      case 'doc':
      case 'docx':
      case 'xls':
      case 'xlsx':
      case 'ppt':
      case 'pptx':
        return 'office';
      case 'txt':
      case 'md':
        return 'text';
      case 'zip':
      case 'rar':
      case '7z':
        return 'archive';
      default:
        return 'file';
    }
  };

  // 表单数据
  const formModel = reactive<FormModel>({
    dataNumber: '',
    dataName: '',
    certificationUnit: '',
    dataClassification: '',
    dataLevel: '',
    dataResourceForm: '',
    statusCd: '',
  });

  // 表格数据
  const loading = ref(false);
  const tableData = ref<EvidenceRecord[]>([]);
  const pagination = reactive<PaginationModel>({
    total: 0,
    current: 1,
    pageSize: 10,
  });

  // 证书弹窗相关数据
  const certificateVisible = ref(false);
  const currentCertificateData = ref<CertificateInfoRespVO>({} as CertificateInfoRespVO);

  // 表格列定义
  const columns = [
    {
      title: '数据编号',
      dataIndex: 'dataNumber',
      width: 150,
    },
    {
      title: '数据名称',
      dataIndex: 'dataName',
      width: 200,
    },
    {
      title: '存证单位',
      dataIndex: 'certificationUnit',
      width: 150,
    },
    {
      title: '数据分类',
      dataIndex: 'dataClassification',
      slotName: 'dataClassification',
      width: 120,
    },
    {
      title: '数据级别',
      dataIndex: 'dataLevel',
      slotName: 'dataLevel',
      width: 100,
    },
    {
      title: '数据生成时间',
      dataIndex: 'dataGenerationTime',
      slotName: 'dataGenerationTime',
      width: 180,
    },
    {
      title: '数据规模',
      dataIndex: 'dataScale',
      slotName: 'dataScale',
      width: 120,
    },
    {
      title: '数据资源形态',
      dataIndex: 'dataResourceForm',
      slotName: 'dataResourceForm',
      width: 140,
    },
    {
      title: '数据资源权属申明',
      dataIndex: 'dataResourceOwnership',
      slotName: 'dataResourceOwnership',
      width: 160,
    },
    {
      title: '数据样本文件',
      dataIndex: 'dataSampleFile',
      slotName: 'dataSampleFile',
      width: 120,
    },
    {
      title: '数据块哈希值',
      dataIndex: 'dataBlockHashValue',
      slotName: 'dataBlockHashValue',
      width: 200,
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      slotName: 'createTime',
      width: 180,
    },
    {
      title: '更新时间',
      dataIndex: 'updateTime',
      slotName: 'updateTime',
      width: 180,
    },
    {
      title: '状态',
      dataIndex: 'statusCd',
      slotName: 'statusCd',
      width: 100,
    },
    {
      title: '操作',
      slotName: 'operations',
      width: 150,
      fixed: 'right' as const,
    },
  ];

  // 详情弹窗
  const detailVisible = ref(false);
  const detailData = ref<DetailItem[]>([]);
  const evidenceFiles = ref<EvidenceFile[]>([]);
  const reviewDetails = ref<CertificateReviewRespVO[]>([]);

  // 获取数据分类字典选项
  const fetchDataClassificationOptions = async () => {
    dataClassificationLoading.value = true;
    try {
      const response = (await getDictDataListByType('data_classification')) as DictDataResponse;
      if (response && response.data) {
        dataClassificationOptions.value = response.data.map((item: DictDataItem) => ({
          label: item.label,
          value: item.value,
        }));
      } else {
        Message.warning('获取数据分类选项失败，请刷新页面重试');
      }
    } catch (error) {
      console.error('获取数据分类选项失败:', error);
      Message.error('获取数据分类选项失败，请刷新页面重试');
    } finally {
      dataClassificationLoading.value = false;
    }
  };

  // 获取数据级别字典选项
  const fetchDataLevelOptions = async () => {
    dataLevelLoading.value = true;
    try {
      const response = (await getDictDataListByType('data_level')) as DictDataResponse;
      if (response && response.data) {
        dataLevelOptions.value = response.data.map((item: DictDataItem) => ({
          label: item.label,
          value: item.value,
        }));
      } else {
        Message.warning('获取数据级别选项失败，请刷新页面重试');
      }
    } catch (error) {
      console.error('获取数据级别选项失败:', error);
      Message.error('获取数据级别选项失败，请刷新页面重试');
    } finally {
      dataLevelLoading.value = false;
    }
  };

  // 获取状态字典选项
  const fetchStatusOptions = async () => {
    statusLoading.value = true;
    try {
      const response = (await getDictDataListByType(
        'data_evidence_review_node'
      )) as DictDataResponse;
      if (response && response.data) {
        statusOptions.value = response.data.map((item: DictDataItem) => ({
          label: item.label,
          value: item.value,
        }));
      } else {
        Message.warning('获取状态选项失败，请刷新页面重试');
      }
    } catch (error) {
      console.error('获取状态选项失败:', error);
      Message.error('获取状态选项失败，请刷新页面重试');
    } finally {
      statusLoading.value = false;
    }
  };

  // 获取数据规模单位字典选项
  const fetchDataScaleUnitOptions = async () => {
    dataScaleUnitLoading.value = true;
    try {
      const response = (await getDictDataListByType('data_scale_unit')) as DictDataResponse;
      if (response && response.data) {
        dataScaleUnitOptions.value = response.data.map((item: DictDataItem) => ({
          label: item.label,
          value: item.value,
        }));
      } else {
        Message.warning('获取数据规模单位选项失败，请刷新页面重试');
      }
    } catch (error) {
      console.error('获取数据规模单位选项失败:', error);
      Message.error('获取数据规模单位选项失败，请刷新页面重试');
    } finally {
      dataScaleUnitLoading.value = false;
    }
  };

  // 获取数据资源形态字典选项
  const fetchDataResourceFormOptions = async () => {
    dataResourceFormLoading.value = true;
    try {
      const response = (await getDictDataListByType('data_resource_form')) as DictDataResponse;
      if (response && response.data) {
        dataResourceFormOptions.value = response.data.map((item: DictDataItem) => ({
          label: item.label,
          value: item.value,
        }));
      } else {
        Message.warning('获取数据资源形态选项失败，请刷新页面重试');
      }
    } catch (error) {
      console.error('获取数据资源形态选项失败:', error);
      Message.error('获取数据资源形态选项失败，请刷新页面重试');
    } finally {
      dataResourceFormLoading.value = false;
    }
  };

  // 获取数据资源权属字典选项
  const fetchDataResourceOwnershipOptions = async () => {
    dataResourceOwnershipLoading.value = true;
    try {
      const response = (await getDictDataListByType('data_resource_ownership')) as DictDataResponse;
      if (response && response.data) {
        dataResourceOwnershipOptions.value = response.data.map((item: DictDataItem) => ({
          label: item.label,
          value: item.value,
        }));
      } else {
        Message.warning('获取数据资源权属选项失败，请刷新页面重试');
      }
    } catch (error) {
      console.error('获取数据资源权属选项失败:', error);
      Message.error('获取数据资源权属选项失败，请刷新页面重试');
    } finally {
      dataResourceOwnershipLoading.value = false;
    }
  };

  // 获取存证列表
  const fetchData = async () => {
    loading.value = true;

    try {
      const params = {
        pageNo: pagination.current.toString(),
        pageSize: pagination.pageSize.toString(),
        ...formModel,
      };

      // 过滤空值参数
      Object.keys(params).forEach(key => {
        if (params[key] === '' || params[key] === null || params[key] === undefined) {
          delete params[key];
        }
      });

      const response = await getCertificateInfoPageClient(params);

      if (response.code === 0 && response.data) {
        tableData.value = response.data.list || [];
        pagination.total = response.data.total || 0;
      } else {
        Message.error(response.msg || '获取数据失败');
        tableData.value = [];
        pagination.total = 0;
      }
    } catch (error) {
      console.error('获取存证列表失败:', error);
      Message.error('获取数据失败，请稍后重试');
      tableData.value = [];
      pagination.total = 0;
    } finally {
      loading.value = false;
    }
  };

  // 页码变化
  const onPageChange = (page: number) => {
    pagination.current = page;
    fetchData();
  };

  // 搜索
  const search = () => {
    pagination.current = 1;
    fetchData();
  };

  // 重置
  const reset = () => {
    formModel.dataNumber = '';
    formModel.dataName = '';
    formModel.certificationUnit = '';
    formModel.dataClassification = '';
    formModel.dataLevel = '';
    formModel.dataResourceForm = '';
    formModel.statusCd = '';
    pagination.current = 1;
    fetchData();
  };

  // 创建存证
  const createEvidence = () => {
    router.push('/data/eviApply');
  };

  // 查看详情
  const viewDetail = async (record: EvidenceRecord) => {
    try {
      // 调用API获取详细信息
      const response = await getCertificateInfoClient(record.id);

      if (response.code === 0 && response.data) {
        const data = response.data;

        detailData.value = [
          {
            label: '存证ID',
            value: data.id?.toString() || '',
          },
          {
            label: '数据编号',
            value: data.dataNumber || '无',
          },
          {
            label: '数据名称',
            value: data.dataName || '无',
          },
          {
            label: '存证单位',
            value: data.certificationUnit || '无',
          },
          {
            label: '数据分类',
            value: getDataClassificationText(data.dataClassification) || '无',
          },
          {
            label: '数据级别',
            value: getDataLevelText(data.dataLevel) || '无',
          },
          {
            label: '数据生成时间',
            value: formatTime(data.dataGenerationTime) || '无',
          },
          {
            label: '数据规模',
            value: getDataScaleText(data.dataScale, data.dataScaleUnit) || '无',
          },
          {
            label: '数据资源形态',
            value: getDataResourceFormText(data.dataResourceForm) || '无',
          },
          {
            label: '数据资源权属申明',
            value: getDataResourceOwnershipText(data.dataResourceOwnership) || '无',
          },
          {
            label: '数据样本文件',
            value: data.dataSampleFile ? '点击查看文件' : '无',
            clickable: !!data.dataSampleFile,
            url: data.dataSampleFile,
          },
          {
            label: '限制情况说明',
            value: data.restrictionDescription || '无',
          },
          {
            label: '其他描述',
            value: data.otherDescription || '无',
          },
          {
            label: '数据块哈希值',
            value: data.dataBlockHashValue || '无',
          },
          {
            label: '数据访问地址',
            value: data.dataAccessAddress || '无',
          },
          {
            label: '扩展信息哈希值',
            value: data.extendedInfoHashValue || '无',
          },
          {
            label: '扩展信息访问地址',
            value: data.extendedInfoAccessAddress || '无',
          },
          {
            label: '数据来源信息',
            value: data.dataSourceInfo || '无',
          },
          {
            label: '数据来源具体信息',
            value: data.dataSourceSpecificInfo || '无',
          },
          {
            label: '数据来源佐证材料',
            value: data.dataSourceEvidenceMaterial ? '点击查看文件' : '无',
            clickable: !!data.dataSourceEvidenceMaterial,
            url: data.dataSourceEvidenceMaterial,
          },
          {
            label: '有效控制措施佐证材料',
            value: data.effectiveControlEvidenceMaterial ? '点击查看文件' : '无',
            clickable: !!data.effectiveControlEvidenceMaterial,
            url: data.effectiveControlEvidenceMaterial,
          },
          {
            label: '个人信息采集合法佐证材料',
            value: data.personalInfoCollectionEvidenceMaterial ? '点击查看文件' : '无',
            clickable: !!data.personalInfoCollectionEvidenceMaterial,
            url: data.personalInfoCollectionEvidenceMaterial,
          },
          {
            label: '创建时间',
            value: formatTime(data.createTime) || '无',
          },
          {
            label: '创建人',
            value: data.creator || '无',
          },
          {
            label: '更新时间',
            value: formatTime(data.updateTime) || '无',
          },
          {
            label: '更新人',
            value: data.updater || '无',
          },
          {
            label: '状态',
            value: getStatusText(getLatestReviewStatusFromDetail(data)) || '无',
          },
          {
            label: '备注',
            value: data.remark || '无',
          },
        ];

        // 收集所有文件信息
        evidenceFiles.value = [];

        // 添加数据样本文件
        if (data.dataSampleFile) {
          evidenceFiles.value.push({
            name: '数据样本文件',
            type: getFileType(data.dataSampleFile),
            url: data.dataSampleFile,
          });
        }

        // 添加数据来源佐证材料
        if (data.dataSourceEvidenceMaterial) {
          evidenceFiles.value.push({
            name: '数据来源佐证材料',
            type: getFileType(data.dataSourceEvidenceMaterial),
            url: data.dataSourceEvidenceMaterial,
          });
        }

        // 添加有效控制措施佐证材料
        if (data.effectiveControlEvidenceMaterial) {
          evidenceFiles.value.push({
            name: '有效控制措施佐证材料',
            type: getFileType(data.effectiveControlEvidenceMaterial),
            url: data.effectiveControlEvidenceMaterial,
          });
        }

        // 添加个人信息采集合法佐证材料
        if (data.personalInfoCollectionEvidenceMaterial) {
          evidenceFiles.value.push({
            name: '个人信息采集合法佐证材料',
            type: getFileType(data.personalInfoCollectionEvidenceMaterial),
            url: data.personalInfoCollectionEvidenceMaterial,
          });
        }

        // 设置审核详情数据
        reviewDetails.value = data.certificateReviews || [];
        // 按创建时间倒序排列，最新的审核记录在前
        reviewDetails.value.sort((a, b) => {
          const timeA = new Date(a.createTime || '').getTime();
          const timeB = new Date(b.createTime || '').getTime();
          return timeB - timeA;
        });

        detailVisible.value = true;
      } else {
        Message.error(response.msg || '获取存证详情失败');
      }
    } catch (error) {
      console.error('获取存证详情失败:', error);
      Message.error('获取存证详情失败，请稍后重试');
    }
  };

  // 编辑存证
  const editEvidence = (record: EvidenceRecord) => {
    // 跳转到编辑页面，传递存证ID
    router.push(`/data/eviApply?id=${record.id}&mode=edit`);
  };

  // 查看证书
  const viewCertificate = async (record: EvidenceRecord) => {
    try {
      // 调用API获取完整的存证信息
      const response = await getCertificateInfoClient(record.id);

      if (response.code === 0 && response.data) {
        currentCertificateData.value = response.data;
        certificateVisible.value = true;
      } else {
        Message.error(response.msg || '获取存证信息失败');
      }
    } catch (error) {
      console.error('获取存证信息失败:', error);
      Message.error('获取存证信息失败，请稍后重试');
    }
  };

  // 下载证书
  const downloadCertificate = async (record: EvidenceRecord) => {
    try {
      // 调用API获取完整的存证信息
      const response = await getCertificateInfoClient(record.id);

      if (response.code === 0 && response.data) {
        // 设置证书数据并打开弹窗，然后自动触发下载
        currentCertificateData.value = response.data;
        certificateVisible.value = true;

        // 给弹窗一点时间渲染，然后触发下载
        setTimeout(() => {
          // 这里可以通过事件或者ref调用证书组件的下载方法
          // 暂时显示提示信息
          Message.info('证书弹窗已打开，请点击"下载证书"按钮进行下载');
        }, 500);
      } else {
        Message.error(response.msg || '获取存证信息失败');
      }
    } catch (error) {
      console.error('获取存证信息失败:', error);
      Message.error('获取存证信息失败，请稍后重试');
    }
  };

  // 下载文件
  const downloadFile = (file: EvidenceFile) => {
    if (!file.url) {
      Message.warning('文件地址不存在');
      return;
    }

    try {
      // 创建一个隐藏的下载链接
      const link = document.createElement('a');
      link.href = file.url;
      link.download = file.name;
      link.style.display = 'none';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      Message.success(`开始下载文件：${file.name}`);
    } catch (error) {
      console.error('下载文件失败:', error);
      Message.error('下载文件失败，请稍后重试');
    }
  };

  // 打开样本文件
  const openSampleFile = (url: string) => {
    if (!url) {
      Message.warning('文件地址不存在');
      return;
    }

    try {
      // 在新标签页中打开文件
      window.open(url, '_blank');
    } catch (error) {
      console.error('打开文件失败:', error);
      Message.error('打开文件失败，请检查文件地址是否有效');
    }
  };

  // 组件挂载时初始化
  onMounted(() => {
    fetchDataClassificationOptions();
    fetchDataLevelOptions();
    fetchStatusOptions();
    fetchDataScaleUnitOptions();
    fetchDataResourceFormOptions();
    fetchDataResourceOwnershipOptions();
    fetchData();
  });
</script>

<style lang="scss" scoped>
  .file-preview {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100px;
    background-color: var(--color-fill-2);
    font-size: 36px;
    color: var(--color-text-3);
  }

  .review-item {
    padding: 12px;
    background-color: var(--color-fill-1);
    border-radius: 6px;
    margin-bottom: 8px;
  }

  .review-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 8px;
  }

  .review-time {
    color: var(--color-text-3);
    font-size: 12px;
  }

  .review-content {
    color: var(--color-text-2);
    line-height: 1.5;
  }

  .review-compliance {
    .arco-col {
      margin-bottom: 8px;
    }

    span {
      font-size: 12px;
      color: var(--color-text-3);
    }
  }

  .review-remark {
    color: var(--color-text-2);
    line-height: 1.5;
    background-color: var(--color-fill-2);
    padding: 8px;
    border-radius: 4px;
  }

  .review-footer {
    border-top: 1px solid var(--color-border-2);
    padding-top: 8px;

    .text-gray {
      color: var(--color-text-4);
      font-size: 12px;
    }
  }

  h3 {
    margin-bottom: 16px;
    color: var(--color-text-1);
    font-weight: 600;
  }
</style>
