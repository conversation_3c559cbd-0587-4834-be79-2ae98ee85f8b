// * 登录模块
export namespace Login {
	export interface ReqLoginForm {
		username: string,
		password: string|boolean,
		// code: string,
		// publicKey: string,
		// randomStr: string,
		// loginWithCode: boolean,
		// grant_type: string,
	}
	export interface CodeForm {
		src: string,
		value: string,
		len: number,
		type: string,
	}
	export interface ResLogin {
		access_token: string;
	}
	export interface ResLogin {
		access_token: string;
	}
}