<template>
  <div class="order-info-container">
    <!-- 订单信息卡片 -->
    <a-card class="order-card" :bordered="false">
      <template #title>
        <div class="order-header">
          <icon-file class="order-icon" />
          <span class="order-title">订单信息</span>
          <a-tag
            v-if="orderInfo"
            :color="getOrderStatusColor(orderInfo.status)"
            class="order-status"
          >
            {{ getOrderStatusText(orderInfo.status) }}
          </a-tag>
        </div>
      </template>

      <div v-if="loading" class="loading-container">
        <a-spin :size="24" />
        <p class="loading-text">正在加载订单信息...</p>
      </div>

      <div v-else-if="error" class="error-container">
        <a-result status="error" :title="error">
          <template #extra>
            <a-button type="primary" @click="$emit('retry')">重新加载</a-button>
          </template>
        </a-result>
      </div>

      <div v-else-if="orderInfo" class="order-content">
        <!-- 产品信息 -->
        <div class="product-section">
          <div class="product-info">
            <div class="product-image">
              <img
                v-if="orderInfo.productImage"
                :src="orderInfo.productImage"
                :alt="orderInfo.productName"
                class="product-img"
              />
              <div v-else class="product-placeholder">
                <icon-image class="placeholder-icon" />
              </div>
            </div>

            <div class="product-details">
              <h3 class="product-name">{{ orderInfo.productName }}</h3>
              <p class="product-description">{{ orderInfo.productDescription }}</p>
              <div class="product-meta">
                <a-tag color="blue">{{ getOrderTypeText(orderInfo.orderType) }}</a-tag>
                <span class="product-id">订单号：{{ orderInfo.orderId }}</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 价格信息 -->
        <div class="price-section">
          <div class="price-row">
            <span class="price-label">商品原价</span>
            <span class="price-value">¥{{ formatAmount(orderInfo.originalAmount) }}</span>
          </div>

          <div v-if="orderInfo.discountAmount > 0" class="price-row discount-row">
            <span class="price-label">商品优惠</span>
            <span class="price-value discount-value"
              >-¥{{ formatAmount(orderInfo.discountAmount) }}</span
            >
          </div>

          <a-divider class="price-divider" />

          <div class="price-row total-row">
            <span class="price-label total-label">应付金额</span>
            <span class="price-value total-value">¥{{ formatAmount(orderInfo.finalAmount) }}</span>
          </div>
        </div>

        <!-- 订单详情 -->
        <div class="order-details">
          <a-descriptions :column="1" size="small" bordered>
            <a-descriptions-item label="创建时间">
              {{ formatTime(orderInfo.createTime) }}
            </a-descriptions-item>
            <a-descriptions-item label="过期时间">
              <span :class="{ 'expire-warning': isExpiringSoon }">
                {{ formatTime(orderInfo.expireTime) }}
              </span>
              <a-tag v-if="isExpiringSoon" color="orange" size="small" class="ml-2">
                即将过期
              </a-tag>
            </a-descriptions-item>
            <a-descriptions-item label="用户ID">
              {{ orderInfo.userId }}
            </a-descriptions-item>
          </a-descriptions>
        </div>

        <!-- 倒计时 -->
        <div v-if="timeRemaining > 0" class="countdown-section">
          <div class="countdown-container">
            <icon-clock-circle class="countdown-icon" />
            <span class="countdown-text">支付剩余时间：</span>
            <span class="countdown-time">{{ formatCountdown(timeRemaining) }}</span>
          </div>
          <a-progress
            :percent="countdownPercent"
            :stroke-color="getCountdownColor()"
            :show-text="false"
            size="small"
            class="countdown-progress"
          />
        </div>
      </div>
    </a-card>
  </div>
</template>

<script setup lang="ts">
  import { computed, onMounted, onUnmounted, ref, watch } from 'vue';
  import { IconFile, IconImage, IconClockCircle } from '@arco-design/web-vue/es/icon';
  import type { OrderInfo } from '@/http/api/payment/types';

  // Props
  interface Props {
    orderInfo: OrderInfo | null;
    loading?: boolean;
    error?: string | null;
  }

  const props = withDefaults(defineProps<Props>(), {
    loading: false,
    error: null,
  });

  // Emits
  const emit = defineEmits<{
    retry: [];
  }>();

  // 倒计时相关
  const timeRemaining = ref(0);
  const countdownTimer = ref<number | null>(null);

  // 计算属性
  const isExpiringSoon = computed(() => {
    if (!props.orderInfo) return false;
    const expireTime = new Date(props.orderInfo.expireTime).getTime();
    const currentTime = new Date().getTime();
    const timeDiff = expireTime - currentTime;
    return timeDiff > 0 && timeDiff < 30 * 60 * 1000; // 30分钟内过期
  });

  const countdownPercent = computed(() => {
    if (!props.orderInfo || timeRemaining.value <= 0) return 0;

    const createTime = new Date(props.orderInfo.createTime).getTime();
    const expireTime = new Date(props.orderInfo.expireTime).getTime();
    const totalTime = expireTime - createTime;
    const elapsed = totalTime - timeRemaining.value;

    return Math.max(0, Math.min(100, (elapsed / totalTime) * 100));
  });

  // 方法
  function getOrderStatusColor(status: string): string {
    const colorMap: Record<string, string> = {
      created: 'blue',
      paid: 'green',
      cancelled: 'red',
      expired: 'gray',
    };
    return colorMap[status] || 'gray';
  }

  function getOrderStatusText(status: string): string {
    const textMap: Record<string, string> = {
      created: '待支付',
      paid: '已支付',
      cancelled: '已取消',
      expired: '已过期',
    };
    return textMap[status] || '未知状态';
  }

  function getOrderTypeText(type: string): string {
    const typeMap: Record<string, string> = {
      package: '套餐购买',
      evidence: '数据存证',
      software: '软件著作权',
      api: 'API服务',
    };
    return typeMap[type] || '其他服务';
  }

  function formatAmount(amount: number): string {
    return amount.toFixed(2);
  }

  function formatTime(timeString: string): string {
    const date = new Date(timeString);
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
    });
  }

  function formatCountdown(milliseconds: number): string {
    const totalSeconds = Math.floor(milliseconds / 1000);
    const hours = Math.floor(totalSeconds / 3600);
    const minutes = Math.floor((totalSeconds % 3600) / 60);
    const seconds = totalSeconds % 60;

    if (hours > 0) {
      return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
    } else {
      return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
    }
  }

  function getCountdownColor(): string {
    const percent = countdownPercent.value;
    if (percent < 20) return '#f53f3f'; // 红色
    if (percent < 50) return '#ff7d00'; // 橙色
    return '#00b42a'; // 绿色
  }

  function updateCountdown() {
    if (!props.orderInfo) return;

    const expireTime = new Date(props.orderInfo.expireTime).getTime();
    const currentTime = new Date().getTime();
    const remaining = expireTime - currentTime;

    if (remaining <= 0) {
      timeRemaining.value = 0;
      stopCountdown();
    } else {
      timeRemaining.value = remaining;
    }
  }

  function startCountdown() {
    updateCountdown();
    countdownTimer.value = window.setInterval(updateCountdown, 1000);
  }

  function stopCountdown() {
    if (countdownTimer.value) {
      clearInterval(countdownTimer.value);
      countdownTimer.value = null;
    }
  }

  // 生命周期
  onMounted(() => {
    if (props.orderInfo) {
      startCountdown();
    }
  });

  onUnmounted(() => {
    stopCountdown();
  });

  // 监听订单信息变化
  watch(
    () => props.orderInfo,
    newOrderInfo => {
      stopCountdown();
      if (newOrderInfo) {
        startCountdown();
      }
    }
  );
</script>

<style scoped lang="scss">
  .order-info-container {
    width: 100%;
  }

  .order-card {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border-radius: 8px;

    :deep(.arco-card-header) {
      border-bottom: 1px solid #f2f3f5;
      padding: 16px 20px;
    }

    :deep(.arco-card-body) {
      padding: 20px;
    }
  }

  .order-header {
    display: flex;
    align-items: center;
    gap: 8px;

    .order-icon {
      font-size: 18px;
      color: $primary-color;
    }

    .order-title {
      font-size: 16px;
      font-weight: 600;
      color: $text-color;
    }

    .order-status {
      margin-left: auto;
    }
  }

  .loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px 20px;

    .loading-text {
      margin-top: 16px;
      color: $text-color-secondary;
    }
  }

  .error-container {
    padding: 20px;
  }

  .order-content {
    display: flex;
    flex-direction: column;
    gap: 24px;
  }

  .product-section {
    .product-info {
      display: flex;
      gap: 16px;

      .product-image {
        flex-shrink: 0;
        width: 80px;
        height: 80px;
        border-radius: 8px;
        overflow: hidden;
        border: 1px solid #f2f3f5;

        .product-img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }

        .product-placeholder {
          width: 100%;
          height: 100%;
          display: flex;
          align-items: center;
          justify-content: center;
          background-color: #f7f8fa;

          .placeholder-icon {
            font-size: 24px;
            color: #c9cdd4;
          }
        }
      }

      .product-details {
        flex: 1;

        .product-name {
          font-size: 16px;
          font-weight: 600;
          color: $text-color;
          margin: 0 0 8px 0;
          line-height: 1.4;
        }

        .product-description {
          font-size: 14px;
          color: $text-color-secondary;
          margin: 0 0 12px 0;
          line-height: 1.5;
        }

        .product-meta {
          display: flex;
          align-items: center;
          gap: 12px;

          .product-id {
            font-size: 12px;
            color: $text-color-tertiary;
          }
        }
      }
    }
  }

  .price-section {
    background-color: #f7f8fa;
    border-radius: 8px;
    padding: 16px;

    .price-row {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 8px;

      &:last-child {
        margin-bottom: 0;
      }

      .price-label {
        font-size: 14px;
        color: $text-color-secondary;
      }

      .price-value {
        font-size: 14px;
        color: $text-color;
        font-weight: 500;
      }

      &.discount-row {
        .discount-value {
          color: $success-color;
        }
      }

      &.total-row {
        .total-label {
          font-size: 16px;
          font-weight: 600;
          color: $text-color;
        }

        .total-value {
          font-size: 18px;
          font-weight: 600;
          color: $primary-color;
        }
      }
    }

    .price-divider {
      margin: 12px 0;
    }
  }

  .order-details {
    :deep(.arco-descriptions-item-label) {
      font-weight: 500;
      color: $text-color-secondary;
    }

    .expire-warning {
      color: $warning-color;
      font-weight: 500;
    }
  }

  .countdown-section {
    background: linear-gradient(135deg, #e8f3ff 0%, #f0f8ff 100%);
    border: 1px solid #d1e7ff;
    border-radius: 8px;
    padding: 16px;

    .countdown-container {
      display: flex;
      align-items: center;
      gap: 8px;
      margin-bottom: 12px;

      .countdown-icon {
        font-size: 16px;
        color: $primary-color;
      }

      .countdown-text {
        font-size: 14px;
        color: $text-color-secondary;
      }

      .countdown-time {
        font-size: 16px;
        font-weight: 600;
        color: $primary-color;
        font-family: 'Courier New', monospace;
      }
    }

    .countdown-progress {
      :deep(.arco-progress-line-wrapper) {
        background-color: rgba(255, 255, 255, 0.8);
      }
    }
  }

  // 响应式设计
  @media (max-width: 768px) {
    .order-card {
      :deep(.arco-card-header) {
        padding: 12px 16px;
      }

      :deep(.arco-card-body) {
        padding: 16px;
      }
    }

    .product-info {
      flex-direction: column;
      gap: 12px;

      .product-image {
        align-self: center;
      }
    }

    .price-section {
      padding: 12px;
    }

    .countdown-section {
      padding: 12px;
    }
  }
</style>
