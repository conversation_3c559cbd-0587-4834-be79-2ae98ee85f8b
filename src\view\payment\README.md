# 文创链独立收银台

## 项目概述

文创链独立收银台是一个功能完整、设计精美的支付解决方案，支持多种支付方式，包括在线支付（微信、支付宝）、积分支付、优惠券支付和余额支付。采用现代化的技术栈和响应式设计，为用户提供安全、便捷的支付体验。

## 🚀 核心特性

### 💳 多样化支付方式
- **在线支付**: 微信支付、支付宝支付
- **账户支付**: 余额支付、积分支付
- **优惠支付**: 优惠券抵扣
- **组合支付**: 支持多种支付方式组合使用

### 📱 全平台适配
- **响应式设计**: 完美适配PC、平板、手机
- **移动端优化**: 触摸友好的交互设计
- **安全区域适配**: 支持iPhone X等设备
- **横屏支持**: 良好的横屏显示效果

### 🔒 安全可靠
- **SSL加密**: 银行级安全传输
- **支付验证**: 多重验证机制
- **防重复提交**: 避免重复扣款
- **状态监控**: 实时支付状态跟踪

### 🎨 用户体验
- **直观界面**: 清晰的支付流程指示
- **实时计算**: 动态显示优惠和金额
- **智能推荐**: 最优支付方式建议
- **友好提示**: 详细的错误和成功提示

## 📁 项目结构

```
src/view/payment/
├── Cashier.vue                    # 主收银台页面
├── PaymentResult.vue              # 支付结果页面
├── components/                    # 组件目录
│   ├── OrderInfo.vue             # 订单信息组件
│   ├── PaymentMethods.vue        # 支付方式选择组件
│   ├── PaymentCalculation.vue    # 支付计算组件
│   ├── PaymentConfirmation.vue   # 支付确认组件
│   └── methods/                  # 支付方式子组件
│       ├── OnlinePayment.vue     # 在线支付组件
│       ├── BalancePayment.vue    # 余额支付组件
│       ├── PointsPayment.vue     # 积分支付组件
│       └── CouponPayment.vue     # 优惠券支付组件
├── hooks/                        # Composition API Hooks
│   ├── usePayment.ts             # 支付逻辑Hook
│   ├── usePaymentCalculation.ts  # 支付计算Hook
│   ├── usePaymentValidation.ts   # 支付验证Hook
│   └── useMobileAdaptation.ts    # 移动端适配Hook
├── styles/                       # 样式文件
│   └── mobile.scss               # 移动端样式
├── __tests__/                    # 测试文件
│   └── PaymentCalculation.test.ts
└── README.md                     # 项目说明
```

## 🛠 技术栈

### 前端框架
- **Vue 3**: 渐进式JavaScript框架
- **TypeScript**: 类型安全的JavaScript超集
- **Composition API**: Vue 3的组合式API

### UI组件库
- **Arco Design**: 企业级UI组件库
- **自定义组件**: 针对支付场景的专用组件

### 状态管理
- **Pinia**: Vue 3官方推荐的状态管理库
- **响应式状态**: 实时更新的支付状态

### 样式方案
- **SCSS**: CSS预处理器
- **Tailwind CSS**: 原子化CSS框架
- **响应式设计**: 移动优先的设计理念

### 开发工具
- **Vite**: 快速的构建工具
- **ESLint**: 代码质量检查
- **Prettier**: 代码格式化
- **Vitest**: 单元测试框架

## 🚀 快速开始

### 1. 安装依赖

```bash
npm install
```

### 2. 启动开发服务器

```bash
npm run dev
```

### 3. 访问收银台

```
http://localhost:3000/payment/cashier?orderId=ORDER_123&type=package&amount=99.00
```

### 4. 构建生产版本

```bash
npm run build
```

## 📖 使用指南

### 基础用法

```vue
<template>
  <div>
    <!-- 跳转到收银台 -->
    <a-button @click="goToCashier">去支付</a-button>
  </div>
</template>

<script setup>
import { useRouter } from 'vue-router'

const router = useRouter()

function goToCashier() {
  router.push({
    name: 'Cashier',
    query: {
      orderId: 'ORDER_123456',
      type: 'package',
      amount: '99.00'
    }
  })
}
</script>
```

### 组件使用

```vue
<template>
  <!-- 订单信息组件 -->
  <OrderInfo
    :order-info="orderInfo"
    :loading="loading"
    :error="error"
    @retry="handleRetry"
  />
  
  <!-- 支付方式组件 -->
  <PaymentMethods
    :loading="loading"
    @payment-change="handlePaymentChange"
    @amount-change="handleAmountChange"
  />
  
  <!-- 支付计算组件 -->
  <PaymentCalculation
    :calculation="calculation"
    :loading="calculationLoading"
    @recalculate="handleRecalculate"
  />
  
  <!-- 支付确认组件 -->
  <PaymentConfirmation
    :final-amount="finalAmount"
    :savings="savings"
    :submitting="submitting"
    @submit="handleSubmit"
    @cancel="handleCancel"
  />
</template>
```

### Hooks使用

```typescript
// 支付逻辑Hook
const {
  initializePayment,
  submitPayment,
  cancelPayment,
  paymentStore
} = usePayment()

// 支付计算Hook
const {
  setPointsUsage,
  setBalanceUsage,
  smartAllocatePayment,
  paymentBreakdown
} = usePaymentCalculation()

// 支付验证Hook
const {
  validatePayment,
  validationErrors,
  isPaymentValid
} = usePaymentValidation()

// 移动端适配Hook
const {
  isMobile,
  isTablet,
  deviceType,
  getAdaptiveFontSize
} = useMobileAdaptation()
```

## 🧪 测试

### 运行单元测试

```bash
npm run test
```

### 运行测试覆盖率

```bash
npm run test:coverage
```

### 测试文件结构

```
__tests__/
├── PaymentCalculation.test.ts    # 支付计算组件测试
├── PaymentMethods.test.ts        # 支付方式组件测试
├── hooks/
│   ├── usePayment.test.ts        # 支付Hook测试
│   └── usePaymentCalculation.test.ts
└── utils/
    └── testUtils.ts              # 测试工具函数
```

## 📚 API文档

详细的API接口文档请参考：
- [支付API接口文档](../../../docs/payment-api-reference.md)
- [使用文档](../../../docs/payment-cashier-usage.md)

## 🎨 样式定制

### 主题色彩

```scss
// 主品牌色
$primary-color: #165dff;
$primary-light: #4080ff;
$primary-dark: #0f58d3;

// 功能色
$success-color: #00b42a;
$warning-color: #ff7d00;
$error-color: #f53f3f;
```

### 移动端适配

```scss
// 移动端断点
$mobile-breakpoint: 768px;
$tablet-breakpoint: 1024px;

// 移动端混合器
@mixin mobile-only {
  @media (max-width: #{$mobile-breakpoint - 1px}) {
    @content;
  }
}

// 触摸优化
@mixin touch-optimized {
  min-height: 44px;
  min-width: 44px;
  touch-action: manipulation;
}
```

## 🔧 配置说明

### 环境变量

```env
# API基础地址
VITE_API_BASE_URL=https://api.wenchuangchain.com

# 支付配置
VITE_PAYMENT_TIMEOUT=300000
VITE_WECHAT_APP_ID=wx1234567890
VITE_ALIPAY_APP_ID=2021000000000000
```

### 路由配置

```typescript
{
  path: '/payment',
  children: [
    {
      path: 'cashier',
      name: 'Cashier',
      component: () => import('@/view/payment/Cashier.vue'),
      meta: {
        title: '收银台',
        requiresAuth: true
      }
    },
    {
      path: 'result',
      name: 'PaymentResult',
      component: () => import('@/view/payment/PaymentResult.vue'),
      meta: {
        title: '支付结果',
        requiresAuth: true
      }
    }
  ]
}
```

## 🐛 常见问题

### Q: 支付超时如何处理？
A: 系统会自动进行支付状态轮询，超时后提示用户查询最新状态或重新支付。

### Q: 移动端键盘弹起遮挡问题？
A: 使用了键盘检测和视口调整，自动处理键盘弹起时的布局问题。

### Q: 如何添加新的支付方式？
A: 在`PaymentMethods.vue`中添加新的支付方式组件，并在相关Hook中添加处理逻辑。

### Q: 支付安全如何保障？
A: 采用HTTPS传输、支付信息加密、防重复提交等多重安全措施。

## 📝 更新日志

### v1.0.0 (2024-01-01)
- ✨ 初始版本发布
- ✨ 支持微信、支付宝、积分、优惠券、余额支付
- ✨ 完整的移动端适配
- ✨ 响应式设计实现
- ✨ 支付状态实时监控
- ✨ 多重安全验证机制

## 🤝 贡献指南

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 👥 团队

- **产品设计**: 文创链产品团队
- **前端开发**: 文创链前端团队
- **后端开发**: 文创链后端团队
- **测试**: 文创链测试团队

## 📞 联系我们

- **官网**: https://www.wenchuangchain.com
- **邮箱**: <EMAIL>
- **客服**: 400-123-4567
