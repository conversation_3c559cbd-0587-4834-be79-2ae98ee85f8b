/** @format */

import http from '@/http/sopHttp';
import type { Payment } from './interface';

/**
 * 支付相关API接口
 */

// 获取订单信息
export async function getOrderInfo(
  params: Payment.GetOrderInfoRequest
): Promise<Payment.GetOrderInfoResponse> {
  return http.get(`/payment/order/${params.orderId}`) as Promise<Payment.GetOrderInfoResponse>;
}

// 获取支付方式列表
export async function getPaymentMethods(
  params: Payment.GetPaymentMethodsRequest
): Promise<Payment.GetPaymentMethodsResponse> {
  return http.get('/payment/methods', params) as Promise<Payment.GetPaymentMethodsResponse>;
}

// 获取用户资产信息
export async function getUserAssets(
  params?: Payment.GetUserAssetsRequest
): Promise<Payment.GetUserAssetsResponse> {
  return http.get('/payment/user/assets', params) as Promise<Payment.GetUserAssetsResponse>;
}

// 获取可用优惠券
export async function getAvailableCoupons(
  params: Payment.GetAvailableCouponsRequest
): Promise<Payment.GetAvailableCouponsResponse> {
  return http.get(
    '/payment/coupons/available',
    params
  ) as Promise<Payment.GetAvailableCouponsResponse>;
}

// 计算支付金额
export async function calculatePayment(
  params: Payment.CalculatePaymentRequest
): Promise<Payment.CalculatePaymentResponse> {
  return http.post('/payment/calculate', params) as Promise<Payment.CalculatePaymentResponse>;
}

// 创建支付订单
export async function createPaymentOrder(
  params: Payment.CreatePaymentOrderRequest
): Promise<Payment.CreatePaymentOrderResponse> {
  return http.post('/payment/create', params) as Promise<Payment.CreatePaymentOrderResponse>;
}

// 查询支付状态
export async function queryPaymentStatus(
  params: Payment.QueryPaymentStatusRequest
): Promise<Payment.QueryPaymentStatusResponse> {
  return http.get(`/payment/status/${params.paymentId}`, {
    orderId: params.orderId,
  }) as Promise<Payment.QueryPaymentStatusResponse>;
}

// 取消支付
export async function cancelPayment(
  params: Payment.CancelPaymentRequest
): Promise<Payment.CancelPaymentResponse> {
  return http.post('/payment/cancel', params) as Promise<Payment.CancelPaymentResponse>;
}

// 申请退款
export async function requestRefund(
  params: Payment.RefundRequest
): Promise<Payment.RefundResponse> {
  return http.post('/payment/refund', params) as Promise<Payment.RefundResponse>;
}

// 获取支付记录
export async function getPaymentRecords(
  params?: Payment.GetPaymentRecordsRequest
): Promise<Payment.GetPaymentRecordsResponse> {
  return http.get('/payment/records', params) as Promise<Payment.GetPaymentRecordsResponse>;
}

// 获取积分记录
export async function getPointsRecords(
  params?: Payment.GetPointsRecordsRequest
): Promise<Payment.GetPointsRecordsResponse> {
  return http.get('/payment/points/records', params) as Promise<Payment.GetPointsRecordsResponse>;
}

// 获取余额记录
export async function getBalanceRecords(
  params?: Payment.GetBalanceRecordsRequest
): Promise<Payment.GetBalanceRecordsResponse> {
  return http.get('/payment/balance/records', params) as Promise<Payment.GetBalanceRecordsResponse>;
}

// 获取优惠券记录
export async function getCouponRecords(
  params?: Payment.GetCouponRecordsRequest
): Promise<Payment.GetCouponRecordsResponse> {
  return http.get('/payment/coupons/records', params) as Promise<Payment.GetCouponRecordsResponse>;
}

/**
 * 微信支付相关接口
 */
export namespace WechatPay {
  // 创建微信支付订单
  export async function createOrder(params: {
    orderId: string;
    amount: number;
    description: string;
    notifyUrl?: string;
  }) {
    return http.post('/payment/wechat/create', params);
  }

  // 查询微信支付订单
  export async function queryOrder(params: { orderId: string; transactionId?: string }) {
    return http.get('/payment/wechat/query', params);
  }

  // 关闭微信支付订单
  export async function closeOrder(params: { orderId: string }) {
    return http.post('/payment/wechat/close', params);
  }
}

/**
 * 支付宝支付相关接口
 */
export namespace AlipayPay {
  // 创建支付宝支付订单
  export async function createOrder(params: {
    orderId: string;
    amount: number;
    subject: string;
    body?: string;
    notifyUrl?: string;
  }) {
    return http.post('/payment/alipay/create', params);
  }

  // 查询支付宝支付订单
  export async function queryOrder(params: { orderId: string; tradeNo?: string }) {
    return http.get('/payment/alipay/query', params);
  }

  // 关闭支付宝支付订单
  export async function closeOrder(params: { orderId: string }) {
    return http.post('/payment/alipay/close', params);
  }
}

/**
 * 积分支付相关接口
 */
export namespace PointsPay {
  // 积分支付
  export async function payWithPoints(params: { orderId: string; points: number; amount: number }) {
    return http.post('/payment/points/pay', params);
  }

  // 获取积分兑换比例
  export async function getPointsRate() {
    return http.get('/payment/points/rate');
  }

  // 获取积分余额
  export async function getPointsBalance() {
    return http.get('/payment/points/balance');
  }
}

/**
 * 余额支付相关接口
 */
export namespace BalancePay {
  // 余额支付
  export async function payWithBalance(params: {
    orderId: string;
    amount: number;
    password?: string; // 支付密码
  }) {
    return http.post('/payment/balance/pay', params);
  }

  // 获取余额
  export async function getBalance() {
    return http.get('/payment/balance');
  }

  // 余额充值
  export async function recharge(params: { amount: number; paymentMethod: 'wechat' | 'alipay' }) {
    return http.post('/payment/balance/recharge', params);
  }
}

/**
 * 优惠券相关接口
 */
export namespace CouponPay {
  // 使用优惠券
  export async function useCoupon(params: { orderId: string; couponId: string; amount: number }) {
    return http.post('/payment/coupon/use', params);
  }

  // 验证优惠券
  export async function validateCoupon(params: {
    couponId: string;
    orderId: string;
    amount: number;
  }) {
    return http.post('/payment/coupon/validate', params);
  }

  // 获取用户优惠券列表
  export async function getUserCoupons(params?: {
    status?: 'unused' | 'used' | 'expired';
    pageNum?: number;
    pageSize?: number;
  }) {
    return http.get('/payment/coupon/list', params);
  }
}
