// * 登录模块
export namespace User {
	export interface editUserInfo {
		nickName?: string,
		publicKey?: string | unknown,
		cipher?: string | boolean,
	}
	// 关注 Attention
	export interface ReqAttention {
		current: number,
		size: number,
	}
	export interface userInfo {
		current: number,
		size: number,
	}
	export interface ResAttention {
		records: object,
		total: number,
	}
	// 浏览 RecordRecord
	export interface ReqBrowseRecord {
		current: number,
		size: number,
	}
}