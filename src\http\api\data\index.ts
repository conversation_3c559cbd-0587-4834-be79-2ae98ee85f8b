/** @format */

import proHttp from '@/http/proHttp';

// 类型定义
export interface CertificateReviewRespVO {
  id: number;
  reviewDataId?: number;
  reviewOrgId?: number;
  reviewDate?: string;
  reviewResults?: string;
  reviewDataSubjectComp?: number;
  reviewDataSourceComp?: number;
  reviewDataProcessComp?: number;
  reviewDataContentComp?: number;
  statusCd?: string;
  remark?: string;
  creator?: string;
  createTime?: string;
  updater?: string;
  updateTime?: string;
}

export interface BusinessBlockchainLinkRespVO {
  id: number;
  businessId?: number;
  businessType?: string;
  address?: string;
  dacId?: number;
  worksId?: number;
  amount?: number;
  categoryId?: number;
  to?: string;
  gasUsed?: string;
  blockNumber?: string;
  transactionHash?: string;
  hash?: string;
  createDate?: string;
  statusDate?: string;
  statusCd?: string;
  remark?: string;
  creator?: string;
  createTime?: string;
  updater?: string;
  updateTime?: string;
}

export interface CertificateInfoRespVO {
  id: number;
  dataNumber?: string;
  dataName?: string;
  certificationUnit?: string;
  dataClassification?: string;
  dataLevel?: string;
  dataGenerationTime?: string;
  dataScale?: string;
  dataScaleUnit?: string;
  dataResourceForm?: string;
  dataResourceOwnership?: string;
  dataSampleFile?: string;
  restrictionDescription?: string;
  otherDescription?: string;
  dataBlockHashValue?: string;
  dataAccessAddress?: string;
  extendedInfoHashValue?: string;
  extendedInfoAccessAddress?: string;
  dataSourceInfo?: string;
  dataSourceSpecificInfo?: string;
  dataSourceEvidenceMaterial?: string;
  effectiveControlEvidenceMaterial?: string;
  personalInfoCollectionEvidenceMaterial?: string;
  remark?: string;
  createTime: string;
  creator?: string;
  updater?: string;
  updateTime?: string;
  certificateReviews?: CertificateReviewRespVO[];
  blockchainLinks?: BusinessBlockchainLinkRespVO[];
}

export interface PageResultCertificateInfoRespVO {
  list: CertificateInfoRespVO[];
  total: number;
}

export interface CommonResultPageResultCertificateInfoRespVO {
  code: number;
  data: PageResultCertificateInfoRespVO;
  msg: string;
}

export interface CommonResultCertificateInfoRespVO {
  code: number;
  data: CertificateInfoRespVO;
  msg: string;
}

// 存证信息相关接口
// 生成存证编号
export function generateDataNumber() {
  return proHttp.post('/app-api/business/certificate-info/generateDataNumber');
}
// 创建存证信息 - 客户端接口
export function createCertificateInfo(params: {
  dataNumber?: string;
  dataName?: string;
  certificationUnit?: string;
  dataClassification?: string;
  dataLevel?: string;
  dataGenerationTime?: string;
  dataScale?: string;
  dataScaleUnit?: string;
  dataResourceForm?: string;
  dataResourceOwnership?: string;
  dataSampleFile?: string;
  restrictionDescription?: string;
  otherDescription?: string;
  dataBlockHashValue?: string;
  dataAccessAddress?: string;
  extendedInfoHashValue?: string;
  extendedInfoAccessAddress?: string;
  dataSourceInfo?: string;
  dataSourceSpecificInfo?: string;
  dataSourceEvidenceMaterial?: string;
  effectiveControlEvidenceMaterial?: string;
  personalInfoCollectionEvidenceMaterial?: string;
  remark?: string;
}) {
  return proHttp.post('/app-api/business/certificate-info/create', params);
}

// 更新存证信息
export function updateCertificateInfo(params: {
  dataNumber?: string;
  dataName?: string;
  certificationUnit?: string;
  dataClassification?: string;
  dataLevel?: string;
  dataGenerationTime?: string;
  dataScale?: string;
  dataScaleUnit?: string;
  dataResourceForm?: string;
  dataResourceOwnership?: string;
  dataSampleFile?: string;
  restrictionDescription?: string;
  otherDescription?: string;
  dataBlockHashValue?: string;
  dataAccessAddress?: string;
  extendedInfoHashValue?: string;
  extendedInfoAccessAddress?: string;
  dataSourceInfo?: string;
  dataSourceSpecificInfo?: string;
  dataSourceEvidenceMaterial?: string;
  effectiveControlEvidenceMaterial?: string;
  personalInfoCollectionEvidenceMaterial?: string;
  remark?: string;
}) {
  return proHttp.put('/admin-api/business/certificate-info/update', params);
}

// 获取存证信息分页
export function getCertificateInfoPage(params: {
  dataNumber?: string;
  dataName?: string;
  certificationUnit?: string;
  dataClassification?: string;
  dataLevel?: string;
  dataGenerationTime?: string;
  dataScale?: string;
  dataScaleUnit?: string;
  dataResourceForm?: string;
  dataResourceOwnership?: string;
  dataSampleFile?: string;
  restrictionDescription?: string;
  otherDescription?: string;
  dataBlockHashValue?: string;
  dataAccessAddress?: string;
  extendedInfoHashValue?: string;
  extendedInfoAccessAddress?: string;
  dataSourceInfo?: string;
  dataSourceSpecificInfo?: string;
  dataSourceEvidenceMaterial?: string;
  effectiveControlEvidenceMaterial?: string;
  personalInfoCollectionEvidenceMaterial?: string;
  remark?: string;
  createTime?: string;
  pageNo: string;
  pageSize: string;
}) {
  return proHttp.get('/admin-api/business/certificate-info/page', { params });
}

// 获取单个存证信息
export function getCertificateInfo(id: number) {
  return proHttp.get('/admin-api/business/certificate-info/get', { params: { id } });
}

// 导出存证信息Excel
export function exportCertificateInfoExcel(params: {
  dataNumber?: string;
  dataName?: string;
  certificationUnit?: string;
  dataClassification?: string;
  dataLevel?: string;
  dataGenerationTime?: string;
  dataScale?: string;
  dataScaleUnit?: string;
  dataResourceForm?: string;
  dataResourceOwnership?: string;
  dataSampleFile?: string;
  restrictionDescription?: string;
  otherDescription?: string;
  dataBlockHashValue?: string;
  dataAccessAddress?: string;
  extendedInfoHashValue?: string;
  extendedInfoAccessAddress?: string;
  dataSourceInfo?: string;
  dataSourceSpecificInfo?: string;
  dataSourceEvidenceMaterial?: string;
  effectiveControlEvidenceMaterial?: string;
  personalInfoCollectionEvidenceMaterial?: string;
  remark?: string;
  createTime?: string;
  pageNo: string;
  pageSize: string;
}) {
  return proHttp.get('/admin-api/business/certificate-info/export-excel', { params });
}

// 删除存证信息
export function deleteCertificateInfo(id: number) {
  return proHttp.delete('/admin-api/business/certificate-info/delete', { params: { id } });
}

// 客户端 - 获取存证信息分页列表
export function getCertificateInfoPageClient(params: {
  dataNumber?: string;
  dataName?: string;
  certificationUnit?: string;
  dataClassification?: string;
  dataLevel?: string;
  dataGenerationTime?: string;
  dataScale?: string;
  dataScaleUnit?: string;
  dataResourceForm?: string;
  dataResourceOwnership?: string;
  dataSampleFile?: string;
  restrictionDescription?: string;
  otherDescription?: string;
  dataBlockHashValue?: string;
  dataAccessAddress?: string;
  extendedInfoHashValue?: string;
  extendedInfoAccessAddress?: string;
  dataSourceInfo?: string;
  dataSourceSpecificInfo?: string;
  dataSourceEvidenceMaterial?: string;
  effectiveControlEvidenceMaterial?: string;
  personalInfoCollectionEvidenceMaterial?: string;
  remark?: string;
  createTime?: string;
  pageNo: string;
  pageSize: string;
}): Promise<CommonResultPageResultCertificateInfoRespVO> {
  return proHttp.get('/app-api/business/certificate-info/page', {
    params,
  }) as Promise<CommonResultPageResultCertificateInfoRespVO>;
}

// 客户端 - 获取单个存证信息详情
export function getCertificateInfoClient(id: number): Promise<CommonResultCertificateInfoRespVO> {
  return proHttp.get('/app-api/business/certificate-info/get', {
    params: { id },
  }) as Promise<CommonResultCertificateInfoRespVO>;
}

// 客户端 - 根据审核状态获得存证信息分页列表
export function getCertificateInfoPageByStatus(params: {
  dataNumber?: string;
  dataName?: string;
  certificationUnit?: string;
  dataClassification?: string;
  dataLevel?: string;
  dataGenerationTime?: string[];
  dataScale?: string;
  dataScaleUnit?: string;
  dataResourceForm?: string;
  dataResourceOwnership?: string;
  dataSampleFile?: string;
  restrictionDescription?: string;
  otherDescription?: string;
  dataBlockHashValue?: string;
  dataAccessAddress?: string;
  extendedInfoHashValue?: string;
  extendedInfoAccessAddress?: string;
  dataSourceInfo?: string;
  dataSourceSpecificInfo?: string;
  dataSourceEvidenceMaterial?: string;
  effectiveControlEvidenceMaterial?: string;
  personalInfoCollectionEvidenceMaterial?: string;
  statusCd?: string;
  statusCdList?: string[];
  remark?: string;
  createTime?: string[];
  pageNo: number;
  pageSize: number;
}): Promise<CommonResultPageResultCertificateInfoRespVO> {
  return proHttp.get('/app-api/business/certificate-info/page-by-status', {
    params,
  }) as Promise<CommonResultPageResultCertificateInfoRespVO>;
}
