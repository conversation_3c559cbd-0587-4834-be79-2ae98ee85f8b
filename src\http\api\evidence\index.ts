/** @format */

import http from "@/http/proHttp";

// 区块链存证相关接口
// 创建存证申请
export function createEvidenceApplication(params: {
  id?: string;
  evidenceType?: string;
  evidenceTitle?: string;
  evidenceDescription?: string;
  applicantName?: string;
  applicantType?: string;
  applicantId?: string;
  contactPhone?: string;
  contactEmail?: string;
  evidenceFiles?: string[];
  evidenceHash?: string;
  blockchainNetwork?: string;
  statusCd?: string;
  remark?: string;
}) {
  return http.post('/admin-api/evidence/application/create', params);
}

// 更新存证申请
export function updateEvidenceApplication(params: {
  id?: string;
  evidenceType?: string;
  evidenceTitle?: string;
  evidenceDescription?: string;
  applicantName?: string;
  applicantType?: string;
  applicantId?: string;
  contactPhone?: string;
  contactEmail?: string;
  evidenceFiles?: string[];
  evidenceHash?: string;
  blockchainNetwork?: string;
  statusCd?: string;
  remark?: string;
}) {
  return http.put('/admin-api/evidence/application/update', params);
}

// 获取存证申请分页
export function getEvidenceApplicationPage(params: {
  evidenceType?: string;
  evidenceTitle?: string;
  applicantName?: string;
  applicantType?: string;
  statusCd?: string;
  blockchainNetwork?: string;
  createTimeStart?: string;
  createTimeEnd?: string;
  pageNo: string;
  pageSize: string;
}) {
  return http.get('/admin-api/evidence/application/page', { params });
}

// 获取存证申请详情
export function getEvidenceApplication(id: number) {
  return http.get('/admin-api/evidence/application/get', { params: { id } });
}

// 删除存证申请
export function deleteEvidenceApplication(id: number) {
  return http.delete('/admin-api/evidence/application/delete', { params: { id } });
}

// 提交存证到区块链
export function submitToBlockchain(params: {
  applicationId: string;
  evidenceData: string;
  evidenceHash: string;
  blockchainNetwork: string;
}) {
  return http.post('/admin-api/evidence/blockchain/submit', params);
}

// 查询区块链存证记录
export function queryBlockchainEvidence(params: {
  evidenceHash?: string;
  transactionHash?: string;
  blockNumber?: string;
  applicantName?: string;
  evidenceTitle?: string;
  pageNo: string;
  pageSize: string;
}) {
  return http.get('/admin-api/evidence/blockchain/query', { params });
}

// 验证区块链存证
export function verifyBlockchainEvidence(params: {
  evidenceHash: string;
  transactionHash?: string;
  blockNumber?: string;
}) {
  return http.post('/admin-api/evidence/blockchain/verify', params);
}

// 获取存证证书
export function getEvidenceCertificate(id: string) {
  return http.get(`/admin-api/evidence/certificate/${id}`);
}

// 下载存证证书
export function downloadEvidenceCertificate(id: string) {
  return http.download(`/admin-api/evidence/certificate/download/${id}`);
}

// 获取区块链网络状态
export function getBlockchainNetworkStatus() {
  return http.get('/admin-api/evidence/blockchain/status');
}

// 获取存证统计信息
export function getEvidenceStatistics(params: {
  timeRange?: string;
  evidenceType?: string;
}) {
  return http.get('/admin-api/evidence/statistics', { params });
}

// 批量存证
export function batchCreateEvidence(params: {
  evidences: Array<{
    evidenceTitle: string;
    evidenceType: string;
    evidenceDescription: string;
    evidenceFiles: string[];
  }>;
  applicantInfo: {
    applicantName: string;
    applicantType: string;
    applicantId: string;
    contactPhone: string;
    contactEmail: string;
  };
}) {
  return http.post('/admin-api/evidence/batch/create', params);
}

// 获取存证类型列表
export function getEvidenceTypes() {
  return http.get('/admin-api/evidence/types');
}

// 获取支持的区块链网络列表
export function getBlockchainNetworks() {
  return http.get('/admin-api/evidence/blockchain/networks');
}

// 计算文件哈希值
export function calculateFileHash(file: File) {
  return http.post('/admin-api/evidence/hash/calculate', file);
}

// 获取存证费用
export function getEvidenceFee(params: {
  evidenceType: string;
  fileSize: number;
  blockchainNetwork: string;
}) {
  return http.get('/admin-api/evidence/fee/calculate', { params });
}
