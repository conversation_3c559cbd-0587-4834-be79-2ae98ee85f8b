# 用户登录页面优化说明

## 概述

本次优化为用户登录页面添加了两种登录方式：
1. **账号+密码登录**（原有功能）
2. **手机号+短信验证码登录**（新增功能）

## 功能特性

### 1. 登录方式切换
- 使用标签页（Tabs）组件实现登录方式切换
- 支持"账号登录"和"手机登录"两种模式
- 切换登录方式时自动清空表单数据

### 2. 账号登录
- 支持用户名/邮箱 + 密码登录
- 密码使用 MD5 加密传输
- 保持原有的登录逻辑和API接口

### 3. 手机号登录
- 手机号格式验证（支持中国大陆手机号）
- 短信验证码发送功能
- 验证码倒计时（60秒）
- 验证码输入限制（只允许数字，4-6位）

### 4. 表单验证
- **手机号验证**：`/^1[3-9]\d{9}$/`
- **短信验证码验证**：`/^\d{4,6}$/`
- **必填字段验证**：所有登录字段都进行必填验证

### 5. 用户体验优化
- 发送验证码按钮状态管理
- 倒计时显示和自动重置
- 错误提示信息优化
- 表单切换时数据清空

## API接口

### 发送短信验证码
```typescript
memberSendSmsCode({
  mobile: string,    // 手机号
  scene: '2'        // 场景：2-登录
})
```

### 手机号登录
```typescript
memberSmsLogin({
  mobile: string,    // 手机号
  smsCode: string   // 短信验证码
})
```

### 账号登录（原有）
```typescript
memberLogin({
  username: string,  // 用户名/邮箱
  password: string   // MD5加密后的密码
})
```

## 技术实现

### 1. 组件结构
- 使用 Vue 3 Composition API
- 响应式数据管理
- 条件渲染实现不同登录表单

### 2. 状态管理
```typescript
const loginType = ref('account');  // 登录方式
const form = reactive({           // 表单数据
  username: '',
  password: '',
  mobile: '',
  smsCode: '',
});
const countdown = ref(60);        // 倒计时
```

### 3. 关键方法
- `handleLoginTypeChange()`: 登录方式切换处理
- `sendSmsCode()`: 发送短信验证码
- `handleSmsCodeInput()`: 验证码输入处理
- `handleSubmit()`: 登录提交处理
- `setTime()`: 倒计时管理

## 样式优化

- 优化标签页间距和样式
- 统一表单元素间距
- 保持与原有设计风格一致

## 测试覆盖

已添加单元测试覆盖以下功能：
- 页面渲染正确性
- 登录方式切换
- 表单数据清空
- 手机号格式验证
- 短信验证码格式验证
- 验证码输入处理

## 使用说明

1. 用户访问登录页面，默认显示"账号登录"标签
2. 点击"手机登录"标签切换到手机号登录模式
3. 输入手机号后，点击"发送验证码"按钮
4. 输入收到的短信验证码
5. 点击"登录"按钮完成登录

## 兼容性

- 保持与原有登录功能完全兼容
- 不影响现有用户的登录体验
- 支持新老用户使用不同登录方式
