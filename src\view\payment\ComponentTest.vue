<template>
  <div class="component-test">
    <h1>组件测试页面</h1>
    <p>测试各个组件是否能正常加载和显示</p>
    
    <div class="test-section">
      <h2>1. 服务类型选择组件测试</h2>
      <ServiceTypeSelector
        :loading="false"
        :selected-type="''"
        @select="handleServiceTypeSelect"
        @change="handleServiceTypeChange"
      />
    </div>

    <div class="test-section">
      <h2>2. 优惠券选择组件测试</h2>
      <CouponSelector
        :available-coupons="[]"
        :selected-coupons="[]"
        :order-amount="100"
        :loading="false"
        @select="handleCouponSelect"
        @remove="handleCouponRemove"
      />
    </div>

    <div class="test-section">
      <h2>3. 支付方式选择组件测试</h2>
      <PaymentMethodSelector
        :payment-methods="[]"
        :user-assets="undefined"
        :order-amount="100"
        :loading="false"
        @payment-change="handlePaymentMethodChange"
        @amount-change="handleAmountChange"
      />
    </div>

    <div class="test-section">
      <h2>4. 支付确认组件测试</h2>
      <PaymentConfirmation
        :final-amount="100"
        :savings="0"
        :submitting="false"
        :show-cancel-button="true"
        @submit="handleSubmitPayment"
        @cancel="handleCancelPayment"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
  import { Message } from '@arco-design/web-vue';
  import ServiceTypeSelector from './components/ServiceTypeSelector.vue';
  import CouponSelector from './components/CouponSelector.vue';
  import PaymentMethodSelector from './components/PaymentMethodSelector.vue';
  import PaymentConfirmation from './components/PaymentConfirmation.vue';

  // 事件处理
  function handleServiceTypeSelect(serviceType: any) {
    console.log('服务类型选择:', serviceType);
    Message.success('服务类型选择组件工作正常');
  }

  function handleServiceTypeChange(serviceTypeValue: string) {
    console.log('服务类型变化:', serviceTypeValue);
  }

  function handleCouponSelect(coupon: any) {
    console.log('优惠券选择:', coupon);
    Message.success('优惠券选择组件工作正常');
  }

  function handleCouponRemove(couponId: string) {
    console.log('移除优惠券:', couponId);
  }

  function handlePaymentMethodChange(methods: any) {
    console.log('支付方式变化:', methods);
    Message.success('支付方式选择组件工作正常');
  }

  function handleAmountChange(amount: number) {
    console.log('支付金额变化:', amount);
  }

  function handleSubmitPayment() {
    Message.success('支付确认组件工作正常');
  }

  function handleCancelPayment() {
    Message.info('取消支付');
  }
</script>

<style scoped lang="less">
  .component-test {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;

    h1 {
      color: #262626;
      margin-bottom: 16px;
    }

    p {
      color: #8c8c8c;
      margin-bottom: 32px;
    }

    .test-section {
      margin-bottom: 40px;
      padding: 20px;
      border: 1px solid #f0f0f0;
      border-radius: 8px;
      background: #fafafa;

      h2 {
        color: #262626;
        margin-bottom: 20px;
        font-size: 18px;
      }
    }
  }
</style>
