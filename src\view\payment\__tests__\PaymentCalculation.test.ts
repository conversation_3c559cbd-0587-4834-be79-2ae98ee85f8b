/** @format */

import { describe, it, expect, beforeEach, vi } from 'vitest';
import { mount } from '@vue/test-utils';
import { createPinia, setActivePinia } from 'pinia';
import PaymentCalculation from '../components/PaymentCalculation.vue';
import { usePaymentStore } from '@/store/modules/payment';
import type { PaymentCalculation as PaymentCalculationType } from '@/http/api/payment/types';

// Mock Arco Design components
vi.mock('@arco-design/web-vue', () => ({
  Message: {
    error: vi.fn(),
    success: vi.fn(),
    warning: vi.fn(),
    info: vi.fn()
  }
}));

// Mock icons
vi.mock('@arco-design/web-vue/es/icon', () => ({
  IconCalculator: { name: 'IconCalculator' },
  IconGift: { name: 'IconGift' },
  IconStar: { name: 'IconStar' },
  IconWallet: { name: 'IconWallet' },
  IconCreditCard: { name: 'IconCreditCard' },
  IconCheckCircle: { name: 'IconCheckCircle' },
  IconInfoCircle: { name: 'IconInfoCircle' },
  IconRefresh: { name: 'IconRefresh' }
}));

describe('PaymentCalculation', () => {
  let wrapper: any;
  let paymentStore: any;

  const mockCalculation: PaymentCalculationType = {
    originalAmount: 100.00,
    couponDiscount: 10.00,
    pointsUsed: 500,
    pointsValue: 5.00,
    balanceUsed: 20.00,
    onlinePayAmount: 65.00,
    finalAmount: 65.00,
    savings: 35.00
  };

  beforeEach(() => {
    setActivePinia(createPinia());
    paymentStore = usePaymentStore();
    
    // Mock store state
    paymentStore.selectedCoupons = [
      {
        couponId: 'COUPON_001',
        name: '新用户优惠券',
        type: 'amount',
        value: 10.00,
        minOrderAmount: 50.00,
        expireTime: '2024-12-31T23:59:59Z',
        description: '新用户专享10元优惠券',
        applicable: true
      }
    ];
    
    paymentStore.userAssets = {
      balance: 100.00,
      points: 1000,
      pointsRate: 100,
      availableCoupons: []
    };
  });

  afterEach(() => {
    if (wrapper) {
      wrapper.unmount();
    }
  });

  it('should render correctly with calculation data', () => {
    wrapper = mount(PaymentCalculation, {
      props: {
        calculation: mockCalculation,
        loading: false,
        error: null
      },
      global: {
        stubs: {
          'a-card': true,
          'a-spin': true,
          'a-descriptions': true,
          'a-descriptions-item': true,
          'a-divider': true,
          'a-alert': true,
          'a-button': true,
          'a-tag': true,
          'a-progress': true
        }
      }
    });

    expect(wrapper.exists()).toBe(true);
    expect(wrapper.find('.calculation-container').exists()).toBe(true);
  });

  it('should display loading state correctly', () => {
    wrapper = mount(PaymentCalculation, {
      props: {
        calculation: mockCalculation,
        loading: true,
        error: null
      },
      global: {
        stubs: {
          'a-card': true,
          'a-spin': true
        }
      }
    });

    expect(wrapper.find('.loading-container').exists()).toBe(true);
    expect(wrapper.find('.loading-text').text()).toContain('正在计算费用');
  });

  it('should format amounts correctly', () => {
    wrapper = mount(PaymentCalculation, {
      props: {
        calculation: mockCalculation,
        loading: false,
        error: null
      },
      global: {
        stubs: {
          'a-card': true,
          'a-descriptions': true,
          'a-descriptions-item': true,
          'a-divider': true,
          'a-alert': true,
          'a-tag': true
        }
      }
    });

    const vm = wrapper.vm;
    expect(vm.formatAmount(100.123)).toBe('100.12');
    expect(vm.formatAmount(0)).toBe('0.00');
    expect(vm.formatAmount(99.9)).toBe('99.90');
  });

  it('should format points correctly', () => {
    wrapper = mount(PaymentCalculation, {
      props: {
        calculation: mockCalculation,
        loading: false,
        error: null
      },
      global: {
        stubs: {
          'a-card': true,
          'a-descriptions': true,
          'a-descriptions-item': true,
          'a-divider': true,
          'a-alert': true,
          'a-tag': true
        }
      }
    });

    const vm = wrapper.vm;
    expect(vm.formatPoints(1000)).toBe('1,000积分');
    expect(vm.formatPoints(500)).toBe('500积分');
  });

  it('should show discounts section when has discounts', () => {
    wrapper = mount(PaymentCalculation, {
      props: {
        calculation: mockCalculation,
        loading: false,
        error: null
      },
      global: {
        stubs: {
          'a-card': true,
          'a-descriptions': true,
          'a-descriptions-item': true,
          'a-divider': true,
          'a-alert': true,
          'a-tag': true
        }
      }
    });

    const vm = wrapper.vm;
    expect(vm.hasDiscounts).toBe(true);
    expect(wrapper.find('.discount-section').exists()).toBe(true);
  });

  it('should show payment methods section when has payment methods', () => {
    wrapper = mount(PaymentCalculation, {
      props: {
        calculation: mockCalculation,
        loading: false,
        error: null
      },
      global: {
        stubs: {
          'a-card': true,
          'a-descriptions': true,
          'a-descriptions-item': true,
          'a-divider': true,
          'a-alert': true,
          'a-tag': true
        }
      }
    });

    const vm = wrapper.vm;
    expect(vm.hasPaymentMethods).toBe(true);
    expect(wrapper.find('.payment-methods-section').exists()).toBe(true);
  });

  it('should emit recalculate event when recalculate button is clicked', async () => {
    wrapper = mount(PaymentCalculation, {
      props: {
        calculation: mockCalculation,
        loading: false,
        error: 'Calculation error'
      },
      global: {
        stubs: {
          'a-card': true,
          'a-button': false,
          'a-descriptions': true,
          'a-descriptions-item': true,
          'a-divider': true,
          'a-alert': true,
          'a-tag': true
        }
      }
    });

    const recalculateButton = wrapper.find('.recalculate-section a-button');
    expect(recalculateButton.exists()).toBe(true);

    await recalculateButton.trigger('click');
    expect(wrapper.emitted('recalculate')).toBeTruthy();
    expect(wrapper.emitted('recalculate')).toHaveLength(1);
  });

  it('should show error state when has error', () => {
    wrapper = mount(PaymentCalculation, {
      props: {
        calculation: mockCalculation,
        loading: false,
        error: 'Calculation failed'
      },
      global: {
        stubs: {
          'a-card': true,
          'a-button': true,
          'a-descriptions': true,
          'a-descriptions-item': true,
          'a-divider': true,
          'a-alert': true,
          'a-tag': true
        }
      }
    });

    const vm = wrapper.vm;
    expect(vm.hasError).toBe(true);
    expect(wrapper.find('.recalculate-section').exists()).toBe(true);
  });

  it('should calculate savings correctly', () => {
    const calculation = {
      ...mockCalculation,
      couponDiscount: 15.00,
      pointsValue: 10.00,
      balanceUsed: 25.00,
      savings: 50.00
    };

    wrapper = mount(PaymentCalculation, {
      props: {
        calculation,
        loading: false,
        error: null
      },
      global: {
        stubs: {
          'a-card': true,
          'a-descriptions': true,
          'a-descriptions-item': true,
          'a-divider': true,
          'a-alert': true,
          'a-tag': true
        }
      }
    });

    expect(wrapper.find('.savings-info').exists()).toBe(true);
    expect(wrapper.find('.savings-text').text()).toContain('50.00');
  });

  it('should handle zero amounts correctly', () => {
    const zeroCalculation: PaymentCalculationType = {
      originalAmount: 0,
      couponDiscount: 0,
      pointsUsed: 0,
      pointsValue: 0,
      balanceUsed: 0,
      onlinePayAmount: 0,
      finalAmount: 0,
      savings: 0
    };

    wrapper = mount(PaymentCalculation, {
      props: {
        calculation: zeroCalculation,
        loading: false,
        error: null
      },
      global: {
        stubs: {
          'a-card': true,
          'a-descriptions': true,
          'a-descriptions-item': true,
          'a-divider': true,
          'a-alert': true,
          'a-tag': true
        }
      }
    });

    const vm = wrapper.vm;
    expect(vm.hasDiscounts).toBe(false);
    expect(vm.hasPaymentMethods).toBe(false);
    expect(wrapper.find('.discount-section').exists()).toBe(false);
    expect(wrapper.find('.payment-methods-section').exists()).toBe(false);
  });

  it('should show coupon count correctly', () => {
    // Add more coupons to test count display
    paymentStore.selectedCoupons = [
      {
        couponId: 'COUPON_001',
        name: '新用户优惠券',
        type: 'amount',
        value: 10.00,
        minOrderAmount: 50.00,
        expireTime: '2024-12-31T23:59:59Z',
        description: '新用户专享10元优惠券',
        applicable: true
      },
      {
        couponId: 'COUPON_002',
        name: '满减优惠券',
        type: 'amount',
        value: 5.00,
        minOrderAmount: 30.00,
        expireTime: '2024-12-31T23:59:59Z',
        description: '满30减5优惠券',
        applicable: true
      }
    ];

    wrapper = mount(PaymentCalculation, {
      props: {
        calculation: mockCalculation,
        loading: false,
        error: null
      },
      global: {
        stubs: {
          'a-card': true,
          'a-descriptions': true,
          'a-descriptions-item': true,
          'a-divider': true,
          'a-alert': true,
          'a-tag': true
        }
      }
    });

    const vm = wrapper.vm;
    expect(vm.selectedCoupons).toHaveLength(2);
  });
});
