<template>
  <div class="page-container">
    <!-- Hero Banner Section -->
    <div class="hero-section">
      <div class="content-wrapper">
        <h1 class="hero-title">计算机软件著作权登记服务</h1>
        <p class="hero-description">
          计算机软件著作权是指软件的开发者或者其他权利人依据有关著作权法律的规定，对于软件作品所享有的各项专有权利。本平台提供软件著作权登记和存证服务，保护您的软件作品权益。
        </p>
        <div class="hero-buttons">
          <!-- <a-button type="primary" size="large" class="hero-button" @click="goToEvidence">
            <span>立即存证</span>
            <icon-arrow-right />
          </a-button>
          <a-button type="outline" size="large" class="hero-button" @click="goToRegister">
            <span>立即登记</span>
            <icon-arrow-right />
          </a-button> -->
          <a-button type="primary" size="large" class="hero-button" @click="goToRegister">
            <span>立即登记</span>
            <icon-arrow-right />
          </a-button>
        </div>
      </div>
    </div>

    <!-- Public Announcement Section -->
    <div class="section public-announcement">
      <div class="section-title">
        <h2>软件著作权登记公示</h2>
      </div>
      <div class="announcement-list">
        <a-card v-for="item in announcementList" :key="item.id" class="announcement-card">
          <template #title>
            <div class="card-title">
              {{ item.title }}
            </div>
          </template>
          <template #extra>
            <a-button type="text" @click="viewDetails(item.id)"> 详情 </a-button>
          </template>
          <div class="card-content">
            <div class="card-item">
              <span class="label">申请编号：</span>
              <span class="value">{{ item.applicationNumber }}</span>
            </div>
            <div class="card-item">
              <span class="label">公示截止日期：</span>
              <span class="value">{{ item.endDate }}</span>
            </div>
            <div class="card-item">
              <span class="label">申请主体：</span>
              <span class="value">{{ item.applicant }}</span>
            </div>
            <div class="card-status">
              <a-tag color="blue">
                {{ item.status }}
              </a-tag>
            </div>
          </div>
        </a-card>
        <div class="view-more">
          <a-button type="text" @click="viewMoreAnnouncements">
            查看更多
            <icon-arrow-right />
          </a-button>
        </div>
      </div>
    </div>

    <!-- Platform Introduction Section -->
    <div class="section platform-intro">
      <div class="section-title">
        <h2>平台介绍</h2>
      </div>
      <div class="intro-content">
        <p>
          文创链软件著作权登记平台为软件开发者和企业提供便捷的软件著作权登记服务，全流程线上办理，快速高效。
        </p>
        <p>
          平台集"软件存证—登记申请—公示公告—签发证书—备案管理"功能于一体，实现登记及时受理、进度随时查询、结果实时反馈、过程安全可靠，为您的软件作品提供全方位的保护。
        </p>
      </div>
    </div>

    <!-- Registration Process Section -->
    <div class="section registration-process">
      <div class="section-title">
        <h2>登记流程</h2>
      </div>
      <div class="process-steps">
        <a-steps>
          <a-step title="用户注册">
            <template #description>
              <p>注册账号并完成实名认证</p>
            </template>
          </a-step>
          <a-step title="提交申请">
            <template #description>
              <p>填写软件信息并上传相关文档</p>
            </template>
          </a-step>
          <a-step title="材料审核">
            <template #description>
              <p>平台审核材料合规性</p>
            </template>
          </a-step>
          <a-step title="公示阶段">
            <template #description>
              <p>软件信息进入公示期</p>
            </template>
          </a-step>
          <a-step title="证书颁发">
            <template #description>
              <p>审核通过后颁发登记证书</p>
            </template>
          </a-step>
        </a-steps>
      </div>
    </div>

    <!-- Application Scenarios Section -->
    <div class="section application-scenarios">
      <div class="section-title">
        <h2>应用场景</h2>
      </div>
      <div class="scenarios-container">
        <a-row :gutter="24">
          <a-col :span="12">
            <div class="scenario-card">
              <div class="scenario-icon">
                <icon-file />
              </div>
              <div class="scenario-content">
                <h3>软件存证</h3>
                <p>
                  用于对企业或个人开发的软件程序进行存证，保障软件代码、文档及相关资料的真实性和完整性，为软件著作权保护提供可靠的技术支持和法律依据。
                </p>
                <a-button type="primary" @click="goToEvidence"> 立即使用 </a-button>
              </div>
            </div>
          </a-col>
          <a-col :span="12">
            <div class="scenario-card">
              <div class="scenario-icon">
                <icon-copyright />
              </div>
              <div class="scenario-content">
                <h3>软件著作权登记</h3>
                <p>
                  软件著作权登记证书是软件著作权的法律凭证，可用于软件产品销售、许可授权、投融资、知识产权诉讼等场景，有效保障软件开发者的合法权益。
                </p>
                <a-button type="primary" @click="goToRegister"> 立即使用 </a-button>
              </div>
            </div>
          </a-col>
        </a-row>
      </div>
    </div>

    <!-- Policy Support Section -->
    <div class="section policy-support">
      <div class="section-title">
        <h2>政策支持</h2>
      </div>
      <div class="policy-content">
        <a-collapse accordion>
          <a-collapse-item key="1" header="《中华人民共和国著作权法》">
            <p>
              根据《著作权法》，软件著作权自软件开发完成之日起产生。软件著作权人可以向国家著作权行政管理部门认定的软件登记机构办理登记。登记可以作为著作权人确认和权属纠纷处理的初步证据。
            </p>
          </a-collapse-item>
          <a-collapse-item key="2" header="《计算机软件保护条例》">
            <p>
              《计算机软件保护条例》明确规定了软件著作权的内容、归属、期限以及软件登记的相关程序，为软件著作权的保护提供了具体的法律依据。
            </p>
          </a-collapse-item>
          <a-collapse-item key="3" header="《计算机软件著作权登记办法》">
            <p>
              《计算机软件著作权登记办法》详细规定了软件著作权登记的申请材料、登记流程、证书颁发等事项，是软件著作权登记的具体操作指南。
            </p>
          </a-collapse-item>
        </a-collapse>
      </div>
    </div>

    <!-- FAQ Section -->
    <div class="section faq">
      <div class="section-title">
        <h2>常见问题</h2>
      </div>
      <div class="faq-content">
        <a-collapse accordion>
          <a-collapse-item key="1" header="什么是软件著作权？">
            <p>
              软件著作权是指软件开发者或其他权利人对软件享有的各项专有权利，包括发表权、署名权、修改权、复制权、发行权、出租权、信息网络传播权、翻译权等。
            </p>
          </a-collapse-item>
          <a-collapse-item key="2" header="软件著作权登记有什么好处？">
            <p>
              软件著作权登记可以作为软件著作权归属的初步证据，有利于维护著作权人的合法权益，预防和解决著作权纠纷。同时，登记证书也是软件产品销售、许可授权、投融资等商业活动的重要凭证。
            </p>
          </a-collapse-item>
          <a-collapse-item key="3" header="软件著作权登记需要哪些材料？">
            <p>
              一般需要提供申请表、软件说明书、源代码（连续前后各30页）、身份证明文件（个人或企业）等材料。具体要求可参考登记申请页面的指引。
            </p>
          </a-collapse-item>
          <a-collapse-item key="4" header="软件著作权登记多久能完成？">
            <p>
              在材料齐全、符合要求的情况下，从申请到获得登记证书一般需要15-30个工作日。通过本平台申请可以实时查询进度，提高登记效率。
            </p>
          </a-collapse-item>
        </a-collapse>
      </div>
    </div>

    <!-- Contact Us Section -->
    <div class="section contact-us">
      <div class="section-title">
        <h2>联系我们</h2>
      </div>
      <div class="contact-content">
        <div class="contact-item">
          <div class="contact-label">存证、实名认证咨询:</div>
          <div class="contact-value">18081067661</div>
        </div>
        <div class="contact-item">
          <div class="contact-label">登记咨询:</div>
          <div class="contact-value">(028) 8605 8625（工作日接听）、18081067661</div>
        </div>
        <div class="contact-item">
          <div class="contact-label">登记审查:</div>
          <div class="contact-value">(028) 8605 8625（工作日接听）</div>
        </div>
        <div class="contact-item">
          <div class="contact-label">技术支持:</div>
          <div class="contact-value">18081067661</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref, onMounted } from 'vue';
  import { useRouter } from 'vue-router';
  import {
    IconArrowRight,
    IconFile,
    IconCopyright,
    IconCheckCircle,
    IconSearch,
  } from '@arco-design/web-vue/es/icon';
  import {
    getSoftwareCopyrightList,
    getSoftwareCopyrightPolicies,
    getSoftwareCopyrightFAQs,
  } from '@/http/api/software';

  // 定义接口类型
  interface Announcement {
    id: string;
    title: string;
    applicationNumber: string;
    endDate: string;
    applicant: string;
    status: string;
  }

  interface Policy {
    id: string;
    title: string;
    content: string;
  }

  interface FAQ {
    id: string;
    question: string;
    answer: string;
  }

  // API 响应类型
  interface ApiResponse<T> {
    list: T[];
    total?: number;
    [key: string]: any;
  }

  const router = useRouter();

  // 数据声明
  const announcementList = ref<Announcement[]>([]);
  const policyList = ref<Policy[]>([]);
  const faqList = ref<FAQ[]>([]);
  const loading = ref(false);

  // 页面方法
  const goToEvidence = () => {
    router.push('/software/evidence');
  };

  const goToRegister = () => {
    router.push('/software/register');
  };

  const viewDetails = (id: string) => {
    router.push(`/software/details/${id}`);
  };

  const viewMoreAnnouncements = () => {
    router.push('/software/announcements');
  };

  // 获取公示列表数据
  const fetchAnnouncementList = async () => {
    try {
      loading.value = true;
      // 注释掉API调用，直接使用模拟数据
      // const res = await getSoftwareCopyrightList({ page: 1, size: 3 });
      // if (res && res.list) {
      //   announcementList.value = res.list;
      // } else {
      // 使用示例数据
      announcementList.value = [
        {
          id: '1',
          title: '某企业管理系统V1.0',
          applicationNumber: 'SW2023-0001',
          endDate: '2023-12-31',
          applicant: '某科技有限公司',
          status: '公示中',
        },
        {
          id: '2',
          title: '智能分析平台V2.1',
          applicationNumber: 'SW2023-0002',
          endDate: '2023-12-25',
          applicant: '某信息技术有限公司',
          status: '公示中',
        },
        {
          id: '3',
          title: '在线学习软件V3.0',
          applicationNumber: 'SW2023-0003',
          endDate: '2023-12-20',
          applicant: '某教育科技有限公司',
          status: '公示中',
        },
      ];
      // }
    } catch (error) {
      console.error('获取公示列表失败:', error);
      // 使用示例数据作为备份
      announcementList.value = [
        {
          id: '1',
          title: '某企业管理系统V1.0',
          applicationNumber: 'SW2023-0001',
          endDate: '2023-12-31',
          applicant: '某科技有限公司',
          status: '公示中',
        },
        {
          id: '2',
          title: '智能分析平台V2.1',
          applicationNumber: 'SW2023-0002',
          endDate: '2023-12-25',
          applicant: '某信息技术有限公司',
          status: '公示中',
        },
        {
          id: '3',
          title: '在线学习软件V3.0',
          applicationNumber: 'SW2023-0003',
          endDate: '2023-12-20',
          applicant: '某教育科技有限公司',
          status: '公示中',
        },
      ];
    } finally {
      loading.value = false;
    }
  };

  // 获取政策数据
  const fetchPolicyList = async () => {
    try {
      // API尚未实现，暂不获取数据
      // const res = await getSoftwareCopyrightPolicies();
      // if (res && res.list) {
      //   policyList.value = res.list;
      // }
      policyList.value = [];
    } catch (error) {
      console.error('获取政策列表失败:', error);
      policyList.value = [];
    }
  };

  // 获取FAQ数据
  const fetchFAQList = async () => {
    try {
      // API尚未实现，暂不获取数据
      // const res = await getSoftwareCopyrightFAQs();
      // if (res && res.list) {
      //   faqList.value = res.list;
      // }
      faqList.value = [];
    } catch (error) {
      console.error('获取FAQ列表失败:', error);
      faqList.value = [];
    }
  };

  onMounted(() => {
    fetchAnnouncementList();
    // fetchPolicyList();
    // fetchFAQList();
  });
</script>

<style lang="scss" scoped>
  .page-container {
    width: 100%;
    max-width: 100vw;
    overflow-x: hidden;
  }

  .hero-section {
    background-color: #f5f7fa;
    padding: 80px 0;
    position: relative;

    .content-wrapper {
      max-width: 1200px;
      margin: 0 auto;
      padding: 0 20px;

      .hero-title {
        font-size: 2.5rem;
        font-weight: bold;
        margin-bottom: 20px;
        color: #1d2129;
      }

      .hero-description {
        font-size: 1.1rem;
        line-height: 1.6;
        color: #4e5969;
        max-width: 700px;
        margin-bottom: 30px;
      }

      .hero-buttons {
        display: flex;
        gap: 20px;

        .hero-button {
          display: flex;
          align-items: center;
          gap: 8px;
        }
      }
    }
  }

  .section {
    padding: 60px 0;
    max-width: 1200px;
    margin: 0 auto;

    .section-title {
      text-align: center;
      margin-bottom: 40px;

      h2 {
        font-size: 2rem;
        font-weight: bold;
        color: #1d2129;
        position: relative;
        display: inline-block;

        &:after {
          content: '';
          display: block;
          width: 50px;
          height: 4px;
          background-color: #165dff;
          margin: 15px auto 0;
        }
      }
    }
  }

  .public-announcement {
    .announcement-list {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: 20px;

      @media (max-width: 768px) {
        grid-template-columns: 1fr;
      }

      .announcement-card {
        border-radius: 8px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
        transition:
          transform 0.3s ease,
          box-shadow 0.3s ease;

        &:hover {
          transform: translateY(-5px);
          box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        .card-title {
          font-weight: bold;
          font-size: 1.1rem;
        }

        .card-content {
          padding: 10px 0;

          .card-item {
            margin-bottom: 8px;
            display: flex;

            .label {
              color: #86909c;
              margin-right: 8px;
              flex-shrink: 0;
            }

            .value {
              color: #1d2129;
            }
          }

          .card-status {
            margin-top: 15px;
          }
        }
      }

      .view-more {
        grid-column: 1 / -1;
        text-align: center;
        margin-top: 20px;
      }
    }
  }

  .platform-intro {
    .intro-content {
      max-width: 800px;
      margin: 0 auto;
      text-align: center;

      p {
        margin-bottom: 20px;
        line-height: 1.7;
        color: #4e5969;
        font-size: 1.1rem;
      }
    }
  }

  .registration-process {
    .process-steps {
      max-width: 900px;
      margin: 0 auto;
    }
  }

  .application-scenarios {
    .scenarios-container {
      .scenario-card {
        display: flex;
        background-color: #f5f7fa;
        border-radius: 8px;
        padding: 30px;
        height: 100%;
        transition:
          transform 0.3s ease,
          box-shadow 0.3s ease;

        &:hover {
          transform: translateY(-5px);
          box-shadow: 0 10px 20px rgba(0, 0, 0, 0.05);
        }

        .scenario-icon {
          font-size: 2.5rem;
          color: #165dff;
          margin-right: 20px;
          display: flex;
          align-items: center;
        }

        .scenario-content {
          flex: 1;

          h3 {
            font-size: 1.3rem;
            font-weight: bold;
            margin-bottom: 15px;
            color: #1d2129;
          }

          p {
            color: #4e5969;
            margin-bottom: 20px;
            line-height: 1.6;
          }
        }
      }
    }
  }

  .policy-support,
  .faq {
    .policy-content,
    .faq-content {
      max-width: 800px;
      margin: 0 auto;

      p {
        color: #4e5969;
        line-height: 1.6;
      }
    }
  }

  .contact-us {
    .contact-content {
      max-width: 600px;
      margin: 0 auto;

      .contact-item {
        display: flex;
        margin-bottom: 15px;

        .contact-label {
          flex: 0 0 180px;
          font-weight: bold;
          color: #1d2129;
        }

        .contact-value {
          flex: 1;
          color: #4e5969;
        }
      }
    }
  }
</style>
