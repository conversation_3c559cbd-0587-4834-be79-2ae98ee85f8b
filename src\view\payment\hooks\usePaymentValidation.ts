/** @format */

import { ref, computed } from 'vue';
import { usePaymentStore } from '@/store/modules/payment';
import type { PaymentMethod, CouponInfo } from '@/http/api/payment/types';

/**
 * 支付验证Hook
 */
export function usePaymentValidation() {
  const paymentStore = usePaymentStore();

  // 验证状态
  const validationErrors = ref<string[]>([]);
  const isValidating = ref(false);

  // 计算属性
  const hasValidationErrors = computed(() => validationErrors.value.length > 0);
  const isPaymentValid = computed(() => !hasValidationErrors.value && paymentStore.isPaymentValid);

  /**
   * 验证订单信息
   */
  function validateOrder(): boolean {
    const errors: string[] = [];
    const order = paymentStore.currentOrder;

    if (!order) {
      errors.push('订单信息不存在');
      return false;
    }

    // 验证订单状态
    if (order.status !== 'created') {
      errors.push('订单状态异常，无法支付');
    }

    // 验证订单金额
    if (order.finalAmount <= 0) {
      errors.push('订单金额无效');
    }

    // 验证订单是否过期
    const expireTime = new Date(order.expireTime).getTime();
    const currentTime = new Date().getTime();
    
    if (currentTime > expireTime) {
      errors.push('订单已过期');
    }

    validationErrors.value = errors;
    return errors.length === 0;
  }

  /**
   * 验证支付方式选择
   */
  function validatePaymentMethods(): boolean {
    const errors: string[] = [];
    const selectedMethods = paymentStore.selectedMethods;
    const calculation = paymentStore.paymentCalculation;

    // 检查是否选择了支付方式
    if (selectedMethods.length === 0) {
      errors.push('请选择支付方式');
    }

    // 验证各种支付方式
    for (const method of selectedMethods) {
      const methodErrors = validateSinglePaymentMethod(method);
      errors.push(...methodErrors);
    }

    // 验证支付金额
    if (calculation.finalAmount < 0) {
      errors.push('支付金额计算错误');
    }

    // 验证在线支付金额
    const hasOnlinePayment = selectedMethods.some(m => m.type === 'wechat' || m.type === 'alipay');
    if (hasOnlinePayment && calculation.onlinePayAmount <= 0) {
      errors.push('在线支付金额必须大于0');
    }

    validationErrors.value = errors;
    return errors.length === 0;
  }

  /**
   * 验证单个支付方式
   */
  function validateSinglePaymentMethod(method: PaymentMethod): string[] {
    const errors: string[] = [];
    const userAssets = paymentStore.userAssets;

    switch (method.type) {
      case 'balance':
        if (!userAssets || (method.amount || 0) > userAssets.balance) {
          errors.push('余额不足');
        }
        if ((method.amount || 0) <= 0) {
          errors.push('余额使用金额必须大于0');
        }
        break;

      case 'points':
        if (!userAssets || (method.amount || 0) > userAssets.points) {
          errors.push('积分不足');
        }
        if ((method.amount || 0) <= 0) {
          errors.push('积分使用数量必须大于0');
        }
        break;

      case 'wechat':
      case 'alipay':
        if (!method.enabled) {
          errors.push(`${method.name}支付暂不可用`);
        }
        if ((method.amount || 0) <= 0) {
          errors.push(`${method.name}支付金额必须大于0`);
        }
        // 检查最小支付金额
        if (method.minAmount && (method.amount || 0) < method.minAmount) {
          errors.push(`${method.name}最小支付金额为${method.minAmount}元`);
        }
        // 检查最大支付金额
        if (method.maxAmount && (method.amount || 0) > method.maxAmount) {
          errors.push(`${method.name}最大支付金额为${method.maxAmount}元`);
        }
        break;

      case 'coupon':
        // 优惠券验证在选择时已完成
        break;
    }

    return errors;
  }

  /**
   * 验证优惠券
   */
  function validateCoupons(): boolean {
    const errors: string[] = [];
    const selectedCoupons = paymentStore.selectedCoupons;
    const orderAmount = paymentStore.currentOrder?.finalAmount || 0;

    for (const coupon of selectedCoupons) {
      const couponErrors = validateSingleCoupon(coupon, orderAmount);
      errors.push(...couponErrors);
    }

    // 检查优惠券组合是否合理
    const totalDiscount = selectedCoupons.reduce((total, coupon) => {
      return total + calculateCouponDiscount(coupon, orderAmount);
    }, 0);

    if (totalDiscount > orderAmount) {
      errors.push('优惠券折扣金额超过订单金额');
    }

    validationErrors.value = errors;
    return errors.length === 0;
  }

  /**
   * 验证单个优惠券
   */
  function validateSingleCoupon(coupon: CouponInfo, orderAmount: number): string[] {
    const errors: string[] = [];

    // 检查最小订单金额
    if (orderAmount < coupon.minOrderAmount) {
      errors.push(`优惠券"${coupon.name}"要求订单金额不少于${coupon.minOrderAmount}元`);
    }

    // 检查是否过期
    const expireTime = new Date(coupon.expireTime).getTime();
    const currentTime = new Date().getTime();
    
    if (currentTime > expireTime) {
      errors.push(`优惠券"${coupon.name}"已过期`);
    }

    // 检查是否适用
    if (!coupon.applicable) {
      errors.push(`优惠券"${coupon.name}"不适用于当前订单`);
    }

    return errors;
  }

  /**
   * 计算优惠券折扣金额
   */
  function calculateCouponDiscount(coupon: CouponInfo, orderAmount: number): number {
    if (coupon.type === 'amount') {
      return Math.min(coupon.value, orderAmount);
    } else if (coupon.type === 'discount') {
      const discountAmount = orderAmount * (1 - coupon.value);
      return Math.min(discountAmount, coupon.maxDiscountAmount || discountAmount);
    }
    return 0;
  }

  /**
   * 验证用户资产
   */
  function validateUserAssets(): boolean {
    const errors: string[] = [];
    const userAssets = paymentStore.userAssets;

    if (!userAssets) {
      errors.push('无法获取用户资产信息');
      validationErrors.value = errors;
      return false;
    }

    // 验证余额
    if (userAssets.balance < 0) {
      errors.push('用户余额异常');
    }

    // 验证积分
    if (userAssets.points < 0) {
      errors.push('用户积分异常');
    }

    validationErrors.value = errors;
    return errors.length === 0;
  }

  /**
   * 验证支付环境
   */
  function validatePaymentEnvironment(): boolean {
    const errors: string[] = [];

    // 检查网络连接
    if (!navigator.onLine) {
      errors.push('网络连接异常，请检查网络后重试');
    }

    // 检查浏览器支持
    if (!window.crypto || !window.crypto.subtle) {
      errors.push('浏览器不支持安全支付，请升级浏览器');
    }

    // 检查HTTPS
    if (location.protocol !== 'https:' && location.hostname !== 'localhost') {
      errors.push('请在安全环境下进行支付');
    }

    validationErrors.value = errors;
    return errors.length === 0;
  }

  /**
   * 全面验证支付信息
   */
  async function validatePayment(): Promise<boolean> {
    isValidating.value = true;
    validationErrors.value = [];

    try {
      // 依次验证各个方面
      const validations = [
        validatePaymentEnvironment(),
        validateOrder(),
        validateUserAssets(),
        validateCoupons(),
        validatePaymentMethods()
      ];

      const isValid = validations.every(result => result);
      
      return isValid;
      
    } catch (error) {
      console.error('支付验证失败:', error);
      validationErrors.value.push('支付验证过程中发生错误');
      return false;
    } finally {
      isValidating.value = false;
    }
  }

  /**
   * 获取验证错误信息
   */
  function getValidationErrorMessage(): string {
    return validationErrors.value.join('；');
  }

  /**
   * 清除验证错误
   */
  function clearValidationErrors() {
    validationErrors.value = [];
  }

  /**
   * 检查特定类型的错误
   */
  function hasErrorType(errorKeyword: string): boolean {
    return validationErrors.value.some(error => error.includes(errorKeyword));
  }

  /**
   * 实时验证支付方式
   */
  function validatePaymentMethodRealtime(method: PaymentMethod): { valid: boolean; errors: string[] } {
    const errors = validateSinglePaymentMethod(method);
    return {
      valid: errors.length === 0,
      errors
    };
  }

  /**
   * 实时验证优惠券
   */
  function validateCouponRealtime(coupon: CouponInfo): { valid: boolean; errors: string[] } {
    const orderAmount = paymentStore.currentOrder?.finalAmount || 0;
    const errors = validateSingleCoupon(coupon, orderAmount);
    return {
      valid: errors.length === 0,
      errors
    };
  }

  return {
    // 状态
    validationErrors,
    isValidating,
    
    // 计算属性
    hasValidationErrors,
    isPaymentValid,
    
    // 方法
    validateOrder,
    validatePaymentMethods,
    validateCoupons,
    validateUserAssets,
    validatePaymentEnvironment,
    validatePayment,
    validatePaymentMethodRealtime,
    validateCouponRealtime,
    getValidationErrorMessage,
    clearValidationErrors,
    hasErrorType
  };
}
