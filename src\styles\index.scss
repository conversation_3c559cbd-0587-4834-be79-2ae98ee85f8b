// 使用现代 Sass 模块系统导入变量
@use 'sass:color';
@use './_variables.scss' as *;

// 全局样式
html,
body {
  overflow-x: hidden;
}

body {
  font-family: $font-family;
  font-size: $font-size-base;
  color: $text-color;
  background-color: $background-color;
  line-height: 1.5;
  margin: 0;
  padding: 0;
}

// 常用工具类
.container {
  max-width: $container-width;
  margin: 0 auto;
  padding: 0 $spacing-md;
}

// 响应式工具类
@media (max-width: $screen-md) {
  .container {
    padding: 0 $spacing-sm;
  }
}

// ==================== 按钮组件样式 ====================
.btn-primary {
  background-color: $primary-color;
  color: $text-color-inverse;
  border-radius: $border-radius-base;
  padding: $spacing-sm $spacing-md;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    background-color: $primary-light;
    transform: translateY(-1px);
    box-shadow: $box-shadow-base;
  }

  &:active {
    background-color: $primary-dark;
    transform: translateY(0);
  }

  &:disabled {
    background-color: $text-color-disabled;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
  }
}

.btn-secondary {
  background-color: $secondary-color;
  color: $text-color-inverse;
  border-radius: $border-radius-base;
  padding: $spacing-sm $spacing-md;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    background-color: $secondary-light;
  }

  &:active {
    background-color: $secondary-dark;
  }
}

.btn-outline {
  background-color: transparent;
  color: $primary-color;
  border: 1px solid $primary-color;
  border-radius: $border-radius-base;
  padding: $spacing-sm $spacing-md;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    background-color: $primary-color;
    color: $text-color-inverse;
  }
}

// ==================== 状态色工具类 ====================
.text-success {
  color: $success-color;
}

.text-warning {
  color: $warning-color;
}

.text-error {
  color: $error-color;
}

.text-info {
  color: $info-color;
}

.bg-success {
  background-color: $success-color;
}

.bg-warning {
  background-color: $warning-color;
}

.bg-error {
  background-color: $error-color;
}

.bg-info {
  background-color: $info-color;
}

// ==================== 链接样式 ====================
.link {
  color: $link-color;
  text-decoration: none;
  transition: color 0.2s ease;

  &:hover {
    color: $link-hover-color;
    text-decoration: underline;
  }

  &:active {
    color: $link-active-color;
  }

  &:visited {
    color: $link-visited-color;
  }
}

// ==================== 卡片组件 ====================
.card {
  background-color: $background-color;
  border: 1px solid $border-color;
  border-radius: $border-radius-lg;
  padding: $spacing-lg;
  box-shadow: $box-shadow-sm;
  transition: box-shadow 0.2s ease;

  &:hover {
    box-shadow: $box-shadow-base;
  }
}

.card-header {
  border-bottom: 1px solid $border-color-light;
  padding-bottom: $spacing-md;
  margin-bottom: $spacing-md;
}

// ==================== 表单组件 ====================
.form-input {
  border: 1px solid $border-color;
  border-radius: $border-radius-base;
  padding: $spacing-sm $spacing-md;
  font-size: $font-size-base;
  color: $text-color;
  background-color: $background-color;
  transition: border-color 0.2s ease;

  &:focus {
    outline: none;
    border-color: $border-color-focus;
    box-shadow: 0 0 0 2px rgba(22, 93, 255, 0.1);
  }

  &:disabled {
    background-color: $background-color-tertiary;
    color: $text-color-disabled;
    cursor: not-allowed;
  }

  &.error {
    border-color: $error-color;
  }

  &.success {
    border-color: $success-color;
  }
}

// 布局工具类
.flex {
  display: flex;
}

.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.flex-column {
  display: flex;
  flex-direction: column;
}

// 间距工具类
.mb-sm {
  margin-bottom: $spacing-sm;
}
.mb-md {
  margin-bottom: $spacing-md;
}
.mb-lg {
  margin-bottom: $spacing-lg;
}

.mt-sm {
  margin-top: $spacing-sm;
}
.mt-md {
  margin-top: $spacing-md;
}
.mt-lg {
  margin-top: $spacing-lg;
}

.p-sm {
  padding: $spacing-sm;
}
.p-md {
  padding: $spacing-md;
}
.p-lg {
  padding: $spacing-lg;
}

// 文本工具类
.text-primary {
  color: $primary-color;
}
.text-secondary {
  color: $secondary-color;
}
.text-success {
  color: $success-color;
}
.text-warning {
  color: $warning-color;
}
.text-error {
  color: $error-color;
}

.text-center {
  text-align: center;
}
.text-right {
  text-align: right;
}
.text-left {
  text-align: left;
}

.text-sm {
  font-size: $font-size-sm;
}
.text-base {
  font-size: $font-size-base;
}
.text-lg {
  font-size: $font-size-lg;
}
.text-xl {
  font-size: $font-size-xl;
}
.text-xxl {
  font-size: $font-size-xxl;
}
