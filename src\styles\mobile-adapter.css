/* 移动设备适配辅助样式 */

/* 移动设备导航栏样式 */
.mobile-menu-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  display: none;
}

.mobile-menu-container.active {
  display: block;
}

/* 移动设备内容适配 */
.mobile-padding {
  padding-left: 1rem;
  padding-right: 1rem;
}

/* 移动设备文本调整 */
@media (max-width: 767px) {
  h1 {
    font-size: 1.75rem !important;
  }

  h2 {
    font-size: 1.5rem !important;
  }

  h3 {
    font-size: 1.25rem !important;
  }

  p {
    font-size: 0.875rem !important;
  }
}

/* 移动设备按钮样式调整 */
@media (max-width: 767px) {
  .mobile-full-width {
    width: 100% !important;
    margin-left: 0 !important;
    margin-right: 0 !important;
  }

  .mobile-stack {
    display: flex !important;
    flex-direction: column !important;
  }

  .mobile-stack > * {
    margin-bottom: 0.5rem !important;
  }

  .mobile-stack > *:last-child {
    margin-bottom: 0 !important;
  }
}

/* 移动设备表格适配 */
@media (max-width: 767px) {
  .mobile-scroll-table {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }
}

/* 移动设备图片适配 */
.img-fluid {
  max-width: 100%;
  height: auto;
}

/* 移动设备隐藏/显示元素 */
.hide-on-mobile {
  display: block;
}

.show-on-mobile {
  display: none;
}

@media (max-width: 767px) {
  .hide-on-mobile {
    display: none !important;
  }

  .show-on-mobile {
    display: block !important;
  }
}
