/** @format */

import { createRouter, createWebHashHistory, RouteRecordRaw } from 'vue-router';
import NProgress from 'nprogress'; // progress bar
import 'nprogress/nprogress.css';

NProgress.configure({ showSpinner: false }); // NProgress Configuration

const routes: RouteRecordRaw[] = [
  {
    path: '/',
    redirect: '/home',
  },
  {
    path: '/home',
    name: 'home',
    meta: {
      header: 'home',
      requiresAuth: false,
      keepAlive: true,
      title: '文创链开放平台 - 数字资产全产业链服务平台',
      description:
        '文创链开放平台提供数据知识产权存证、区块链应用、文化数据服务一站式解决方案，助力文化创意产业数字化转型。',
    },
    component: () => import('@/view/home.vue'),
  },
  {
    path: '/products',
    meta: {
      header: 'products',
      title: '产品大全 - 文创链开放平台',
      description:
        '文创链开放平台产品中心，提供区块链存证、知识产权登记等产品服务，满足不同场景的数字文创需求。',
    },
    redirect: '/products/list',
    component: () => import('@/view/products/index.vue'),
    children: [
      {
        path: 'list',
        component: () => import('@/view/products/list.vue'),
        meta: {
          title: '产品列表 - 文创链开放平台',
          description:
            '浏览文创链开放平台全部产品，包括数据存证、知识产权保护、区块链应用等多种数字文创解决方案。',
        },
      },
      {
        path: 'detail',
        component: () => import('@/view/products/detail.vue'),
        meta: {
          title: '产品详情 - 文创链开放平台',
          description: '文创链开放平台产品详细介绍，了解产品功能、特点、应用场景和价值。',
        },
      },
    ],
  },
  {
    path: '/works',
    redirect: '/works/page',
    meta: {
      header: 'works-copyright',
      title: '作品著作权登记 - 文创链开放平台',
      description:
        '文创链提供作品著作权登记服务，保护文学、艺术和科学作品，维护创作者知识产权权益。',
    },
    component: () => import('@/view/works/index.vue'),
    children: [
      {
        path: 'page',
        component: () => import('@/view/works/home.vue'),
        meta: {
          title: '作品著作权服务 - 文创链开放平台',
          description: '文创链作品著作权登记服务，保护创作者权益，提供便捷的登记流程和法律保障。',
        },
      },
      {
        path: 'evidence',
        component: () => import('@/view/works/evidence.vue'),
        meta: {
          title: '作品著作权存证 - 文创链开放平台',
          description: '文创链作品著作权存证服务，通过区块链技术对作品著作权进行可信存证和保护。',
        },
      },
      {
        path: 'register',
        component: () => import('@/view/works/register.vue'),
        meta: {
          title: '作品著作权登记 - 文创链开放平台',
          description: '文创链作品著作权登记服务，提供便捷高效的作品著作权登记流程。',
        },
      },
      {
        path: 'details/:id',
        component: () => import('@/view/works/details.vue'),
        meta: {
          title: '作品著作权详情 - 文创链开放平台',
          description: '查看作品著作权详细信息、登记状态和公示内容。',
        },
      },
    ],
  },
  {
    path: '/evidence',
    redirect: '/evidence/page',
    meta: {
      header: 'blockchain-evidence',
      title: '区块链存证 - 文创链开放平台',
      description: '文创链提供区块链存证服务，为数字资产提供不可篡改的时间戳证明和版权保护。',
    },
    component: () => import('@/view/evidence/index.vue'),
    children: [
      {
        path: 'page',
        component: () => import('@/view/evidence/home.vue'),
        meta: {
          title: '区块链存证服务 - 文创链开放平台',
          description: '文创链区块链存证服务，为数字资产提供可信时间戳和不可篡改的存证证明。',
        },
      },
      {
        path: 'submit',
        component: () => import('@/view/evidence/submit.vue'),
        meta: {
          title: '区块链存证申请 - 文创链开放平台',
          description: '文创链区块链存证申请，快速为您的数字资产提供区块链存证服务。',
        },
      },
      {
        path: 'query',
        component: () => import('@/view/evidence/query.vue'),
        meta: {
          title: '区块链存证查询 - 文创链开放平台',
          description: '查询和验证区块链存证记录，确认数字资产的存证状态和真实性。',
        },
      },
      {
        path: 'details/:id',
        component: () => import('@/view/evidence/details.vue'),
        meta: {
          title: '区块链存证详情 - 文创链开放平台',
          description: '查看区块链存证详细信息、区块链凭证和验证结果。',
        },
      },
    ],
  },
  {
    path: '/api',
    redirect: '/api/list',
    meta: {
      header: 'api',
      title: 'API开放文档 - 文创链开放平台',
      description:
        '文创链开放平台API文档，提供详细的接口说明和调用示例，帮助开发者快速接入平台服务。',
    },
    component: () => import('@/view/api/index.vue'),
    children: [
      {
        path: 'list',
        component: () => import('@/view/api/list.vue'),
        meta: {
          title: 'API列表 - 文创链开放平台',
          description: '文创链开放平台API接口列表，包含各类数据存证、区块链查询等功能接口。',
        },
      },
      {
        path: 'detail',
        component: () => import('@/view/api/detail.vue'),
        meta: {
          title: 'API详情 - 文创链开放平台',
          description: '文创链开放平台API接口详细说明，包括请求参数、返回结果和调用示例。',
        },
      },
    ],
  },
  {
    path: '/login',
    name: 'login',
    component: () => import('@/view/login/index.vue'),
    meta: {
      requiresAuth: false,
    },
  },
  {
    path: '/authentication',
    name: 'authentication',
    component: () => import('@/view/authentication/index.vue'),
    meta: {
      requiresAuth: false,
    },
  },
  {
    path: '/uacAuthentication',
    name: 'uacAuthentication',
    component: () => import('@/view/authentication/uacAuthentication.vue'),
    meta: {
      requiresAuth: false,
    },
  },
  {
    path: '/helibaoAuthentication',
    name: 'helibaoAuthentication',
    component: () => import('@/view/authentication/helibaoAuthentication.vue'),
    meta: {
      requiresAuth: false,
    },
  },
  {
    path: '/register',
    name: 'register',
    component: () => import('@/view/login/register.vue'),
    meta: {
      requiresAuth: false,
    },
  },
  {
    path: '/forget',
    name: 'forget',
    component: () => import('@/view/login/forget.vue'),
    meta: {
      requiresAuth: false,
    },
  },
  {
    path: '/apiDoc',
    name: 'apiDoc',
    component: () => import('@/view/apiFiles/doc.vue'),
    meta: {
      requiresAuth: false,
    },
  },
  {
    path: '/user',
    redirect: '/user/userInfo',
    component: () => import('@/view/user/index.vue'),
    children: [
      {
        path: 'userInfo',
        component: () => import('@/view/user/userInfo.vue'),
      },
      {
        path: 'productsList',
        component: () => import('@/view/user/productsList.vue'),
      },
      {
        path: 'packagePurchase',
        component: () => import('@/view/user/packagePurchase.vue'),
      },
      {
        path: 'quantityUsed',
        component: () => import('@/view/user/quantityUsed.vue'),
      },
      {
        path: 'followRecord',
        component: () => import('@/view/user/followRecord.vue'),
      },
      {
        path: 'browseRecords',
        component: () => import('@/view/user/browseRecords.vue'),
      },
      {
        path: 'tradeList',
        component: () => import('@/view/user/tradeList.vue'),
      },
      {
        path: 'assetManage',
        component: () => import('@/view/user/assetManage.vue'),
      },
      {
        path: 'evidenceList',
        component: () => import('@/view/user/dataEvidenceList.vue'),
      },
      {
        path: 'softwareRegList',
        component: () => import('@/view/user/softwareRegList.vue'),
      },
    ],
  },
  {
    path: '/data',
    redirect: '/data/page',
    meta: {
      header: 'data-asset',
      title: '数据知识产权存证 - 文创链开放平台',
      description:
        '文创链数据知识产权存证服务，基于区块链技术提供数据资产保护，确保数字内容版权安全可信。',
    },
    component: () => import('@/view/data/index.vue'),
    children: [
      {
        path: 'page',
        component: () => import('@/view/data/home.vue'),
        meta: {
          title: '数据存证首页 - 文创链开放平台',
          description: '文创链数据存证服务首页，提供区块链存证解决方案，保护数字内容原创权益。',
        },
      },
      {
        path: 'eviApply',
        component: () => import('@/view/data/eviApply.vue'),
        meta: {
          title: '申请数据存证 - 文创链开放平台',
          description: '在文创链开放平台申请数据存证，将您的原创内容通过区块链技术进行版权保护。',
        },
      },
      {
        path: 'regPublic',
        component: () => import('@/view/data/regPublic.vue'),
        meta: {
          title: '公示数据登记 - 文创链开放平台',
          description: '文创链数据公示登记服务，通过区块链技术实现数据公开、透明的存证和验证。',
        },
      },
      {
        path: 'evidence/detail/:id',
        component: () => import('@/view/data/eviDetail.vue'),
        meta: {
          title: '存证详情 - 文创链开放平台',
          description: '查看数据存证详情信息，包括存证信息、区块链凭证和文件列表。',
        },
      },
      {
        path: 'announcement',
        component: () => import('@/view/data/announcement.vue'),
        meta: {
          title: '数据知识产权公示公告 - 文创链开放平台',
          description: '查看数据知识产权公示公告信息。',
        },
      },
      {
        path: 'upload-test',
        component: () => import('@/view/test/uploadTest.vue'),
        meta: {
          title: '文件上传测试 - 文创链开放平台',
          description: '文件上传功能测试页面。',
          requiresAuth: false,
        },
      },
    ],
  },
  {
    path: '/software',
    redirect: '/software/page',
    meta: {
      header: 'software-copyright',
      title: '计算机软件著作权登记 - 文创链开放平台',
      description: '文创链提供计算机软件著作权登记服务，保护软件开发成果，维护知识产权权益。',
    },
    component: () => import('@/view/software/index.vue'),
    children: [
      {
        path: 'page',
        component: () => import('@/view/software/home.vue'),
        meta: {
          title: '软件著作权服务 - 文创链开放平台',
          description:
            '文创链软件著作权登记服务，保护软件开发者权益，提供便捷的登记流程和法律保障。',
        },
      },
      {
        path: 'evidence',
        component: () => import('@/view/software/evidence.vue'),
        meta: {
          title: '软件著作权存证 - 文创链开放平台',
          description: '文创链软件著作权存证服务，通过区块链技术对软件著作权进行可信存证和保护。',
        },
      },
      {
        path: 'register',
        component: () => import('@/view/software/register.vue'),
        meta: {
          title: '软件著作权登记 - 文创链开放平台',
          description: '文创链软件著作权登记服务，提供便捷高效的软件著作权登记流程。',
        },
      },
      {
        path: 'details/:id',
        component: () => import('@/view/software/details.vue'),
        meta: {
          title: '软件著作权详情 - 文创链开放平台',
          description: '查看软件著作权详细信息、登记状态和公示内容。',
        },
      },
      {
        path: 'announcements',
        component: () => import('@/view/software/announcements.vue'),
        meta: {
          title: '软件著作权公示公告 - 文创链开放平台',
          description: '查看软件著作权登记公示公告信息。',
        },
      },
    ],
  },
  {
    path: '/payment',
    meta: {
      title: '支付中心 - 文创链开放平台',
      description: '文创链开放平台支付中心，提供安全便捷的支付服务。',
    },
    children: [
      {
        path: 'cashier',
        name: 'Cashier',
        component: () => import('@/view/payment/Cashier.vue'),
        meta: {
          title: '收银台 - 文创链开放平台',
          description: '文创链开放平台收银台，支持多种支付方式，安全便捷完成支付。',
          requiresAuth: true,
          keepAlive: false,
        },
      },
      {
        path: 'result',
        name: 'PaymentResult',
        component: () => import('@/view/payment/PaymentResult.vue'),
        meta: {
          title: '支付结果 - 文创链开放平台',
          description: '查看支付结果，确认订单状态。',
          requiresAuth: true,
          keepAlive: false,
        },
      },
      {
        path: 'demo',
        name: 'CashierDemo',
        component: () => import('@/view/payment/CashierDemo.vue'),
        meta: {
          title: '收银台演示 - 文创链开放平台',
          description: '收银台页面改造演示，展示新的布局和功能。',
          requiresAuth: false,
          keepAlive: false,
        },
      },
      {
        path: 'test',
        name: 'ComponentTest',
        component: () => import('@/view/payment/ComponentTest.vue'),
        meta: {
          title: '组件测试 - 文创链开放平台',
          description: '测试各个支付组件是否能正常工作。',
          requiresAuth: false,
          keepAlive: false,
        },
      },
    ],
  },
  // {
  //   path: '/',
  //   name: 'Login',
  //   component: () => import('@/pages/login/Login.vue'), // 注意这里要带上 文件后缀.vue
  // },
];

const router = createRouter({
  history: createWebHashHistory(),
  routes,
});
router.beforeEach((pre, next) => {
  NProgress.start();
});

router.afterEach(() => {
  NProgress.done();
});
export default router;
