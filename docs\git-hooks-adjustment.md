# Git Hooks 配置调整说明

## 调整概述

根据项目需求，已将git hooks检查策略调整为**仅保留git提交日志检查**，取消所有其他检查项。

## 调整详情

### 1. 禁用的检查项

- ✅ **ESLint代码检查** - 已禁用
- ✅ **Prettier代码格式化** - 已禁用  
- ✅ **样式文件格式化** - 已禁用
- ✅ **lint-staged预提交检查** - 已禁用

### 2. 保留的检查项

- ✅ **提交消息格式检查** - 仍然启用
  - 使用commitlint进行提交消息验证
  - 遵循conventional commits规范
  - 支持的提交类型：feat, add, fix, docs, style, refactor, perf, test, chore, revert, build, ci, release

## 修改的文件

### `.husky/pre-commit`
```bash
#!/usr/bin/env sh
. "$(dirname -- "$0")/_/husky.sh"

# 已禁用 lint-staged 检查，仅保留提交日志检查
# npx lint-staged
```

### `package.json`
将 `lint-staged` 配置重命名为 `_lint-staged-disabled`，使其失效但保留配置以备后续需要。

## 提交消息格式要求

提交消息必须遵循以下格式：
```
<type>: <description>

[optional body]

[optional footer]
```

### 支持的类型
- `feat`: 新功能
- `add`: 添加功能  
- `fix`: 修复bug
- `docs`: 文档变更
- `style`: 代码格式（不影响功能）
- `refactor`: 代码重构
- `perf`: 性能优化
- `test`: 测试相关
- `chore`: 构建过程或辅助工具变更
- `revert`: 回滚
- `build`: 构建流程、依赖变更
- `ci`: CI配置变更
- `release`: 发布新版本

### 示例
```bash
git commit -m "feat: 添加用户登录功能"
git commit -m "fix: 修复文件上传失败的问题"
git commit -m "docs: 更新API文档"
```

## 如何重新启用代码检查

如果将来需要重新启用代码检查，可以：

1. 恢复 `.husky/pre-commit` 文件：
   ```bash
   #!/usr/bin/env sh
   . "$(dirname -- "$0")/_/husky.sh"
   
   npx lint-staged
   ```

2. 在 `package.json` 中将 `_lint-staged-disabled` 重命名回 `lint-staged`

## 注意事项

- 虽然禁用了自动代码检查，但建议开发者在提交前手动运行 `npm run lint` 和 `npm run format` 来保持代码质量
- 提交消息检查仍然有效，不符合规范的提交消息将被拒绝
- 可以通过 `git commit --no-verify` 跳过所有hooks检查（不推荐）

---

**调整时间**: 2025-06-19  
**调整原因**: 简化开发流程，仅保留必要的提交日志检查
