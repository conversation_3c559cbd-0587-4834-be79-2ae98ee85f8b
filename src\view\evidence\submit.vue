<template>
  <div class="page-container">
    <div class="page-header">
      <h1 class="page-title">区块链存证申请</h1>
      <p class="page-description">为您的数字资产提供不可篡改的区块链存证服务</p>
    </div>

    <div class="content-wrapper">
      <a-steps :current="currentStep" class="mb-8">
        <a-step title="填写基本信息" />
        <a-step title="上传存证文件" />
        <a-step title="确认并支付" />
        <a-step title="完成" />
      </a-steps>

      <div class="form-container">
        <div v-if="currentStep === 0">
          <h2 class="form-section-title">存证基本信息</h2>
          <a-form :model="formData" layout="vertical">
            <a-form-item field="evidenceTitle" label="存证标题" required>
              <a-input v-model="formData.evidenceTitle" placeholder="请输入存证标题" />
            </a-form-item>
            <a-form-item field="evidenceType" label="存证类型" required>
              <a-select v-model="formData.evidenceType" placeholder="请选择存证类型">
                <a-option value="document">文档存证</a-option>
                <a-option value="image">图片存证</a-option>
                <a-option value="audio">音频存证</a-option>
                <a-option value="video">视频存证</a-option>
                <a-option value="code">代码存证</a-option>
                <a-option value="contract">合同存证</a-option>
                <a-option value="other">其他</a-option>
              </a-select>
            </a-form-item>
            <a-form-item field="evidenceDescription" label="存证描述" required>
              <a-textarea
                v-model="formData.evidenceDescription"
                placeholder="请简要描述存证内容和目的"
                :max-length="500"
                show-word-limit
              />
            </a-form-item>
            <a-form-item field="applicantType" label="申请人类型" required>
              <a-radio-group v-model="formData.applicantType">
                <a-radio value="individual">个人</a-radio>
                <a-radio value="enterprise">企业</a-radio>
              </a-radio-group>
            </a-form-item>
            <a-form-item field="applicantName" label="申请人姓名/企业名称" required>
              <a-input v-model="formData.applicantName" placeholder="请输入申请人姓名或企业名称" />
            </a-form-item>
            <a-form-item field="applicantId" label="身份证号/统一社会信用代码" required>
              <a-input
                v-model="formData.applicantId"
                placeholder="请输入身份证号或统一社会信用代码"
              />
            </a-form-item>
            <a-form-item field="contactPhone" label="联系电话" required>
              <a-input v-model="formData.contactPhone" placeholder="请输入联系电话" />
            </a-form-item>
            <a-form-item field="contactEmail" label="电子邮箱" required>
              <a-input v-model="formData.contactEmail" placeholder="请输入电子邮箱" />
            </a-form-item>
          </a-form>
          <div class="step-actions">
            <a-button type="primary" @click="nextStep"> 下一步 </a-button>
          </div>
        </div>

        <div v-if="currentStep === 1">
          <h2 class="form-section-title">上传存证文件</h2>
          <p class="upload-description">
            请上传需要存证的文件，系统将自动计算文件哈希值并上链存证：
          </p>

          <a-form :model="formData" layout="vertical">
            <a-form-item field="evidenceFiles" label="存证文件" required>
              <a-upload
                action="/"
                :custom-request="handleFileUpload"
                :file-list="formData.evidenceFiles"
                accept="*"
                multiple
                :limit="10"
                @change="handleEvidenceFilesChange"
              >
                <template #upload-button>
                  <a-button>
                    <template #icon>
                      <icon-upload />
                    </template>
                    选择文件
                  </a-button>
                </template>
                <template #extra>
                  <div class="upload-tip">
                    支持所有格式文件，单个文件最大100MB，最多可上传10个文件
                  </div>
                </template>
              </a-upload>
            </a-form-item>

            <div v-if="formData.evidenceFiles.length > 0" class="file-hash-list">
              <h3>文件哈希值</h3>
              <div v-for="file in formData.evidenceFiles" :key="file.uid" class="hash-item">
                <div class="file-info">
                  <icon-file />
                  <span class="file-name">{{ file.name }}</span>
                  <span class="file-size">({{ formatFileSize(file.size) }})</span>
                </div>
                <div class="file-hash">
                  <span class="hash-label">SHA256:</span>
                  <span class="hash-value">{{ file.hash || '计算中...' }}</span>
                  <a-button v-if="file.hash" type="text" size="mini" @click="copyHash(file.hash)">
                    <icon-copy />
                  </a-button>
                </div>
              </div>
            </div>

            <a-form-item field="blockchainNetwork" label="区块链网络" required>
              <a-select v-model="formData.blockchainNetwork" placeholder="请选择区块链网络">
                <a-option value="ethereum">以太坊主网</a-option>
                <a-option value="polygon">Polygon</a-option>
                <a-option value="bsc">BSC</a-option>
                <a-option value="private">私有链</a-option>
              </a-select>
            </a-form-item>
          </a-form>

          <div class="step-actions">
            <a-button @click="prevStep"> 上一步 </a-button>
            <a-button type="primary" :disabled="!canProceedToStep2" @click="nextStep">
              下一步
            </a-button>
          </div>
        </div>

        <div v-if="currentStep === 2">
          <h2 class="form-section-title">确认存证信息</h2>

          <div class="confirmation-section">
            <h3>基本信息</h3>
            <div class="info-grid">
              <div class="info-item">
                <span class="label">存证标题：</span>
                <span class="value">{{ formData.evidenceTitle }}</span>
              </div>
              <div class="info-item">
                <span class="label">存证类型：</span>
                <span class="value">{{ getEvidenceTypeText(formData.evidenceType) }}</span>
              </div>
              <div class="info-item">
                <span class="label">申请人：</span>
                <span class="value">{{ formData.applicantName }}</span>
              </div>
              <div class="info-item">
                <span class="label">区块链网络：</span>
                <span class="value">{{ getNetworkText(formData.blockchainNetwork) }}</span>
              </div>
              <div class="info-item full-width">
                <span class="label">存证描述：</span>
                <span class="value">{{ formData.evidenceDescription }}</span>
              </div>
            </div>
          </div>

          <div class="confirmation-section">
            <h3>存证文件</h3>
            <div class="file-list">
              <div v-for="file in formData.evidenceFiles" :key="file.uid" class="file-item">
                <icon-file />
                <div class="file-details">
                  <div class="file-name">{{ file.name }}</div>
                  <div class="file-meta">{{ formatFileSize(file.size) }} · {{ file.hash }}</div>
                </div>
              </div>
            </div>
          </div>

          <div class="fee-section">
            <a-alert type="info">
              <template #message>
                <div class="fee-info">
                  <h4>存证费用明细</h4>
                  <div class="fee-item">
                    <span>基础存证费用：</span>
                    <span>{{ baseFee }}元</span>
                  </div>
                  <div class="fee-item">
                    <span>区块链网络费用：</span>
                    <span>{{ networkFee }}元</span>
                  </div>
                  <div class="fee-item">
                    <span>文件数量费用（{{ formData.evidenceFiles.length }}个）：</span>
                    <span>{{ fileFee }}元</span>
                  </div>
                  <div class="fee-total">
                    <span>总计：</span>
                    <span class="total-amount">{{ totalFee }}元</span>
                  </div>
                </div>
              </template>
            </a-alert>
          </div>

          <div class="step-actions">
            <a-button @click="prevStep"> 上一步 </a-button>
            <a-button type="primary" :loading="submitting" @click="submitEvidence">
              确认存证并支付
            </a-button>
          </div>
        </div>

        <div v-if="currentStep === 3">
          <div class="success-container">
            <icon-check-circle class="success-icon" />
            <h2 class="success-title">存证申请提交成功！</h2>
            <p class="success-message">
              您的文件已成功提交区块链存证，正在进行上链处理，预计3-5分钟完成。
            </p>
            <div class="success-info">
              <p>
                <span class="label">存证编号：</span><span class="value">{{ evidenceNumber }}</span>
              </p>
              <p>
                <span class="label">交易哈希：</span
                ><span class="value">{{ transactionHash }}</span>
              </p>
              <p>
                <span class="label">提交时间：</span><span class="value">{{ submissionTime }}</span>
              </p>
              <p>
                <span class="label">区块链网络：</span
                ><span class="value">{{ getNetworkText(formData.blockchainNetwork) }}</span>
              </p>
            </div>
            <a-alert type="success" class="mt-4">
              <template #message>
                存证上链完成后，您将收到邮件和短信通知。您可以在"查询验证"页面查看存证状态和下载存证证书。
              </template>
            </a-alert>
            <div class="success-actions">
              <a-button type="primary" @click="goToQuery"> 查询验证 </a-button>
              <a-button @click="downloadReceipt"> 下载存证回执 </a-button>
              <a-button @click="goToHome"> 返回首页 </a-button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref, computed } from 'vue';
  import { useRouter } from 'vue-router';
  import { Message } from '@arco-design/web-vue';
  import { IconUpload, IconFile, IconCopy, IconCheckCircle } from '@arco-design/web-vue/es/icon';
  import { createEvidenceApplication, calculateFileHash } from '@/http/api/evidence';

  const router = useRouter();

  // 当前步骤
  const currentStep = ref(0);

  // 表单数据
  const formData = ref({
    evidenceTitle: '',
    evidenceType: '',
    evidenceDescription: '',
    applicantType: 'individual',
    applicantName: '',
    applicantId: '',
    contactPhone: '',
    contactEmail: '',
    evidenceFiles: [] as any[],
    blockchainNetwork: 'ethereum',
  });

  // 费用相关
  const baseFee = ref(20);
  const networkFee = ref(15);
  const fileFee = computed(() => formData.value.evidenceFiles.length * 5);
  const totalFee = computed(() => baseFee.value + networkFee.value + fileFee.value);

  // 提交状态
  const submitting = ref(false);
  const evidenceNumber = ref('');
  const transactionHash = ref('');
  const submissionTime = ref('');

  // 计算属性
  const canProceedToStep2 = computed(() => {
    return formData.value.evidenceFiles.length > 0 && formData.value.blockchainNetwork;
  });

  // 步骤控制方法
  const nextStep = () => {
    if (currentStep.value < 3) {
      currentStep.value++;
    }
  };

  const prevStep = () => {
    if (currentStep.value > 0) {
      currentStep.value--;
    }
  };

  // 文件上传处理
  const handleFileUpload = (option: any) => {
    let aborted = false;

    // 异步处理文件哈希计算
    (async () => {
      try {
        // 计算文件哈希值
        const hash = await calculateFileHash(option.file);
        if (!aborted) {
          option.file.hash = hash;
          option.onSuccess();
        }
      } catch (error) {
        if (!aborted) {
          option.onError(error);
        }
      }
    })();

    // 立即返回包含 abort 方法的对象
    return {
      abort: () => {
        aborted = true;
      },
    };
  };

  const handleEvidenceFilesChange = (fileList: any[]) => {
    formData.value.evidenceFiles = fileList;
  };

  // 工具方法
  const getEvidenceTypeText = (type: string) => {
    const typeMap: Record<string, string> = {
      document: '文档存证',
      image: '图片存证',
      audio: '音频存证',
      video: '视频存证',
      code: '代码存证',
      contract: '合同存证',
      other: '其他',
    };
    return typeMap[type] || type;
  };

  const getNetworkText = (network: string) => {
    const networkMap: Record<string, string> = {
      ethereum: '以太坊主网',
      polygon: 'Polygon',
      bsc: 'BSC',
      private: '私有链',
    };
    return networkMap[network] || network;
  };

  const formatFileSize = (size: number) => {
    if (size < 1024) return size + ' B';
    if (size < 1024 * 1024) return (size / 1024).toFixed(1) + ' KB';
    return (size / (1024 * 1024)).toFixed(1) + ' MB';
  };

  const copyHash = (hash: string) => {
    navigator.clipboard.writeText(hash);
    Message.success('哈希值已复制到剪贴板');
  };

  // 提交存证
  const submitEvidence = async () => {
    submitting.value = true;
    try {
      // 构建提交数据
      const submitData = {
        ...formData.value,
        evidenceFiles: formData.value.evidenceFiles.map(file => file.name),
        evidenceHash: formData.value.evidenceFiles.map(file => file.hash).join(','),
      };

      // 调用API提交存证
      const response = await createEvidenceApplication(submitData);

      // 设置成功信息
      evidenceNumber.value = `EV${Date.now()}`;
      transactionHash.value = `0x${Math.random().toString(16).substr(2, 64)}`;
      submissionTime.value = new Date().toLocaleString();

      // 跳转到成功页面
      nextStep();

      Message.success('存证申请提交成功！');
    } catch (error) {
      Message.error('提交失败，请重试');
      console.error('提交存证失败:', error);
    } finally {
      submitting.value = false;
    }
  };

  // 页面跳转方法
  const goToQuery = () => {
    router.push('/evidence/query');
  };

  const downloadReceipt = () => {
    // 这里应该实现下载存证回执的逻辑
    Message.info('存证回执下载功能开发中...');
  };

  const goToHome = () => {
    router.push('/evidence/page');
  };
</script>

<style lang="scss" scoped>
  .page-container {
    width: 100%;
    min-height: 100vh;
    background-color: #f7f8fa;
    padding-bottom: 40px;
  }

  .page-header {
    background: linear-gradient(135deg, #165dff 0%, #4080ff 100%);
    color: white;
    padding: 40px 0;
    text-align: center;

    .page-title {
      font-size: 32px;
      font-weight: 600;
      margin-bottom: 12px;
    }

    .page-description {
      font-size: 16px;
      opacity: 0.9;
      margin: 0;
    }
  }

  .content-wrapper {
    max-width: 800px;
    margin: 0 auto;
    padding: 40px 24px;
  }

  .form-container {
    background: white;
    border-radius: 12px;
    padding: 32px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  }

  .form-section-title {
    font-size: 20px;
    font-weight: 600;
    color: #1d2129;
    margin-bottom: 24px;
    padding-bottom: 12px;
    border-bottom: 2px solid #e5e6eb;
  }

  .upload-description {
    color: #4e5969;
    margin-bottom: 24px;
    line-height: 1.6;
  }

  .upload-tip {
    color: #86909c;
    font-size: 12px;
    margin-top: 4px;
  }

  .file-hash-list {
    margin-top: 24px;
    padding: 16px;
    background: #f7f8fa;
    border-radius: 8px;

    h3 {
      font-size: 14px;
      font-weight: 600;
      color: #1d2129;
      margin-bottom: 16px;
    }

    .hash-item {
      margin-bottom: 16px;
      padding: 12px;
      background: white;
      border-radius: 6px;
      border: 1px solid #e5e6eb;

      &:last-child {
        margin-bottom: 0;
      }

      .file-info {
        display: flex;
        align-items: center;
        gap: 8px;
        margin-bottom: 8px;

        .arco-icon {
          color: #165dff;
          font-size: 16px;
        }

        .file-name {
          font-weight: 500;
          color: #1d2129;
        }

        .file-size {
          color: #86909c;
          font-size: 12px;
        }
      }

      .file-hash {
        display: flex;
        align-items: center;
        gap: 8px;

        .hash-label {
          color: #86909c;
          font-size: 12px;
          width: 50px;
          flex-shrink: 0;
        }

        .hash-value {
          font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
          font-size: 11px;
          color: #1d2129;
          word-break: break-all;
          flex: 1;
        }
      }
    }
  }

  .step-actions {
    display: flex;
    justify-content: center;
    gap: 16px;
    margin-top: 32px;
    padding-top: 24px;
    border-top: 1px solid #e5e6eb;

    .arco-btn {
      min-width: 120px;
    }
  }

  .confirmation-section {
    margin-bottom: 32px;

    h3 {
      font-size: 16px;
      font-weight: 600;
      color: #1d2129;
      margin-bottom: 16px;
      padding-bottom: 8px;
      border-bottom: 1px solid #e5e6eb;
    }

    .info-grid {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 16px;

      .info-item {
        display: flex;
        align-items: flex-start;

        &.full-width {
          grid-column: 1 / -1;
          flex-direction: column;
          gap: 8px;
        }

        .label {
          color: #86909c;
          width: 120px;
          flex-shrink: 0;
          font-size: 14px;
        }

        .value {
          color: #1d2129;
          font-weight: 500;
          flex: 1;
        }
      }
    }

    .file-list {
      .file-item {
        display: flex;
        align-items: center;
        gap: 12px;
        padding: 12px;
        background: #f7f8fa;
        border-radius: 6px;
        margin-bottom: 8px;

        .arco-icon {
          color: #165dff;
          font-size: 16px;
        }

        .file-details {
          flex: 1;

          .file-name {
            font-size: 14px;
            font-weight: 500;
            color: #1d2129;
            margin-bottom: 4px;
          }

          .file-meta {
            font-size: 11px;
            color: #86909c;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            word-break: break-all;
          }
        }
      }
    }
  }

  .fee-section {
    margin-bottom: 32px;

    .fee-info {
      h4 {
        font-size: 14px;
        font-weight: 600;
        color: #1d2129;
        margin-bottom: 12px;
      }

      .fee-item {
        display: flex;
        justify-content: space-between;
        margin-bottom: 8px;
        color: #4e5969;
        font-size: 14px;
      }

      .fee-total {
        display: flex;
        justify-content: space-between;
        margin-top: 12px;
        padding-top: 12px;
        border-top: 1px solid #e5e6eb;
        font-weight: 600;
        color: #1d2129;

        .total-amount {
          color: #165dff;
          font-size: 16px;
        }
      }
    }
  }

  .success-container {
    text-align: center;
    padding: 40px 0;

    .success-icon {
      font-size: 64px;
      color: #00b42a;
      margin-bottom: 24px;
    }

    .success-title {
      font-size: 24px;
      font-weight: 600;
      color: #1d2129;
      margin-bottom: 16px;
    }

    .success-message {
      font-size: 16px;
      color: #4e5969;
      margin-bottom: 32px;
      line-height: 1.6;
    }

    .success-info {
      background: #f7f8fa;
      border-radius: 8px;
      padding: 24px;
      margin-bottom: 24px;
      text-align: left;

      p {
        margin-bottom: 12px;
        display: flex;
        align-items: flex-start;
        gap: 8px;

        &:last-child {
          margin-bottom: 0;
        }

        .label {
          color: #86909c;
          width: 100px;
          flex-shrink: 0;
          font-size: 14px;
        }

        .value {
          color: #1d2129;
          font-weight: 500;
          font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
          word-break: break-all;
          flex: 1;
        }
      }
    }

    .success-actions {
      display: flex;
      justify-content: center;
      gap: 16px;
      margin-top: 32px;
      flex-wrap: wrap;

      .arco-btn {
        min-width: 140px;
      }
    }
  }

  :deep(.arco-form-item-label) {
    font-weight: 500;
    color: #1d2129;
  }

  :deep(.arco-steps) {
    margin-bottom: 40px;
  }

  @media (max-width: 768px) {
    .content-wrapper {
      padding: 24px 16px;
    }

    .form-container {
      padding: 24px 16px;
    }

    .page-header {
      padding: 32px 16px;

      .page-title {
        font-size: 24px;
      }

      .page-description {
        font-size: 14px;
      }
    }

    .step-actions {
      flex-direction: column;

      .arco-btn {
        width: 100%;
      }
    }

    .success-actions {
      flex-direction: column;

      .arco-btn {
        width: 100%;
      }
    }

    .confirmation-section .info-grid {
      grid-template-columns: 1fr;
    }
  }
</style>
