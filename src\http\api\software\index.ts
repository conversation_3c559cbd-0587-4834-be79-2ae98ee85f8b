/** @format */

import http from "@/http/proHttp";

// 软件登记信息相关接口
// 创建软件登记信息
export function createSoftwareRegistrationInfo(params: {
  id?: string;
  softwareType?: string;
  acquisitionMethod?: string;
  softwareFullName?: string;
  versionNumber?: string;
  rightsScope?: string;
  platformType?: string;
  softwareClassification?: string;
  softwareDescription?: string;
  developmentMethod?: string;
  programmingLanguages?: string;
  completionDate?: string;
  registrantEmail?: string;
  softwareIntroduction?: string;
  statusCd?: string;
  remark?: string;
}) {
  return http.post('/admin-api/software/registration-info/create', params);
}

// 更新软件登记信息
export function updateSoftwareRegistrationInfo(params: {
  id?: string;
  softwareType?: string;
  acquisitionMethod?: string;
  softwareFullName?: string;
  versionNumber?: string;
  rightsScope?: string;
  platformType?: string;
  softwareClassification?: string;
  softwareDescription?: string;
  developmentMethod?: string;
  programmingLanguages?: string;
  completionDate?: string;
  registrantEmail?: string;
  softwareIntroduction?: string;
  statusCd?: string;
  remark?: string;
}) {
  return http.put('/admin-api/software/registration-info/update', params);
}

// 获取软件登记信息分页
export function getSoftwareRegistrationInfoPage(params: {
  softwareType?: string;
  acquisitionMethod?: string;
  softwareFullName?: string;
  versionNumber?: string;
  rightsScope?: string;
  platformType?: string;
  softwareClassification?: string;
  softwareDescription?: string;
  developmentMethod?: string;
  programmingLanguages?: string;
  completionDate?: string;
  registrantEmail?: string;
  softwareIntroduction?: string;
  statusCd?: string;
  remark?: string;
  creator?: string;
  createTime?: string;
  pageNo: string;
  pageSize: string;
}) {
  return http.get('/admin-api/software/registration-info/page', { params });
}

// 获取单个软件登记信息
export function getSoftwareRegistrationInfo(id: number) {
  return http.get('/admin-api/software/registration-info/get', { params: { id } });
}

// 导出软件登记信息Excel
export function exportSoftwareRegistrationInfoExcel(params: {
  softwareType?: string;
  acquisitionMethod?: string;
  softwareFullName?: string;
  versionNumber?: string;
  rightsScope?: string;
  platformType?: string;
  softwareClassification?: string;
  softwareDescription?: string;
  developmentMethod?: string;
  programmingLanguages?: string;
  completionDate?: string;
  registrantEmail?: string;
  softwareIntroduction?: string;
  statusCd?: string;
  remark?: string;
  creator?: string;
  createTime?: string;
  pageNo: string;
  pageSize: string;
}) {
  return http.get('/admin-api/software/registration-info/export-excel', { params });
}

// 删除软件登记信息
export function deleteSoftwareRegistrationInfo(id: number) {
  return http.delete('/admin-api/software/registration-info/delete', { params: { id } });
}

// 获取软件著作权公示列表
export function getSoftwareCopyrightList(params: { page: number; size: number }) {
  // 请替换为实际的API端点
  return http.get('/api/software/copyright/list', { params });
}

// 获取软件著作权政策信息
export function getSoftwareCopyrightPolicies() {
  // 请替换为实际的API端点
  return http.get('/api/software/copyright/policies');
}

// 获取软件著作权常见问题
export function getSoftwareCopyrightFAQs() {
  // 请替换为实际的API端点
  return http.get('/api/software/copyright/faqs');
}