<template>
  <div class="coupon-selector-container">
    <a-card class="coupon-card" :bordered="false">
      <template #title>
        <div class="coupon-header">
          <icon-gift class="coupon-icon" />
          <span class="coupon-title">选择优惠券</span>
          <span v-if="selectedCoupons.length > 0" class="coupon-count">
            (已选择{{ selectedCoupons.length }}张)
          </span>
        </div>
      </template>

      <div v-if="loading" class="loading-container">
        <a-spin :size="24" />
        <p class="loading-text">正在加载优惠券...</p>
      </div>

      <div v-else class="coupon-content">
        <!-- 已选择的优惠券 -->
        <div v-if="selectedCoupons.length > 0" class="selected-coupons">
          <h4 class="section-title">已选择的优惠券</h4>
          <div class="selected-coupons-list">
            <div
              v-for="coupon in selectedCoupons"
              :key="coupon.couponId"
              class="selected-coupon-item"
            >
              <div class="coupon-info">
                <div class="coupon-value">
                  <span v-if="coupon.type === 'amount'" class="value-amount">
                    ¥{{ formatAmount(coupon.value) }}
                  </span>
                  <span v-else class="value-discount">
                    {{ formatDiscount(coupon.value) }}折
                  </span>
                </div>
                <div class="coupon-name">{{ coupon.name }}</div>
              </div>
              <a-button
                type="text"
                size="small"
                @click="handleRemoveCoupon(coupon.couponId)"
              >
                <template #icon>
                  <icon-close />
                </template>
              </a-button>
            </div>
          </div>
        </div>

        <!-- 可用优惠券列表 -->
        <div class="available-coupons">
          <h4 class="section-title">
            可用优惠券
            <span class="coupon-stats">({{ availableCouponsFiltered.length }}张可用)</span>
          </h4>

          <div v-if="availableCouponsFiltered.length === 0" class="empty-coupons">
            <a-empty description="暂无可用的优惠券">
              <template #image>
                <icon-gift style="font-size: 64px; color: #c9cdd4" />
              </template>
            </a-empty>
          </div>

          <div v-else class="coupons-list">
            <div
              v-for="coupon in displayedCoupons"
              :key="coupon.couponId"
              class="coupon-item"
              :class="{
                selected: isSelected(coupon.couponId),
                disabled: !coupon.applicable,
              }"
              @click="handleSelectCoupon(coupon)"
            >
              <div class="coupon-card-inner">
                <div class="coupon-left">
                  <div class="coupon-value-display">
                    <span v-if="coupon.type === 'amount'" class="value-amount">
                      ¥{{ formatAmount(coupon.value) }}
                    </span>
                    <span v-else class="value-discount">
                      {{ formatDiscount(coupon.value) }}<small>折</small>
                    </span>
                  </div>
                  <div class="coupon-type">
                    {{ coupon.type === 'amount' ? '立减券' : '折扣券' }}
                  </div>
                </div>

                <div class="coupon-right">
                  <div class="coupon-details">
                    <div class="coupon-name">{{ coupon.name }}</div>
                    <div class="coupon-description">{{ coupon.description }}</div>
                    <div class="coupon-conditions">
                      <span class="condition-item">
                        满¥{{ formatAmount(coupon.minOrderAmount) }}可用
                      </span>
                      <span v-if="coupon.maxDiscountAmount" class="condition-item">
                        最高优惠¥{{ formatAmount(coupon.maxDiscountAmount) }}
                      </span>
                    </div>
                    <div class="coupon-expire">
                      有效期至：{{ formatExpireTime(coupon.expireTime) }}
                    </div>
                  </div>

                  <div class="coupon-action">
                    <a-checkbox
                      :model-value="isSelected(coupon.couponId)"
                      :disabled="!coupon.applicable"
                      @click.stop
                      @change="handleSelectCoupon(coupon)"
                    />
                  </div>
                </div>
              </div>

              <!-- 不可用原因提示 -->
              <div v-if="!coupon.applicable" class="coupon-disabled-reason">
                <icon-info-circle />
                <span>订单金额不满足使用条件</span>
              </div>
            </div>

            <!-- 显示更多按钮 -->
            <div v-if="availableCouponsFiltered.length > showLimit" class="show-more-container">
              <a-button
                type="text"
                @click="showAll = !showAll"
              >
                {{ showAll ? '收起' : `查看全部(${availableCouponsFiltered.length}张)` }}
                <template #icon>
                  <icon-down v-if="!showAll" />
                  <icon-up v-else />
                </template>
              </a-button>
            </div>
          </div>
        </div>
      </div>
    </a-card>
  </div>
</template>

<script setup lang="ts">
  import { computed, ref } from 'vue';
  import {
    IconGift,
    IconClose,
    IconInfoCircle,
    IconDown,
    IconUp,
  } from '@arco-design/web-vue/es/icon';
  import type { CouponInfo } from '@/http/api/payment/types';

  // Props
  interface Props {
    availableCoupons: CouponInfo[];
    selectedCoupons: CouponInfo[];
    orderAmount: number;
    loading?: boolean;
  }

  const props = withDefaults(defineProps<Props>(), {
    loading: false,
  });

  // Emits
  const emit = defineEmits(['select', 'remove']);

  // 本地状态
  const showAll = ref(false);
  const showLimit = 3;

  // 计算属性
  const availableCouponsFiltered = computed(() =>
    props.availableCoupons.filter(
      coupon => !props.selectedCoupons.some(selected => selected.couponId === coupon.couponId)
    )
  );

  const displayedCoupons = computed(() => {
    if (showAll.value) {
      return availableCouponsFiltered.value;
    }
    return availableCouponsFiltered.value.slice(0, showLimit);
  });

  // 方法
  function isSelected(couponId: string): boolean {
    return props.selectedCoupons.some(coupon => coupon.couponId === couponId);
  }

  function handleSelectCoupon(coupon: CouponInfo) {
    if (!coupon.applicable) return;
    emit('select', coupon);
  }

  function handleRemoveCoupon(couponId: string) {
    emit('remove', couponId);
  }

  function formatAmount(amount: number): string {
    return amount.toFixed(2);
  }

  function formatDiscount(discount: number): string {
    return (discount * 10).toFixed(1);
  }

  function formatExpireTime(expireTime: string): string {
    return new Date(expireTime).toLocaleDateString('zh-CN');
  }
</script>

<style scoped lang="less">
  .coupon-selector-container {
    margin-bottom: 24px;

    .coupon-card {
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

      :deep(.arco-card-header) {
        border-bottom: 1px solid #f0f0f0;
        padding: 16px 20px;
      }

      :deep(.arco-card-body) {
        padding: 20px;
      }
    }

    .coupon-header {
      display: flex;
      align-items: center;
      gap: 8px;

      .coupon-icon {
        font-size: 18px;
        color: #ff4d4f;
      }

      .coupon-title {
        font-size: 16px;
        font-weight: 600;
        color: #262626;
      }

      .coupon-count {
        font-size: 14px;
        color: #1890ff;
        font-weight: normal;
      }
    }

    .loading-container {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 40px 20px;

      .loading-text {
        margin-top: 12px;
        color: #8c8c8c;
        font-size: 14px;
      }
    }

    .coupon-content {
      .section-title {
        font-size: 14px;
        font-weight: 600;
        color: #262626;
        margin: 0 0 16px 0;
        display: flex;
        align-items: center;
        gap: 8px;

        .coupon-stats {
          font-size: 12px;
          color: #8c8c8c;
          font-weight: normal;
        }
      }

      .selected-coupons {
        margin-bottom: 24px;
        padding-bottom: 20px;
        border-bottom: 1px solid #f0f0f0;

        .selected-coupons-list {
          display: flex;
          flex-wrap: wrap;
          gap: 12px;
        }

        .selected-coupon-item {
          display: flex;
          align-items: center;
          gap: 8px;
          padding: 8px 12px;
          background: #f6ffed;
          border: 1px solid #b7eb8f;
          border-radius: 6px;

          .coupon-info {
            display: flex;
            align-items: center;
            gap: 8px;

            .coupon-value {
              .value-amount {
                font-size: 14px;
                font-weight: 600;
                color: #f5222d;
              }

              .value-discount {
                font-size: 14px;
                font-weight: 600;
                color: #f5222d;
              }
            }

            .coupon-name {
              font-size: 12px;
              color: #52c41a;
            }
          }
        }
      }

      .available-coupons {
        .empty-coupons {
          display: flex;
          justify-content: center;
          align-items: center;
          padding: 40px 20px;
        }

        .coupons-list {
          .coupon-item {
            margin-bottom: 12px;
            border: 1px solid #f0f0f0;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;

            &:hover:not(.disabled) {
              border-color: #1890ff;
              box-shadow: 0 2px 8px rgba(24, 144, 255, 0.15);
            }

            &.selected {
              border-color: #1890ff;
              background: #f6ffed;
            }

            &.disabled {
              cursor: not-allowed;
              opacity: 0.6;
              background: #fafafa;
            }

            .coupon-card-inner {
              display: flex;
              padding: 16px;

              .coupon-left {
                width: 100px;
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                border-right: 1px dashed #d9d9d9;
                margin-right: 16px;

                .coupon-value-display {
                  .value-amount {
                    font-size: 20px;
                    font-weight: 600;
                    color: #f5222d;
                  }

                  .value-discount {
                    font-size: 20px;
                    font-weight: 600;
                    color: #f5222d;

                    small {
                      font-size: 12px;
                    }
                  }
                }

                .coupon-type {
                  font-size: 12px;
                  color: #8c8c8c;
                  margin-top: 4px;
                }
              }

              .coupon-right {
                flex: 1;
                display: flex;
                justify-content: space-between;
                align-items: center;

                .coupon-details {
                  flex: 1;

                  .coupon-name {
                    font-size: 16px;
                    font-weight: 600;
                    color: #262626;
                    margin-bottom: 4px;
                  }

                  .coupon-description {
                    font-size: 14px;
                    color: #8c8c8c;
                    margin-bottom: 8px;
                  }

                  .coupon-conditions {
                    display: flex;
                    flex-wrap: wrap;
                    gap: 8px;
                    margin-bottom: 4px;

                    .condition-item {
                      font-size: 12px;
                      color: #1890ff;
                      background: #f0f9ff;
                      padding: 2px 6px;
                      border-radius: 4px;
                    }
                  }

                  .coupon-expire {
                    font-size: 12px;
                    color: #8c8c8c;
                  }
                }

                .coupon-action {
                  margin-left: 16px;
                }
              }
            }

            .coupon-disabled-reason {
              display: flex;
              align-items: center;
              gap: 4px;
              padding: 8px 16px;
              background: #fff2f0;
              border-top: 1px solid #ffccc7;
              font-size: 12px;
              color: #ff4d4f;
            }
          }
        }

        .show-more-container {
          display: flex;
          justify-content: center;
          margin-top: 16px;
        }
      }
    }
  }

  // 响应式设计
  @media (max-width: 768px) {
    .coupon-selector-container {
      .coupon-content {
        .available-coupons {
          .coupons-list {
            .coupon-item {
              .coupon-card-inner {
                flex-direction: column;

                .coupon-left {
                  width: 100%;
                  border-right: none;
                  border-bottom: 1px dashed #d9d9d9;
                  margin-right: 0;
                  margin-bottom: 12px;
                  padding-bottom: 12px;
                }

                .coupon-right {
                  width: 100%;
                }
              }
            }
          }
        }
      }
    }
  }
</style>
