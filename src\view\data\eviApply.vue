<template>
  <div class="evidence-apply-container">
    <div class="page-header">
      <h1 class="page-title">数据存证申请</h1>
      <p class="page-description">通过数据存证，保障数据的真实性、完整性和不可篡改性</p>
    </div>

    <div class="content-wrapper">
      <a-card>
        <a-form :model="formData" layout="vertical">
          <!-- 存证信息 部分 -->
          <div class="section-header">
            <h2 class="section-title">存证信息</h2>
          </div>

          <a-form-item field="dataNumber" label="数据编号">
            <a-input v-model="formData.dataNumber" placeholder="自动生成的数据编号" disabled />
          </a-form-item>
          <a-form-item field="dataName" label="数据名称">
            <a-input v-model="formData.dataName" placeholder="请输入数据名称" />
          </a-form-item>
          <a-form-item field="certificationUnit" label="存证单位">
            <a-input v-model="formData.certificationUnit" placeholder="请输入存证单位" />
          </a-form-item>
          <a-form-item field="dataClassification" label="数据分类">
            <a-select
              v-model="formData.dataClassification"
              placeholder="请选择数据分类"
              :loading="dataCategoryLoading"
            >
              <a-option v-for="item in dataCategoryOptions" :key="item.value" :value="item.value">
                {{ item.label }}
              </a-option>
            </a-select>
          </a-form-item>
          <a-form-item field="dataLevel" label="数据级别">
            <a-select
              v-model="formData.dataLevel"
              placeholder="请选择数据级别"
              :loading="dataLevelLoading"
            >
              <a-option v-for="item in dataLevelOptions" :key="item.value" :value="item.value">
                {{ item.label }}
              </a-option>
            </a-select>
          </a-form-item>
          <a-form-item field="dataGenerationTime" label="数据生成时间">
            <a-date-picker v-model="formData.dataGenerationTime" show-time />
          </a-form-item>
          <a-form-item field="dataScale" label="数据规模">
            <a-input-group style="width: 100%">
              <a-input
                v-model="formData.dataScale"
                placeholder="请输入数据规模，单位为数据条数、字数、章节数、参数数量或一定数量的其他计量单位"
                style="width: 80%"
              />
              <a-select
                v-model="formData.dataScaleUnit"
                placeholder="请选择单位"
                style="width: 20%"
                :loading="dataScaleUnitLoading"
              >
                <a-option
                  v-for="item in dataScaleUnitOptions"
                  :key="item.value"
                  :value="item.value"
                >
                  {{ item.label }}
                </a-option>
              </a-select>
            </a-input-group>
          </a-form-item>
          <a-form-item field="dataResourceForm" label="数据资源形态">
            <a-select
              v-model="formData.dataResourceForm"
              placeholder="请选择数据资源形态"
              :loading="dataResourceTypeLoading"
            >
              <a-option
                v-for="item in dataResourceTypeOptions"
                :key="item.value"
                :value="item.value"
              >
                {{ item.label }}
              </a-option>
            </a-select>
          </a-form-item>
          <a-form-item field="dataResourceOwnership" label="数据资源权属申明">
            <a-checkbox-group
              v-model="formData.dataResourceOwnership"
              :loading="dataResourceOwnershipLoading"
            >
              <a-checkbox
                v-for="item in dataResourceOwnershipOptions"
                :key="item.value"
                :value="item.value"
              >
                {{ item.label }}
              </a-checkbox>
            </a-checkbox-group>
          </a-form-item>

          <a-form-item field="dataFile" label="数据样本文件">
            <a-upload
              action="/"
              :file-list="dataSampleFiles"
              :custom-request="customUploadRequest as any"
              :accept="uploadConfig.acceptTypes.join(',')"
              :limit="1"
              @change="handleFileChange"
            >
              <template #upload-button>
                <a-button type="outline">
                  <icon-upload />
                  上传文件
                </a-button>
              </template>
              <template #extra>
                <div class="upload-tip">
                  支持格式：PDF、Word、Excel、PPT、图片、压缩包等，单个文件不超过{{
                    formatFileSize(uploadConfig.maxSize)
                  }}，只能上传1个文件
                </div>
              </template>
            </a-upload>
          </a-form-item>

          <a-form-item field="restrictionDescription" label="限制情况说明">
            <a-textarea
              v-model="formData.restrictionDescription"
              placeholder="请输入限制情况说明，包括数据资源持有权、 数据加工使用权、 数据产品经营权的具体信息， 以及限制情况说明"
            />
          </a-form-item>
          <a-form-item field="otherDescription" label="其他描述">
            <a-textarea
              v-model="formData.otherDescription"
              placeholder="请输入其他描述，包括数据资源简介、含所属行业（或领域）、应用场景等"
            />
          </a-form-item>
          <a-form-item field="dataBlockHashValue" label="数据块哈希值">
            <a-input v-model="formData.dataBlockHashValue" placeholder="请输入数据块哈希值" />
          </a-form-item>
          <a-form-item field="dataAccessAddress" label="数据访问地址">
            <a-textarea
              v-model="formData.dataAccessAddress"
              placeholder="请输入数据访问地址，报货数据存储位置、服务网址、特定数据库表、特定文件系统目录或其他位置"
            />
          </a-form-item>
          <a-form-item field="extendedInfoHashValue" label="扩展信息哈希值">
            <a-input v-model="formData.extendedInfoHashValue" placeholder="请输入扩展信息哈希值" />
          </a-form-item>
          <a-form-item field="extendedInfoAccessAddress" label="扩展信息访问地址">
            <a-textarea
              v-model="formData.extendedInfoAccessAddress"
              placeholder="请输入扩展信息访问地址，要能够直接访问到所登记的数据的扩展信息"
            />
          </a-form-item>

          <!-- 扩展信息 部分 -->
          <div class="section-header">
            <h2 class="section-title">扩展信息</h2>
          </div>

          <a-form-item field="dataSource" label="数据来源信息">
            <a-input-group style="width: 100%">
              <a-select
                v-model="formData.dataSourceInfo"
                placeholder="请选择数据来源"
                style="width: 20%"
                :loading="dataSourceInfoLoading"
              >
                <a-option
                  v-for="item in dataSourceInfoOptions"
                  :key="item.value"
                  :value="item.value"
                >
                  {{ item.label }}
                </a-option>
              </a-select>
              <a-input
                v-model="formData.dataSourceSpecificInfo"
                placeholder="请输入具体来源信息，包括交易获得、合法授权,自行生产的具体信息"
                style="width: 80%"
              />
            </a-input-group>
          </a-form-item>

          <a-form-item
            field="dataSourceEvidence"
            label="数据来源佐证材料，包括交易凭证、合法授权凭证、自主生产立项材料及单向财务报表等佐证材料"
          >
            <div class="evidence-item">
              <div class="evidence-upload">
                <a-upload
                  action="/"
                  :file-list="dataSourceEvidenceFiles"
                  :custom-request="customUploadRequest as any"
                  :accept="uploadConfig.acceptTypes.join(',')"
                  :limit="1"
                  @change="
                    (files: any) => handleEvidenceFileChange(files, 'dataSourceEvidenceFiles')
                  "
                >
                  <template #upload-button>
                    <a-button type="outline">
                      <icon-upload />
                      上传佐证材料
                    </a-button>
                  </template>
                  <template #extra>
                    <div class="upload-tip">
                      支持多种格式，单个文件不超过{{
                        formatFileSize(uploadConfig.maxSize)
                      }}，只能上传1个文件
                    </div>
                  </template>
                </a-upload>
              </div>
            </div>
          </a-form-item>

          <a-form-item
            field="controlMeasureEvidence"
            label="有效控制措施佐证材料，包括制度文件、针对该数据资源的访问控制策略等能证明组织拥有或控制该数据资源的佐证材料"
          >
            <div class="evidence-item">
              <div class="evidence-upload">
                <a-upload
                  action="/"
                  :file-list="effectiveControlMeasureFiles"
                  :custom-request="customUploadRequest as any"
                  :accept="uploadConfig.acceptTypes.join(',')"
                  :limit="1"
                  @change="
                    (files: any) => handleEvidenceFileChange(files, 'effectiveControlMeasureFiles')
                  "
                >
                  <template #upload-button>
                    <a-button type="outline">
                      <icon-upload />
                      上传佐证材料
                    </a-button>
                  </template>
                  <template #extra>
                    <div class="upload-tip">
                      支持多种格式，单个文件不超过{{
                        formatFileSize(uploadConfig.maxSize)
                      }}，只能上传1个文件
                    </div>
                  </template>
                </a-upload>
              </div>
            </div>
          </a-form-item>

          <a-form-item
            field="personalInfoEvidence"
            label="个人信息采集的合法佐证材料，包括用户协议、生效时间、个人知情同意记录等佐证材料"
          >
            <div class="evidence-item">
              <div class="evidence-upload">
                <a-upload
                  action="/"
                  :file-list="personalInfoCollectionEvidenceFiles"
                  :custom-request="customUploadRequest as any"
                  :accept="uploadConfig.acceptTypes.join(',')"
                  :limit="1"
                  @change="
                    (files: any) =>
                      handleEvidenceFileChange(files, 'personalInfoCollectionEvidenceFiles')
                  "
                >
                  <template #upload-button>
                    <a-button type="outline">
                      <icon-upload />
                      上传佐证材料
                    </a-button>
                  </template>
                  <template #extra>
                    <div class="upload-tip">
                      支持多种格式，单个文件不超过{{
                        formatFileSize(uploadConfig.maxSize)
                      }}，只能上传1个文件
                    </div>
                  </template>
                </a-upload>
              </div>
            </div>
          </a-form-item>

          <a-form-item>
            <div class="btn-container">
              <a-space>
                <a-button @click="resetForm"> 重置 </a-button>
                <a-button type="outline" @click="saveTemporary"> 暂存 </a-button>
                <a-button type="outline" @click="loadSavedData"> 恢复 </a-button>
                <a-button type="primary" :loading="submitting" @click="submitForm">
                  提交存证申请
                </a-button>
              </a-space>
            </div>
          </a-form-item>
        </a-form>
      </a-card>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref, onMounted, onErrorCaptured } from 'vue';
  import { Message } from '@arco-design/web-vue';
  import { IconUpload } from '@arco-design/web-vue/es/icon';
  import { getDictDataListByType } from '@/http/api/global/proIndex';
  import { generateDataNumber, createCertificateInfo } from '@/http/api/data/index';
  import { useUpload, formatFileSize, validateFileType, validateFileSize } from '@/utils/upload';

  // 定义字典数据类型
  interface DictDataItem {
    id: number;
    label: string;
    value: string;
    dictType: string;
  }

  // 定义字典数据响应类型
  interface DictDataResponse {
    code: number;
    data?: DictDataItem[];
    msg?: string;
  }

  // 定义创建存证响应类型
  interface CreateCertificateResponse {
    code: number;
    data?: number; // 返回创建的存证ID
    msg?: string;
  }

  // 定义生成数据编号响应类型
  interface GenerateDataNumberResponse {
    code: number;
    data?: string; // 返回生成的数据编号
    msg?: string;
  }

  // 生成数据编号（本地备用方法）
  const generatedataNumberLocal = () => {
    const prefix = 'SCBA';
    const today = new Date();
    const year = today.getFullYear();
    const month = String(today.getMonth() + 1).padStart(2, '0');
    const day = String(today.getDate()).padStart(2, '0');
    const dateStr = `${year}${month}${day}`;
    const randomPart = Math.floor(10000000 + Math.random() * 90000000)
      .toString()
      .substring(0, 8);
    return `${prefix}${dateStr}${randomPart}`;
  };

  // 从后端接口获取数据编号
  const fetchDataNumber = async () => {
    try {
      const response = (await generateDataNumber()) as GenerateDataNumberResponse;
      if (response && response.code === 0 && response.data) {
        formData.value.dataNumber = response.data;
      } else {
        formData.value.dataNumber = generatedataNumberLocal();
      }
    } catch (error) {
      formData.value.dataNumber = generatedataNumberLocal();
      Message.warning('数据编号接口调用失败，已使用本地生成编号');
    }
  };

  // 通用字典数据获取函数
  const fetchDictOptions = async (
    dictType: string,
    optionsRef: any,
    loadingRef: any,
    errorMsg: string
  ) => {
    loadingRef.value = true;
    try {
      const response = (await getDictDataListByType(dictType)) as DictDataResponse;
      if (response && response.data) {
        optionsRef.value = response.data.map((item: DictDataItem) => ({
          label: item.label,
          value: item.value,
        }));
      } else {
        Message.warning(`获取${errorMsg}失败，请刷新页面重试`);
      }
    } catch (error) {
      Message.error(`获取${errorMsg}失败，请刷新页面重试`);
    } finally {
      loadingRef.value = false;
    }
  };

  // 获取各类字典选项
  const fetchDataCategoryOptions = () =>
    fetchDictOptions('data_classification', dataCategoryOptions, dataCategoryLoading, '数据分类');
  const fetchDataLevelOptions = () =>
    fetchDictOptions('data_level', dataLevelOptions, dataLevelLoading, '数据级别');
  const fetchDataScaleUnitOptions = () =>
    fetchDictOptions('data_scale_unit', dataScaleUnitOptions, dataScaleUnitLoading, '数据规模单位');
  const fetchDataResourceTypeOptions = () =>
    fetchDictOptions(
      'data_resource_form',
      dataResourceTypeOptions,
      dataResourceTypeLoading,
      '数据资源形态'
    );
  const fetchDataResourceOwnershipOptions = () =>
    fetchDictOptions(
      'data_resource_ownership',
      dataResourceOwnershipOptions,
      dataResourceOwnershipLoading,
      '数据资源权属申明'
    );
  const fetchDataSourceInfoOptions = () =>
    fetchDictOptions(
      'data_source_info',
      dataSourceInfoOptions,
      dataSourceInfoLoading,
      '数据来源信息'
    );

  const formData = ref({
    // === 基础存证信息 ===
    dataNumber: '', // 数据编号，系统自动生成的唯一标识符
    dataName: '', // 数据名称，用户输入的数据资源名称
    certificationUnit: '', // 存证单位，申请存证的机构或个人名称
    dataClassification: '', // 数据分类，从数据字典选择的分类类型
    dataLevel: '', // 数据级别，从数据字典选择的数据重要性级别
    dataGenerationTime: undefined as string | number | Date | undefined, // 数据生成时间，用户选择的时间

    // === 数据规模信息 ===
    dataScale: '', // 数据规模数值，如1000、500万等
    dataScaleUnit: '', // 数据规模单位，如条、GB、万条等

    // === 数据资源属性 ===
    dataResourceForm: '', // 数据资源形态，如结构化、非结构化等
    dataResourceOwnership: [], // 数据资源权属申明，多选数组

    // === 描述信息 ===
    restrictionDescription: '', // 限制情况说明，数据使用限制描述
    otherDescription: '', // 其他描述，补充说明信息

    // === 技术信息 ===
    dataBlockHashValue: '', // 数据块哈希值，数据完整性校验值
    dataAccessAddress: '', // 数据访问地址，数据存储或访问位置
    extendedInfoHashValue: '', // 扩展信息哈希值，扩展信息完整性校验
    extendedInfoAccessAddress: '', // 扩展信息访问地址，扩展信息存储位置

    // === 数据来源信息 ===
    dataSourceInfo: '', // 数据来源类型，从数据字典选择
    dataSourceSpecificInfo: '', // 数据来源具体信息，详细的来源描述

    // === 佐证材料信息 ===
    dataSourceEvidence: '', // 数据来源佐证材料描述
    controlMeasureEvidence: '', // 有效控制措施佐证材料描述
    personalInfoEvidence: '', // 个人信息采集佐证材料描述

    // === 文件路径信息 ===
    dataSampleFilePath: '', // 数据样本文件路径
    dataSourceEvidencePath: '', // 数据来源佐证材料文件路径
    controlMeasurePath: '', // 有效控制措施佐证材料文件路径
    personalInfoPath: '', // 个人信息采集佐证材料文件路径
  });

  // === 文件上传（每项限制1个文件） ===
  const dataSampleFiles = ref([]); // 数据样本文件（限制1个）
  const dataSourceEvidenceFiles = ref([]); // 数据来源佐证材料文件（限制1个）
  const effectiveControlMeasureFiles = ref([]); // 有效控制措施佐证材料文件（限制1个）
  const personalInfoCollectionEvidenceFiles = ref([]); // 个人信息采集佐证材料文件（限制1个）

  // === 状态管理 ===
  const submitting = ref(false); // 表单提交状态

  // === 数据字典选项和加载状态 ===

  // 数据分类相关（data_classification）
  const dataCategoryOptions = ref<{ label: string; value: string }[]>([]); // 数据分类选项列表
  const dataCategoryLoading = ref(false); // 数据分类加载状态

  // 数据级别相关（data_level）
  const dataLevelOptions = ref<{ label: string; value: string }[]>([]); // 数据级别选项列表
  const dataLevelLoading = ref(false); // 数据级别加载状态

  // 数据规模单位相关（data_scale_unit）
  const dataScaleUnitOptions = ref<{ label: string; value: string }[]>([]); // 数据规模单位选项列表
  const dataScaleUnitLoading = ref(false); // 数据规模单位加载状态

  // 数据资源形态相关（data_resource_form）
  const dataResourceTypeOptions = ref<{ label: string; value: string }[]>([]); // 数据资源形态选项列表
  const dataResourceTypeLoading = ref(false); // 数据资源形态加载状态

  // 数据资源权属申明相关（data_resource_ownership）
  const dataResourceOwnershipOptions = ref<{ label: string; value: string }[]>([]); // 数据资源权属申明选项列表
  const dataResourceOwnershipLoading = ref(false); // 数据资源权属申明加载状态

  // 数据来源信息相关（data_source_info）
  const dataSourceInfoOptions = ref<{ label: string; value: string }[]>([]); // 数据来源信息选项列表
  const dataSourceInfoLoading = ref(false); // 数据来源信息加载状态

  // 文件上传相关
  const { customRequest } = useUpload();

  // 文件上传配置
  const uploadConfig = {
    maxSize: 100 * 1024 * 1024, // 100MB
    acceptTypes: [
      '.pdf',
      '.doc',
      '.docx',
      '.xls',
      '.xlsx',
      '.ppt',
      '.pptx',
      '.txt',
      '.zip',
      '.rar',
      '.7z',
      '.jpg',
      '.jpeg',
      '.png',
      '.gif',
    ],
  };

  // 组件挂载时生成数据编号
  onMounted(async () => {
    // 从后端接口获取数据编号
    await fetchDataNumber();

    // 获取数据分类选项
    await fetchDataCategoryOptions();

    // 获取数据级别选项
    await fetchDataLevelOptions();

    // 获取数据规模单位选项
    await fetchDataScaleUnitOptions();

    // 获取数据资源形态选项
    await fetchDataResourceTypeOptions();

    // 获取数据资源权属申明选项
    await fetchDataResourceOwnershipOptions();

    // 获取数据来源信息选项
    await fetchDataSourceInfoOptions();

    // 检查是否有暂存数据
    const savedData = localStorage.getItem('evidenceFormTemp');
    if (savedData) {
      const parsedData = JSON.parse(savedData);
      // 显示提示，询问用户是否恢复暂存数据
      const saveTime = parsedData.saveTime || '未知时间';
      const isAutoSaved = parsedData.autoSaved || false;

      if (isAutoSaved) {
        Message.warning(
          `发现因提交失败而自动暂存的表单数据 (${saveTime})，您可以点击"恢复"按钮恢复数据后重新提交。`
        );
      } else {
        Message.info(`发现手动暂存的表单数据 (${saveTime})，您可以点击"恢复"按钮恢复数据。`);
      }
    }
  });

  // 加载暂存的数据
  const loadSavedData = () => {
    const savedData = localStorage.getItem('evidenceFormTemp');
    if (!savedData) {
      Message.warning('没有找到暂存的表单数据');
      return;
    }

    try {
      const parsedData = JSON.parse(savedData);
      const isAutoSaved = parsedData.autoSaved || false;

      // 从暂存数据恢复到formData
      formData.value = {
        dataNumber: parsedData.dataNumber || '',
        dataName: parsedData.dataName || '',
        certificationUnit: parsedData.certificationUnit || '',
        dataClassification: parsedData.dataClassification || '',
        dataLevel: parsedData.dataLevel || '',
        dataGenerationTime: parsedData.dataGenerationTime || null,
        dataScale: parsedData.dataScale || '',
        dataScaleUnit: parsedData.dataScaleUnit || '',
        dataResourceForm: parsedData.dataResourceForm || '',
        // 修复这里：正确解析数据资源权属申明字段
        dataResourceOwnership:
          typeof parsedData.dataResourceOwnership === 'string'
            ? JSON.parse(parsedData.dataResourceOwnership)
            : parsedData.dataResourceOwnership || [],
        restrictionDescription: parsedData.restrictionDescription || '',
        otherDescription: parsedData.otherDescription || '',
        dataBlockHashValue: parsedData.dataBlockHashValue || '',
        dataAccessAddress: parsedData.dataAccessAddress || '',
        extendedInfoHashValue: parsedData.extendedInfoHashValue || '',
        extendedInfoAccessAddress: parsedData.extendedInfoAccessAddress || '',
        dataSourceInfo: parsedData.dataSourceInfo || '',
        dataSourceSpecificInfo: parsedData.dataSourceSpecificInfo || '',
        // === 佐证材料信息 ===
        dataSourceEvidence: parsedData.dataSourceEvidence || '',
        controlMeasureEvidence: parsedData.controlMeasureEvidence || '',
        personalInfoEvidence: parsedData.personalInfoEvidence || '',
        // === 文件路径信息 ===
        dataSampleFilePath: parsedData.dataSampleFile || parsedData.dataSampleFilePath || '',
        dataSourceEvidencePath:
          parsedData.dataSourceEvidenceMaterial || parsedData.dataSourceEvidencePath || '',
        controlMeasurePath:
          parsedData.effectiveControlEvidenceMaterial || parsedData.controlMeasurePath || '',
        personalInfoPath:
          parsedData.personalInfoCollectionEvidenceMaterial || parsedData.personalInfoPath || '',
      };

      // 恢复文件列表
      if (parsedData._files) {
        if (parsedData._files.dataSampleFiles) {
          dataSampleFiles.value = parsedData._files.dataSampleFiles;
        }
        if (parsedData._files.dataSourceEvidenceFiles) {
          dataSourceEvidenceFiles.value = parsedData._files.dataSourceEvidenceFiles;
        }
        if (parsedData._files.effectiveControlMeasureFiles) {
          effectiveControlMeasureFiles.value = parsedData._files.effectiveControlMeasureFiles;
        }
        if (parsedData._files.personalInfoCollectionEvidenceFiles) {
          personalInfoCollectionEvidenceFiles.value =
            parsedData._files.personalInfoCollectionEvidenceFiles;
        }
      }

      // 根据是否为自动保存显示不同的成功消息
      if (isAutoSaved) {
        Message.success('已恢复因提交失败而自动暂存的表单数据，您现在可以重新提交');
      } else {
        Message.success('已恢复手动暂存的表单数据');
      }
    } catch (error) {
      Message.error('恢复暂存数据失败');
      console.error(error);
    }
  };

  // 文件上传前的验证
  const beforeUpload = (file: File) => {
    // 验证文件类型
    if (!validateFileType(file, uploadConfig.acceptTypes)) {
      Message.error(`不支持的文件类型: ${file.name}`);
      return false;
    }

    // 验证文件大小
    if (!validateFileSize(file, uploadConfig.maxSize)) {
      Message.error(`文件大小超过限制: ${formatFileSize(uploadConfig.maxSize)}`);
      return false;
    }

    return true;
  };

  // 处理数据样本文件变更
  const handleFileChange = fileList => {
    console.log('数据样本文件变更:', fileList);
    dataSampleFiles.value = fileList;

    // 检查是否有上传完成的文件，并记录路径
    const uploadedFile = fileList.find(file => file.status === 'done' && file.response);
    if (uploadedFile && uploadedFile.response) {
      console.log('数据样本文件上传完成:', uploadedFile.response);
      formData.value.dataSampleFilePath = uploadedFile.response.data || uploadedFile.response.url;
    }
  };

  // 处理佐证材料文件变更
  const handleEvidenceFileChange = (fileList, fieldName) => {
    console.log(`${fieldName}变更:`, fileList);

    // 更新对应的文件列表
    if (fieldName === 'dataSourceEvidenceFiles') {
      dataSourceEvidenceFiles.value = fileList;

      // 检查是否有上传完成的文件，并记录路径
      const uploadedFile = fileList.find(file => file.status === 'done' && file.response);
      if (uploadedFile && uploadedFile.response) {
        console.log('数据来源佐证材料上传完成:', uploadedFile.response);
        formData.value.dataSourceEvidencePath =
          uploadedFile.response.data || uploadedFile.response.url;
      }
    } else if (fieldName === 'effectiveControlMeasureFiles') {
      effectiveControlMeasureFiles.value = fileList;

      // 检查是否有上传完成的文件，并记录路径
      const uploadedFile = fileList.find(file => file.status === 'done' && file.response);
      if (uploadedFile && uploadedFile.response) {
        console.log('有效控制措施佐证材料上传完成:', uploadedFile.response);
        formData.value.controlMeasurePath = uploadedFile.response.data || uploadedFile.response.url;
      }
    } else if (fieldName === 'personalInfoCollectionEvidenceFiles') {
      personalInfoCollectionEvidenceFiles.value = fileList;

      // 检查是否有上传完成的文件，并记录路径
      const uploadedFile = fileList.find(file => file.status === 'done' && file.response);
      if (uploadedFile && uploadedFile.response) {
        console.log('个人信息采集佐证材料上传完成:', uploadedFile.response);
        formData.value.personalInfoPath = uploadedFile.response.data || uploadedFile.response.url;
      }
    }
  };

  // 自定义上传请求处理
  const customUploadRequest = (options: any) => {
    const file = options.file || options.fileItem?.file || options.fileItem;

    if (!file) {
      options.onError(new Error('未找到文件对象'));
      return { abort: () => {} };
    }

    // 上传前验证
    if (!beforeUpload(file)) {
      options.onError(new Error('文件验证失败'));
      return { abort: () => {} };
    }

    console.log('开始上传文件:', file.name);

    // 使用上传工具
    try {
      const result = customRequest({
        file,
        onProgress: (event: any) => {
          console.log('上传进度:', event.percent);
          options.onProgress && options.onProgress(event);
        },
        onSuccess: (response: any) => {
          console.log('上传成功，原始响应:', response);

          // 确保响应中包含文件路径
          let processedResponse = response;
          if (response && !response.data && response.url) {
            console.log('使用url作为data:', response.url);
            processedResponse = { ...response, data: response.url };
          }

          // 调用成功回调
          options.onSuccess && options.onSuccess(processedResponse);

          // 根据上传控件的名称或ID，直接更新formData中的路径
          const uploaderId = options.uploaderId || '';
          if (uploaderId.includes('dataSample')) {
            formData.value.dataSampleFilePath = processedResponse.data || processedResponse.url;
            console.log('直接设置数据样本文件路径:', formData.value.dataSampleFilePath);
          } else if (uploaderId.includes('dataSource')) {
            formData.value.dataSourceEvidencePath = processedResponse.data || processedResponse.url;
            console.log('直接设置数据来源佐证材料路径:', formData.value.dataSourceEvidencePath);
          } else if (uploaderId.includes('controlMeasure')) {
            formData.value.controlMeasurePath = processedResponse.data || processedResponse.url;
            console.log('直接设置有效控制措施佐证材料路径:', formData.value.controlMeasurePath);
          } else if (uploaderId.includes('personalInfo')) {
            formData.value.personalInfoPath = processedResponse.data || processedResponse.url;
            console.log('直接设置个人信息采集佐证材料路径:', formData.value.personalInfoPath);
          }
        },
        onError: (error: any) => {
          console.error('上传失败:', error);
          // 增强错误处理，提供更具体的错误信息
          const errorMsg = error?.message || '文件上传失败，请重试';
          Message.error(errorMsg);
          options.onError && options.onError(error);
        },
      });

      if (result && typeof result.then === 'function') {
        // 如果返回Promise，包装成UploadRequest对象
        return { abort: () => {} };
      }
      return result || { abort: () => {} };
    } catch (error) {
      console.error('上传调用失败:', error);
      return { abort: () => {} };
    }
  };

  // 收集文件路径
  const collectFilePaths = (files: any[]) => {
    return files
      .filter(file => file.status === 'done' && file.response?.data)
      .map(file => file.response.data)
      .join(',');
  };

  // 表单验证
  const validateForm = () => {
    const errors: string[] = [];

    if (!formData.value.dataName?.trim()) {
      errors.push('请输入数据名称');
    }
    if (!formData.value.certificationUnit?.trim()) {
      errors.push('请输入存证单位');
    }
    if (!formData.value.dataClassification) {
      errors.push('请选择数据分类');
    }
    if (!formData.value.dataLevel) {
      errors.push('请选择数据级别');
    }
    if (!formData.value.dataGenerationTime) {
      errors.push('请选择数据生成时间');
    }

    if (errors.length > 0) {
      Message.error(errors[0]);
      return false;
    }
    return true;
  };

  // 自动暂存表单数据（提交失败时调用）
  const autoSaveFormData = () => {
    try {
      // 构建与submitData相同结构的数据
      const tempData = {
        dataNumber: formData.value.dataNumber,
        dataName: formData.value.dataName,
        certificationUnit: formData.value.certificationUnit,
        dataClassification: formData.value.dataClassification,
        dataLevel: formData.value.dataLevel,
        dataGenerationTime: formData.value.dataGenerationTime,
        dataScale: formData.value.dataScale,
        dataScaleUnit: formData.value.dataScaleUnit,
        dataResourceForm: formData.value.dataResourceForm,
        dataResourceOwnership: Array.isArray(formData.value.dataResourceOwnership)
          ? JSON.stringify(formData.value.dataResourceOwnership)
          : JSON.stringify([formData.value.dataResourceOwnership]),
        dataSampleFile: formData.value.dataSampleFilePath,
        restrictionDescription: formData.value.restrictionDescription,
        otherDescription: formData.value.otherDescription,
        dataBlockHashValue: formData.value.dataBlockHashValue,
        dataAccessAddress: formData.value.dataAccessAddress,
        extendedInfoHashValue: formData.value.extendedInfoHashValue,
        extendedInfoAccessAddress: formData.value.extendedInfoAccessAddress,
        dataSourceInfo: formData.value.dataSourceInfo,
        dataSourceSpecificInfo: formData.value.dataSourceSpecificInfo,
        dataSourceEvidenceMaterial: formData.value.dataSourceEvidencePath,
        effectiveControlEvidenceMaterial: formData.value.controlMeasurePath,
        personalInfoCollectionEvidenceMaterial: formData.value.personalInfoPath,
        // 额外保存文件列表信息，用于恢复上传控件状态
        _files: {
          dataSampleFiles: dataSampleFiles.value,
          dataSourceEvidenceFiles: dataSourceEvidenceFiles.value,
          effectiveControlMeasureFiles: effectiveControlMeasureFiles.value,
          personalInfoCollectionEvidenceFiles: personalInfoCollectionEvidenceFiles.value,
        },
        saveTime: new Date().toLocaleString(),
        autoSaved: true, // 标记为自动保存
      };

      localStorage.setItem('evidenceFormTemp', JSON.stringify(tempData));
      console.log('表单数据已自动暂存到本地');
    } catch (error) {
      console.error('自动暂存表单数据失败:', error);
    }
  };

  // 提交存证申请表单
  const submitForm = async () => {
    if (!validateForm()) {
      return;
    }

    submitting.value = true;

    try {
      // 打印当前文件列表状态，用于调试
      console.log('提交前文件状态:', {
        dataSampleFiles: dataSampleFiles.value,
        dataSourceEvidenceFiles: dataSourceEvidenceFiles.value,
        effectiveControlMeasureFiles: effectiveControlMeasureFiles.value,
        personalInfoCollectionEvidenceFiles: personalInfoCollectionEvidenceFiles.value,
      });

      console.log('提交前文件路径:', {
        dataSampleFilePath: formData.value.dataSampleFilePath,
        dataSourceEvidencePath: formData.value.dataSourceEvidencePath,
        controlMeasurePath: formData.value.controlMeasurePath,
        personalInfoPath: formData.value.personalInfoPath,
      });

      // 检查数据样本文件是否已上传
      if (dataSampleFiles.value.length === 0 || !formData.value.dataSampleFilePath) {
        Message.warning('请上传数据样本文件');
        submitting.value = false;
        return;
      }

      // 收集所有文件路径
      const collectFilePaths = files => {
        return files
          .filter(file => file.status === 'done' && file.response?.data)
          .map(file => file.response.data)
          .join(',');
      };

      // 确保文件路径存在
      if (!formData.value.dataSourceEvidencePath && dataSourceEvidenceFiles.value.length > 0) {
        formData.value.dataSourceEvidencePath = collectFilePaths(dataSourceEvidenceFiles.value);
      }

      if (!formData.value.controlMeasurePath && effectiveControlMeasureFiles.value.length > 0) {
        formData.value.controlMeasurePath = collectFilePaths(effectiveControlMeasureFiles.value);
      }

      if (
        !formData.value.personalInfoPath &&
        personalInfoCollectionEvidenceFiles.value.length > 0
      ) {
        formData.value.personalInfoPath = collectFilePaths(
          personalInfoCollectionEvidenceFiles.value
        );
      }

      // 构建提交数据
      const submitData = {
        dataNumber: formData.value.dataNumber,
        dataName: formData.value.dataName,
        certificationUnit: formData.value.certificationUnit,
        dataClassification: formData.value.dataClassification,
        dataLevel: formData.value.dataLevel,
        // dataGenerationTime: formData.value.dataGenerationTime
        //   ? new Date(formData.value.dataGenerationTime).toLocaleString('sv-SE').replace('T', ' ')
        //   : undefined,
        dataGenerationTime: formData.value.dataGenerationTime
          ? new Date(formData.value.dataGenerationTime).getTime().toString()
          : undefined,
        dataScale: formData.value.dataScale,
        dataScaleUnit: formData.value.dataScaleUnit,
        dataResourceForm: formData.value.dataResourceForm,
        dataResourceOwnership: Array.isArray(formData.value.dataResourceOwnership)
          ? JSON.stringify(formData.value.dataResourceOwnership)
          : JSON.stringify([formData.value.dataResourceOwnership]),
        // 确保文件路径字段正确传递
        dataSampleFile: formData.value.dataSampleFilePath,
        dataSourceEvidenceMaterial: formData.value.dataSourceEvidencePath,
        effectiveControlEvidenceMaterial: formData.value.controlMeasurePath,
        personalInfoCollectionEvidenceMaterial: formData.value.personalInfoPath,
        restrictionDescription: formData.value.restrictionDescription,
        otherDescription: formData.value.otherDescription,
        dataBlockHashValue: formData.value.dataBlockHashValue,
        dataAccessAddress: formData.value.dataAccessAddress,
        extendedInfoHashValue: formData.value.extendedInfoHashValue,
        extendedInfoAccessAddress: formData.value.extendedInfoAccessAddress,
        dataSourceInfo: formData.value.dataSourceInfo,
        dataSourceSpecificInfo: formData.value.dataSourceSpecificInfo,
      };

      // 调试输出，检查提交的数据
      console.log('提交的表单数据:', submitData);

      const response = (await createCertificateInfo(submitData)) as CreateCertificateResponse;

      if (response && response.code === 0) {
        Message.success('存证申请提交成功！');
        // 提交成功后清除暂存数据
        localStorage.removeItem('evidenceFormTemp');
        await resetFormData();
      } else {
        // 提交失败时自动暂存表单数据
        autoSaveFormData();

        // 提供更具体的错误信息
        const errorMsg = response?.msg || '提交失败，请稍后重试';
        Message.error(
          `${errorMsg}，表单数据已自动暂存到本地，您可以稍后点击"恢复"按钮恢复数据后重新提交`
        );
        console.error('表单提交失败:', response);
      }
    } catch (error: any) {
      // 提交异常时也自动暂存表单数据
      autoSaveFormData();

      // 增强错误处理，记录详细错误信息
      console.error('表单提交异常:', error);
      const errorMsg = error?.message || '提交失败，请稍后重试';
      Message.error(
        `${errorMsg}，表单数据已自动暂存到本地，您可以稍后点击"恢复"按钮恢复数据后重新提交`
      );
    } finally {
      submitting.value = false;
    }
  };

  // 重置表单数据
  const resetFormData = async () => {
    formData.value = {
      dataNumber: '',
      dataName: '',
      certificationUnit: '',
      dataClassification: '',
      dataLevel: '',
      dataGenerationTime: undefined,
      dataScale: '',
      dataScaleUnit: '',
      dataResourceForm: '',
      dataResourceOwnership: [],
      restrictionDescription: '',
      otherDescription: '',
      dataBlockHashValue: '',
      dataAccessAddress: '',
      extendedInfoHashValue: '',
      extendedInfoAccessAddress: '',
      dataSourceInfo: '',
      dataSourceSpecificInfo: '',
      dataSourceEvidence: '',
      controlMeasureEvidence: '',
      personalInfoEvidence: '',
      dataSampleFilePath: '',
      dataSourceEvidencePath: '',
      controlMeasurePath: '',
      personalInfoPath: '',
    };

    dataSampleFiles.value = [];
    dataSourceEvidenceFiles.value = [];
    effectiveControlMeasureFiles.value = [];
    personalInfoCollectionEvidenceFiles.value = [];

    await fetchDataNumber();
  };

  // 重置表单
  const resetForm = async () => {
    await resetFormData();
    Message.success('表单已重置');
  };

  // 暂存表单数据到本地存储
  const saveTemporary = () => {
    // 构建与submitData相同结构的数据
    const tempData = {
      dataNumber: formData.value.dataNumber,
      dataName: formData.value.dataName,
      certificationUnit: formData.value.certificationUnit,
      dataClassification: formData.value.dataClassification,
      dataLevel: formData.value.dataLevel,
      dataGenerationTime: formData.value.dataGenerationTime,
      dataScale: formData.value.dataScale,
      dataScaleUnit: formData.value.dataScaleUnit,
      dataResourceForm: formData.value.dataResourceForm,
      dataResourceOwnership: Array.isArray(formData.value.dataResourceOwnership)
        ? JSON.stringify(formData.value.dataResourceOwnership)
        : JSON.stringify([formData.value.dataResourceOwnership]),
      dataSampleFile: formData.value.dataSampleFilePath,
      restrictionDescription: formData.value.restrictionDescription,
      otherDescription: formData.value.otherDescription,
      dataBlockHashValue: formData.value.dataBlockHashValue,
      dataAccessAddress: formData.value.dataAccessAddress,
      extendedInfoHashValue: formData.value.extendedInfoHashValue,
      extendedInfoAccessAddress: formData.value.extendedInfoAccessAddress,
      dataSourceInfo: formData.value.dataSourceInfo,
      dataSourceSpecificInfo: formData.value.dataSourceSpecificInfo,
      dataSourceEvidenceMaterial: formData.value.dataSourceEvidencePath,
      effectiveControlEvidenceMaterial: formData.value.controlMeasurePath,
      personalInfoCollectionEvidenceMaterial: formData.value.personalInfoPath,
      // 额外保存文件列表信息，用于恢复上传控件状态
      _files: {
        dataSampleFiles: dataSampleFiles.value,
        dataSourceEvidenceFiles: dataSourceEvidenceFiles.value,
        effectiveControlMeasureFiles: effectiveControlMeasureFiles.value,
        personalInfoCollectionEvidenceFiles: personalInfoCollectionEvidenceFiles.value,
      },
      saveTime: new Date().toLocaleString(),
    };

    localStorage.setItem('evidenceFormTemp', JSON.stringify(tempData));
    Message.success('表单数据已暂存');
  };

  // 添加组件级错误捕获
  onErrorCaptured((err, instance, info) => {
    console.error('组件错误:', err);
    Message.error('页面发生错误，请刷新重试');
    // 返回false阻止错误向上传播
    return false;
  });
</script>

<style lang="scss" scoped>
  .evidence-apply-container {
    padding: 40px;
  }

  .page-header {
    margin-bottom: 40px;
    text-align: center;
  }

  .page-title {
    font-size: 32px;
    font-weight: bold;
    color: #1d2129;
    margin-bottom: 16px;
  }

  .page-description {
    font-size: 16px;
    color: #4e5969;
  }

  .content-wrapper {
    max-width: 800px;
    margin: 0 auto;
  }

  .section-header {
    margin: 24px 0 16px;
  }

  .section-title {
    font-size: 18px;
    font-weight: 500;
    color: #1d2129;
    position: relative;
    padding-left: 12px;

    &:before {
      content: '';
      position: absolute;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
      width: 4px;
      height: 16px;
      background-color: #165dff;
      border-radius: 2px;
    }
  }

  .evidence-item {
    width: 100%;
    display: flex;
    flex-direction: column;
  }

  .evidence-textarea {
    width: 100%;
    margin-bottom: 12px;
  }

  .evidence-textarea :deep(.arco-textarea) {
    width: 100%;
  }

  .evidence-upload {
    width: 100%;
  }

  .btn-container {
    display: flex;
    justify-content: center;
    width: 100%;
  }

  .upload-tip {
    font-size: 12px;
    color: #86909c;
    margin-top: 4px;
    line-height: 1.4;
  }
</style>
