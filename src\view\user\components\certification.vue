<template>
  <a-spin :loading="loading" style="width: 100%">
    <!-- <a-card class="general-card" :bordered="false" title="个人实名认证">
            <template #extra>
                <a-link>重新认证</a-link>
            </template>
            <a-descriptions class="card-content" :data="organDetail" :column="3" align="right" layout="inline-horizontal"
                :label-style="{ fontWeight: 'normal' }" :value-style="{
                    width: '200px',
                    paddingLeft: '8px',
                    textAlign: 'left',
                }">
            </a-descriptions>
        </a-card> -->
    <a-card class="general-card" :bordered="false" title="企业认证状态">
      <template #extra>
        <a-tag v-if="userInfo.auditStatus == null" color="#86909c" size="large"> 未认证 </a-tag>
        <a-tag v-else-if="userInfo.auditStatus == 0" color="#165dff" size="large">
          认证审核中
        </a-tag>
        <a-tag v-else-if="userInfo.auditStatus == 1" color="#f53f3f" size="large">
          认证未通过
        </a-tag>
        <a-tag v-else color="#00b42a" size="large"> 认证通过 </a-tag>
      </template>
      <a-empty v-if="userInfo.auditStatus === null" class="py-20">
        <template #image>
          <IconIdcard />
        </template>
        <div>您还没有企业实名认证</div>
        <router-link to="/authentication">
          <a-link>前往认证</a-link>
        </router-link>
      </a-empty>
      <template v-if="userInfo.auditStatus !== null">
        <a-descriptions
          :data="organDetail"
          title="企业信息"
          size="large"
          :column="3"
          layout="inline-vertical"
          :value-style="{
            width: '300px',
            marginBottom: '10px',
          }"
        />
        <a-divider />
        <a-descriptions
          :data="bankDetail"
          title="支付信息"
          size="large"
          :column="3"
          layout="inline-vertical"
          :value-style="{
            width: '300px',
            marginBottom: '10px',
          }"
        />
        <a-divider />
        <a-descriptions
          :data="userDetail"
          title="法人信息"
          size="large"
          :column="3"
          layout="inline-vertical"
          :value-style="{
            width: '300px',
            marginBottom: '10px',
          }"
        />
      </template>
    </a-card>
  </a-spin>
</template>

<script lang="ts" setup>
  import { ref, computed } from 'vue';
  import { portalUserInfo } from '@/http/api/authentication';
  import { IconIdcard } from '@arco-design/web-vue/es/icon';
  const loading = ref(false);
  const userInfo = ref({
    auditRemark: '',
    auditStatus: null,
    bankAcctName: '',
    bankAcctNum: '',
    bankName: '',
    branchBankName: '',
    businessAddress: '',
    businessScope: '',
    certificateTypeName: '',
    city: '',
    companyEmail: '',
    companyPhone: '',
    county: '',
    effectiveDate: '',
    idCardNo: '',
    legalAddress: '',
    legalEmail: '',
    legalName: '',
    legalPhone: '',
    province: '',
    registerArea: '',
    registerName: '',
    registerNo: '',
    typeName: '',
  });
  const getUserInfo = () => {
    portalUserInfo().then(res => {
      userInfo.value = res as {
        auditRemark: '';
        auditStatus: null;
        bankAcctName: '';
        bankAcctNum: '';
        bankName: '';
        branchBankName: '';
        businessAddress: '';
        businessScope: '';
        certificateTypeName: '';
        city: '';
        companyEmail: '';
        companyPhone: '';
        county: '';
        effectiveDate: '';
        idCardNo: '';
        legalAddress: '';
        legalEmail: '';
        legalName: '';
        legalPhone: '';
        province: '';
        registerArea: '';
        registerName: '';
        registerNo: '';
        typeName: '';
      };
    });
  };
  getUserInfo();

  const organDetail = computed(() => {
    return [
      {
        label: '企业名称',
        value: userInfo.value.registerName,
      },
      {
        label: '企业类型',
        value: userInfo.value.typeName,
      },
      {
        label: '企业证件类型',
        value: userInfo.value.certificateTypeName,
      },
      {
        label: '统一社会信用代码',
        value: userInfo.value.registerNo,
      },
      {
        label: '注册区域',
        value: userInfo.value.registerArea,
      },
      {
        label: '公司营业地址',
        value: userInfo.value.businessAddress,
      },
      {
        label: '主营业务/业务范围',
        value: userInfo.value.businessScope,
      },
      {
        label: '企业邮箱',
        value: userInfo.value.companyEmail,
      },
    ];
  });
  const userDetail = computed(() => {
    return [
      {
        label: '法人姓名',
        value: userInfo.value.legalName,
      },
      {
        label: '法人手机号',
        value: userInfo.value.legalPhone,
      },
      {
        label: '法人邮箱',
        value: userInfo.value.legalEmail,
      },
      {
        label: '法人身份证号',
        value: userInfo.value.idCardNo,
      },
      {
        label: '有效日期',
        value: userInfo.value.effectiveDate,
      },
      {
        label: '法人联系地址',
        value: userInfo.value.legalAddress,
      },
    ];
  });
  const bankDetail = computed(() => {
    return [
      {
        label: '银行名称',
        value: userInfo.value.bankName,
      },
      {
        label: '银行账号名称',
        value: userInfo.value.bankAcctName,
      },
      {
        label: '银行账号号码',
        value: userInfo.value.bankAcctNum,
      },
      {
        label: '支行名称',
        value: userInfo.value.branchBankName,
      },
    ];
  });
</script>

<style scoped lang="scss">
  .general-card {
    width: 100%;
    border-radius: 4px;
    border: none;

    & > :deep(.arco-card-header) {
      height: auto;
      border: none;
      padding: 0 16px;
    }
  }

  .card-content {
    width: 100%;
    padding: 20px;
    background-color: rgb(var(--gray-1));
  }

  .item-label {
    min-width: 98px;
    text-align: right;
    color: var(--color-text-8);

    &:after {
      content: ':';
    }
  }
</style>
