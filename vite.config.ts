/** @format */

import { defineConfig, loadEnv } from 'vite';
import vue from '@vitejs/plugin-vue';
import path from 'path';
import viteCompression from 'vite-plugin-compression';
import { VitePWA } from 'vite-plugin-pwa';

// 条件导入可视化插件
function getVisualizerPlugin() {
  if (process.env.npm_lifecycle_event?.includes('analyze')) {
    // eslint-disable-next-line @typescript-eslint/no-var-requires
    const visualizer = require('rollup-plugin-visualizer').visualizer;
    return visualizer({
      open: true,
      filename: 'dist/stats.html',
      gzipSize: true,
      brotliSize: true,
    });
  }
  return null;
}

// https://vitejs.dev/config/
export default defineConfig(({ command, mode }) => {
  // 根据当前工作目录中的 `mode` 加载 .env 文件
  // 设置第三个参数为 '' 来加载所有环境变量，而不管是否有 `VITE_` 前缀
  const env = loadEnv(mode, process.cwd(), '');

  // 构建插件数组
  const vitePlugins = [
    vue(),
    // gzip压缩
    viteCompression({
      verbose: true, // 是否在控制台输出压缩结果
      disable: false, // 是否禁用
      threshold: 10240, // 体积大小超过此阈值会被压缩，单位为b
      algorithm: 'gzip', // 压缩算法
      ext: '.gz', // 生成的压缩包后缀
      deleteOriginFile: false, // 压缩后是否删除源文件
    }),
    // PWA 支持
    VitePWA({
      registerType: 'autoUpdate',
      includeAssets: ['favicon.ico', 'robots.txt', 'apple-touch-icon.png'],
      manifest: {
        name: 'Vue3 应用',
        short_name: 'Vue3 App',
        theme_color: '#ffffff',
      },
    }),
  ];

  // 添加可视化插件
  const visualizerPlugin = getVisualizerPlugin();
  if (visualizerPlugin) {
    vitePlugins.push(visualizerPlugin);
  }

  return {
    base: env.VITE_BASE_URL || './', //打包路径
    plugins: vitePlugins,
    resolve: {
      alias: {
        '@': path.resolve(__dirname, 'src'),
      },
    },
    //配置代理
    server: {
      // host: '0.0.0.0',
      // port: 3000,
      // open: true,
      // open: false,
      // https: false,
      proxy: {
        '/api': {
          target: env.VITE_SOP_API_TARGET,
          // target: "http://*************:41390/api",  //  刘霖峰
          // target: "http://************:8083",        //  刘霖峰
          // target: "http://************:8083",        //  牟毅
          // target: 'http://**************:9012',	    //  熊朝志
          // target: 'http://**************:9012',	    //  裴浩
          changeOrigin: true,
          rewrite: path => path.replace(/^\/api/, ''),
        },
        '/pro': {
          target: env.VITE_PRO_API_TARGET,
          changeOrigin: true,
          rewrite: path => path.replace(/^\/pro/, ''),
        },
      },
    },
    // 生产环境打包配置
    //去除 console debugger
    build: {
      // 使用terser进行混淆和压缩
      minify: 'terser',
      terserOptions: {
        compress: {
          drop_console: env.VITE_DROP_CONSOLE === 'true',
          drop_debugger: true,
        },
      },
      // 静态资源处理
      assetsDir: 'static/assets',
      // 静态资源引入添加hash
      rollupOptions: {
        output: {
          chunkFileNames: 'static/js/[name]-[hash].js',
          entryFileNames: 'static/js/[name]-[hash].js',
          assetFileNames: 'static/[ext]/[name]-[hash].[ext]',
          // 代码分割策略
          manualChunks: {
            vue: ['vue', 'vue-router', 'pinia'],
            arco: ['@arco-design/web-vue'],
          },
        },
      },
      // 启用gzip压缩
      // 注意：需要安装vite-plugin-compression
      // 资源大小限制，小于此值的资源不会被处理
      assetsInlineLimit: 4096,
    },
    // css处理
    css: {
      preprocessorOptions: {
        scss: {
          // 使用 @use 代替 @import
          additionalData: `@use "@/styles/_variables.scss" as *;`,
        },
      },
    },
  };
});
