# 文件上传调试指南

## 问题描述

在调用后台创建存证接口时，以下文件路径字段都是空的：
- `dataSampleFile` (数据样本文件)
- `dataSourceEvidenceMaterial` (数据来源佐证材料)
- `effectiveControlEvidenceMaterial` (有效控制措施佐证材料)
- `personalInfoCollectionEvidenceMaterial` (个人信息采集佐证材料)

## 调试步骤

### 1. 检查文件上传流程

我们已经添加了详细的调试日志，请按以下步骤进行调试：

#### 步骤1：上传文件
1. 打开浏览器开发者工具的控制台
2. 尝试上传一个文件到任意文件上传组件
3. 观察控制台输出

#### 步骤2：查看上传日志
上传过程中应该看到以下日志：

```
=== customUploadRequest 被调用 ===
参数: {file: File, onProgress: function, onSuccess: function, onError: function}
环境变量 VITE_UPLOAD_TYPE: [client/server]
环境变量 VITE_PRO_API_TARGET: [API地址]
文件信息: {name: "文件名", size: 文件大小, type: "文件类型"}
文件验证通过，开始调用 customRequest
customRequest 调用结果: [上传结果]
```

#### 步骤3：查看文件状态变化
上传完成后应该看到：

```
=== handleFileChange 被调用 === (或 handleEvidenceFileChange)
传入的文件列表: [文件数组]
文件 0: {
  name: "文件名",
  status: "done", // 应该是 "done"
  response: {data: "文件路径"}, // 应该包含文件路径
  uid: "唯一ID",
  percent: 100
}
更新后的 fileList.value: [更新后的文件列表]
```

#### 步骤4：查看提交时的文件收集
点击"提交存证申请"按钮时应该看到：

```
=== 开始收集文件路径 ===
fileList.value: [文件列表]
sourceEvidenceFiles.value: [文件列表]
controlMeasureFiles.value: [文件列表]
personalInfoFiles.value: [文件列表]

收集文件路径 - 输入文件列表: [文件列表]
文件 文件名 - 状态: done, 响应数据: "文件路径", 有效: true
收集到的文件路径: "文件路径"

=== 文件路径收集结果 ===
dataSampleFilePaths: "文件路径"
sourceEvidencePaths: "文件路径"
controlMeasurePaths: "文件路径"
personalInfoPaths: "文件路径"
```

### 2. 常见问题排查

#### 问题1：文件上传失败
**症状**: 看到上传错误日志
**解决方案**:
1. 检查网络连接
2. 确认后端上传接口是否正常
3. 检查文件大小和类型是否符合要求

#### 问题2：文件状态不是 "done"
**症状**: 文件状态显示为 "uploading" 或 "error"
**解决方案**:
1. 等待上传完成
2. 检查上传接口返回的响应格式
3. 确认 `customRequest` 正确调用了 `onSuccess`

#### 问题3：response.data 为空
**症状**: 文件状态是 "done" 但 response.data 为空
**解决方案**:
1. 检查上传工具返回的响应格式
2. 确认后端接口返回正确的文件路径
3. 检查环境变量配置

#### 问题4：文件列表没有更新
**症状**: handleFileChange 没有被调用
**解决方案**:
1. 检查 Upload 组件的 @change 事件绑定
2. 确认组件的 :file-list 绑定正确

### 3. 环境配置检查

确认以下环境变量配置正确：

```env
# 上传模式：client（前端直连）或 server（后端代理）
VITE_UPLOAD_TYPE=client

# PRO API 地址
VITE_PRO_API_TARGET=http://your-api-server
```

### 4. 手动测试

如果自动调试无法定位问题，可以手动测试：

```javascript
// 在浏览器控制台中执行
console.log('当前文件列表状态:');
console.log('fileList:', window.fileList?.value);
console.log('sourceEvidenceFiles:', window.sourceEvidenceFiles?.value);
console.log('controlMeasureFiles:', window.controlMeasureFiles?.value);
console.log('personalInfoFiles:', window.personalInfoFiles?.value);

// 手动调用收集函数
console.log('手动收集文件路径:');
// 注意：这需要在 Vue 组件内部执行
```

### 5. 预期的正常流程

1. **文件选择**: 用户选择文件
2. **开始上传**: customUploadRequest 被调用
3. **上传进度**: onProgress 被调用，显示上传进度
4. **上传成功**: onSuccess 被调用，文件状态变为 "done"
5. **状态更新**: handleFileChange 被调用，更新文件列表
6. **路径收集**: 提交时 collectFilePaths 提取文件路径
7. **数据提交**: 文件路径包含在提交数据中

### 6. 故障排除清单

- [ ] 文件上传组件正确绑定了 :file-list
- [ ] @change 事件正确绑定了处理函数
- [ ] customUploadRequest 正确调用了 onSuccess
- [ ] 上传响应包含正确的文件路径
- [ ] 文件状态正确更新为 "done"
- [ ] collectFilePaths 函数正确过滤和提取路径
- [ ] 环境变量配置正确

## 联系支持

如果按照以上步骤仍无法解决问题，请提供：
1. 完整的控制台日志
2. 网络请求详情（开发者工具 Network 标签）
3. 环境变量配置
4. 上传的文件信息（大小、类型等）
