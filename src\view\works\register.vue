<template>
  <div class="page-container">
    <div class="page-header">
      <h1 class="page-title">作品著作权登记申请</h1>
      <p class="page-description">填写相关信息完成作品著作权登记申请</p>
    </div>

    <div class="content-wrapper">
      <a-steps :current="currentStep" class="mb-8">
        <a-step title="填写基本信息" />
        <a-step title="上传作品材料" />
        <a-step title="提交申请" />
        <a-step title="完成" />
      </a-steps>

      <div class="form-container">
        <div v-if="currentStep === 0">
          <h2 class="form-section-title">作品基本信息</h2>
          <a-form :model="formData" layout="vertical">
            <a-form-item field="worksTitle" label="作品名称" required>
              <a-input v-model="formData.worksTitle" placeholder="请输入作品名称" />
            </a-form-item>
            <a-form-item field="worksType" label="作品类型" required>
              <a-select v-model="formData.worksType" placeholder="请选择作品类型">
                <a-option value="literary">文字作品</a-option>
                <a-option value="music">音乐作品</a-option>
                <a-option value="art">美术作品</a-option>
                <a-option value="photography">摄影作品</a-option>
                <a-option value="film">影视作品</a-option>
                <a-option value="graphic">图形作品</a-option>
                <a-option value="architecture">建筑作品</a-option>
                <a-option value="other">其他作品</a-option>
              </a-select>
            </a-form-item>
            <a-form-item field="worksCategory" label="作品分类" required>
              <a-input v-model="formData.worksCategory" placeholder="请输入作品分类" />
            </a-form-item>
            <a-form-item field="creationDate" label="创作完成日期" required>
              <a-date-picker v-model="formData.creationDate" placeholder="请选择创作完成日期" />
            </a-form-item>
            <a-form-item field="publicationDate" label="首次发表日期">
              <a-date-picker
                v-model="formData.publicationDate"
                placeholder="请选择首次发表日期（如未发表可不填）"
              />
            </a-form-item>
            <a-form-item field="authorName" label="作者姓名" required>
              <a-input v-model="formData.authorName" placeholder="请输入作者姓名" />
            </a-form-item>
            <a-form-item field="authorNationality" label="作者国籍" required>
              <a-input v-model="formData.authorNationality" placeholder="请输入作者国籍" />
            </a-form-item>
            <a-form-item field="copyrightOwner" label="著作权人" required>
              <a-input v-model="formData.copyrightOwner" placeholder="请输入著作权人" />
            </a-form-item>
            <a-form-item field="creationNature" label="创作性质" required>
              <a-select v-model="formData.creationNature" placeholder="请选择创作性质">
                <a-option value="original">原创</a-option>
                <a-option value="adaptation">改编</a-option>
                <a-option value="translation">翻译</a-option>
                <a-option value="compilation">汇编</a-option>
              </a-select>
            </a-form-item>
            <a-form-item field="worksDescription" label="作品简介" required>
              <a-textarea
                v-model="formData.worksDescription"
                placeholder="请简要描述作品的主要内容和特点"
                :max-length="500"
                show-word-limit
              />
            </a-form-item>
          </a-form>
          <div class="step-actions">
            <a-button type="primary" @click="nextStep"> 下一步 </a-button>
          </div>
        </div>

        <div v-if="currentStep === 1">
          <h2 class="form-section-title">上传作品材料</h2>
          <p class="upload-description">请上传以下材料用于著作权登记：</p>

          <a-form :model="formData" layout="vertical">
            <a-form-item field="worksSample" label="作品样本" required>
              <a-upload
                action="/"
                :custom-request="handleFileUpload"
                :file-list="formData.worksSampleFiles"
                accept=".pdf,.doc,.docx,.jpg,.jpeg,.png,.mp3,.mp4,.avi"
                @change="handleWorksSampleChange"
              >
                <template #upload-button>
                  <a-button>
                    <template #icon>
                      <icon-upload />
                    </template>
                    上传作品样本
                  </a-button>
                </template>
                <template #extra>
                  <div class="upload-tip">
                    支持PDF、Word、图片、音频、视频等格式，大小不超过100MB
                  </div>
                </template>
              </a-upload>
            </a-form-item>

            <a-form-item field="worksDescription" label="作品说明书" required>
              <a-upload
                action="/"
                :custom-request="handleFileUpload"
                :file-list="formData.worksDescriptionFiles"
                accept=".pdf,.doc,.docx"
                @change="handleWorksDescriptionChange"
              >
                <template #upload-button>
                  <a-button>
                    <template #icon>
                      <icon-upload />
                    </template>
                    上传作品说明书
                  </a-button>
                </template>
                <template #extra>
                  <div class="upload-tip">详细说明作品的创作过程、内容特点等</div>
                </template>
              </a-upload>
            </a-form-item>

            <a-form-item field="identityProof" label="身份证明" required>
              <a-upload
                action="/"
                :custom-request="handleFileUpload"
                :file-list="formData.identityProofFiles"
                accept=".pdf,.jpg,.jpeg,.png"
                @change="handleIdentityProofChange"
              >
                <template #upload-button>
                  <a-button>
                    <template #icon>
                      <icon-upload />
                    </template>
                    上传身份证明
                  </a-button>
                </template>
                <template #extra>
                  <div class="upload-tip">个人身份证或企业营业执照</div>
                </template>
              </a-upload>
            </a-form-item>

            <a-form-item field="copyrightProof" label="著作权归属证明">
              <a-upload
                action="/"
                :custom-request="handleFileUpload"
                :file-list="formData.copyrightProofFiles"
                accept=".pdf,.doc,.docx,.jpg,.jpeg,.png"
                @change="handleCopyrightProofChange"
              >
                <template #upload-button>
                  <a-button>
                    <template #icon>
                      <icon-upload />
                    </template>
                    上传著作权归属证明
                  </a-button>
                </template>
                <template #extra>
                  <div class="upload-tip">如委托创作、职务作品等需要提供相关证明文件</div>
                </template>
              </a-upload>
            </a-form-item>
          </a-form>

          <div class="step-actions">
            <a-button @click="prevStep"> 上一步 </a-button>
            <a-button type="primary" :disabled="!canProceedToStep2" @click="nextStep">
              下一步
            </a-button>
          </div>
        </div>

        <div v-if="currentStep === 2">
          <h2 class="form-section-title">申请人信息</h2>
          <a-form :model="applicantData" layout="vertical">
            <a-form-item field="name" label="申请人姓名/企业名称" required>
              <a-input v-model="applicantData.name" placeholder="请输入申请人姓名或企业名称" />
            </a-form-item>
            <a-form-item field="idNumber" label="身份证号/统一社会信用代码" required>
              <a-input
                v-model="applicantData.idNumber"
                placeholder="请输入身份证号或统一社会信用代码"
              />
            </a-form-item>
            <a-form-item field="address" label="联系地址" required>
              <a-input v-model="applicantData.address" placeholder="请输入联系地址" />
            </a-form-item>
            <a-form-item field="phone" label="联系电话" required>
              <a-input v-model="applicantData.phone" placeholder="请输入联系电话" />
            </a-form-item>
            <a-form-item field="email" label="电子邮箱" required>
              <a-input v-model="applicantData.email" placeholder="请输入电子邮箱" />
            </a-form-item>

            <a-form-item field="agreement">
              <a-checkbox v-model="applicantData.agreement">
                我已阅读并同意<a href="javascript:void(0)">《作品著作权登记服务协议》</a
                >，承诺所提供的信息真实有效。
              </a-checkbox>
            </a-form-item>
          </a-form>

          <div class="step-actions">
            <a-button @click="prevStep"> 上一步 </a-button>
            <a-button
              type="primary"
              :loading="submitting"
              :disabled="!applicantData.agreement"
              @click="submitRegistration"
            >
              提交申请
            </a-button>
          </div>
        </div>

        <div v-if="currentStep === 3">
          <div class="success-container">
            <icon-check-circle class="success-icon" />
            <h2 class="success-title">登记申请提交成功！</h2>
            <p class="success-message">
              您的作品著作权登记申请已成功提交，我们将尽快审核您的申请。
            </p>
            <div class="success-info">
              <p>
                <span class="label">申请编号：</span><span class="value">{{ registrationId }}</span>
              </p>
              <p>
                <span class="label">提交时间：</span><span class="value">{{ submissionTime }}</span>
              </p>
              <p><span class="label">预计审核：</span><span class="value">5-10个工作日</span></p>
            </div>
            <a-alert type="info" class="mt-4">
              <template #message>
                您可以在"个人中心-登记申请"中查看申请进度。审核通过后，将进入公示期（15天），公示期满无异议后将颁发作品著作权登记证书。
              </template>
            </a-alert>
            <div class="success-actions">
              <a-button type="primary" @click="goToApplicationList"> 查看我的申请 </a-button>
              <a-button @click="goToHome"> 返回首页 </a-button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref, computed } from 'vue';
  import { useRouter } from 'vue-router';
  import { Message } from '@arco-design/web-vue';
  import { IconUpload, IconCheckCircle } from '@arco-design/web-vue/es/icon';
  import { createWorksRegistrationInfo } from '@/http/api/works';

  const router = useRouter();

  // 当前步骤
  const currentStep = ref(0);

  // 表单数据
  const formData = ref({
    worksTitle: '',
    worksType: '',
    worksCategory: '',
    creationDate: '',
    publicationDate: '',
    authorName: '',
    authorNationality: '中国',
    copyrightOwner: '',
    creationNature: '',
    worksDescription: '',
    worksSampleFiles: [] as any[],
    worksDescriptionFiles: [] as any[],
    identityProofFiles: [] as any[],
    copyrightProofFiles: [] as any[],
  });

  // 申请人数据
  const applicantData = ref({
    name: '',
    idNumber: '',
    address: '',
    phone: '',
    email: '',
    agreement: false,
  });

  // 提交状态
  const submitting = ref(false);
  const registrationId = ref('');
  const submissionTime = ref('');

  // 计算属性
  const canProceedToStep2 = computed(() => {
    return (
      formData.value.worksSampleFiles.length > 0 &&
      formData.value.worksDescriptionFiles.length > 0 &&
      formData.value.identityProofFiles.length > 0
    );
  });

  // 步骤控制方法
  const nextStep = () => {
    if (currentStep.value < 3) {
      currentStep.value++;
    }
  };

  const prevStep = () => {
    if (currentStep.value > 0) {
      currentStep.value--;
    }
  };

  // 文件上传处理
  const handleFileUpload = (option: any) => {
    // 这里应该实现实际的文件上传逻辑
    // 暂时模拟上传成功
    const timer = setTimeout(() => {
      option.onSuccess();
    }, 1000);

    // 返回包含 abort 方法的对象
    return {
      abort: () => {
        clearTimeout(timer);
      },
    };
  };

  const handleWorksSampleChange = (fileList: any[]) => {
    formData.value.worksSampleFiles = fileList;
  };

  const handleWorksDescriptionChange = (fileList: any[]) => {
    formData.value.worksDescriptionFiles = fileList;
  };

  const handleIdentityProofChange = (fileList: any[]) => {
    formData.value.identityProofFiles = fileList;
  };

  const handleCopyrightProofChange = (fileList: any[]) => {
    formData.value.copyrightProofFiles = fileList;
  };

  // 提交申请
  const submitRegistration = async () => {
    submitting.value = true;
    try {
      // 构建提交数据
      const submitData = {
        ...formData.value,
        ...applicantData.value,
        creationDate: formData.value.creationDate
          ? new Date(formData.value.creationDate).toISOString().split('T')[0]
          : '',
        publicationDate: formData.value.publicationDate
          ? new Date(formData.value.publicationDate).toISOString().split('T')[0]
          : '',
      };

      // 调用API提交申请
      const response = await createWorksRegistrationInfo(submitData);

      // 设置成功信息
      registrationId.value = `ZP${Date.now()}`;
      submissionTime.value = new Date().toLocaleString();

      // 跳转到成功页面
      nextStep();

      Message.success('作品著作权登记申请提交成功！');
    } catch (error) {
      Message.error('提交失败，请重试');
      console.error('提交申请失败:', error);
    } finally {
      submitting.value = false;
    }
  };

  // 页面跳转方法
  const goToApplicationList = () => {
    router.push('/user/applications');
  };

  const goToHome = () => {
    router.push('/works/page');
  };
</script>

<style lang="scss" scoped>
  .page-container {
    width: 100%;
    min-height: 100vh;
    background-color: #f7f8fa;
    padding-bottom: 40px;
  }

  .page-header {
    background: linear-gradient(135deg, #165dff 0%, #4080ff 100%);
    color: white;
    padding: 40px 0;
    text-align: center;

    .page-title {
      font-size: 32px;
      font-weight: 600;
      margin-bottom: 12px;
    }

    .page-description {
      font-size: 16px;
      opacity: 0.9;
      margin: 0;
    }
  }

  .content-wrapper {
    max-width: 800px;
    margin: 0 auto;
    padding: 40px 24px;
  }

  .form-container {
    background: white;
    border-radius: 12px;
    padding: 32px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  }

  .form-section-title {
    font-size: 20px;
    font-weight: 600;
    color: #1d2129;
    margin-bottom: 24px;
    padding-bottom: 12px;
    border-bottom: 2px solid #e5e6eb;
  }

  .upload-description {
    color: #4e5969;
    margin-bottom: 24px;
    line-height: 1.6;
  }

  .upload-tip {
    color: #86909c;
    font-size: 12px;
    margin-top: 4px;

    a {
      color: #165dff;
      text-decoration: none;

      &:hover {
        text-decoration: underline;
      }
    }
  }

  .step-actions {
    display: flex;
    justify-content: center;
    gap: 16px;
    margin-top: 32px;
    padding-top: 24px;
    border-top: 1px solid #e5e6eb;

    .arco-btn {
      min-width: 120px;
    }
  }

  .success-container {
    text-align: center;
    padding: 40px 0;

    .success-icon {
      font-size: 64px;
      color: #00b42a;
      margin-bottom: 24px;
    }

    .success-title {
      font-size: 24px;
      font-weight: 600;
      color: #1d2129;
      margin-bottom: 16px;
    }

    .success-message {
      font-size: 16px;
      color: #4e5969;
      margin-bottom: 32px;
      line-height: 1.6;
    }

    .success-info {
      background: #f7f8fa;
      border-radius: 8px;
      padding: 24px;
      margin-bottom: 24px;
      text-align: left;

      p {
        margin-bottom: 8px;
        display: flex;
        align-items: center;

        &:last-child {
          margin-bottom: 0;
        }

        .label {
          color: #86909c;
          width: 100px;
          flex-shrink: 0;
        }

        .value {
          color: #1d2129;
          font-weight: 500;
        }
      }
    }

    .success-actions {
      display: flex;
      justify-content: center;
      gap: 16px;
      margin-top: 32px;

      .arco-btn {
        min-width: 140px;
      }
    }
  }

  :deep(.arco-form-item-label) {
    font-weight: 500;
    color: #1d2129;
  }

  :deep(.arco-steps) {
    margin-bottom: 40px;
  }

  :deep(.arco-upload-list-item) {
    border-radius: 6px;
  }

  :deep(.arco-upload-list-item-error) {
    border-color: #f53f3f;
    background-color: #ffece8;
  }

  :deep(.arco-upload-list-item-done) {
    border-color: #00b42a;
    background-color: #e8ffea;
  }

  @media (max-width: 768px) {
    .content-wrapper {
      padding: 24px 16px;
    }

    .form-container {
      padding: 24px 16px;
    }

    .page-header {
      padding: 32px 16px;

      .page-title {
        font-size: 24px;
      }

      .page-description {
        font-size: 14px;
      }
    }

    .step-actions {
      flex-direction: column;

      .arco-btn {
        width: 100%;
      }
    }

    .success-actions {
      flex-direction: column;

      .arco-btn {
        width: 100%;
      }
    }
  }
</style>
