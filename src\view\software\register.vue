<template>
  <div class="page-container">
    <div class="page-header">
      <h1 class="page-title">软件著作权登记申请</h1>
      <p class="page-description">填写相关信息完成软件著作权登记申请</p>
    </div>

    <div class="content-wrapper">
      <a-steps :current="currentStep" class="mb-8">
        <a-step title="填写基本信息" />
        <a-step title="上传登记材料" />
        <a-step title="提交申请" />
        <a-step title="完成" />
      </a-steps>

      <a-card class="form-card">
        <div v-if="currentStep === 0">
          <h2 class="form-section-title">基本信息</h2>
          <a-form :model="formData" layout="vertical">
            <a-form-item field="softwareName" label="软件名称" required>
              <a-input v-model="formData.softwareName" placeholder="请输入软件全称" />
            </a-form-item>
            <a-form-item field="softwareShortName" label="软件简称">
              <a-input v-model="formData.softwareShortName" placeholder="请输入软件简称（可选）" />
            </a-form-item>
            <a-form-item field="softwareVersion" label="软件版本号" required>
              <a-input v-model="formData.softwareVersion" placeholder="如：V1.0、2.1.3等" />
            </a-form-item>
            <a-form-item field="completionDate" label="开发完成日期" required>
              <a-date-picker v-model="formData.completionDate" style="width: 100%" />
            </a-form-item>
            <a-form-item field="developmentType" label="开发方式" required>
              <a-select v-model="formData.developmentType" placeholder="请选择开发方式">
                <a-option value="original"> 原始开发 </a-option>
                <a-option value="commissioned"> 委托开发 </a-option>
                <a-option value="joint"> 合作开发 </a-option>
                <a-option value="adaptation"> 修改、改编 </a-option>
              </a-select>
            </a-form-item>
            <a-form-item field="copyrightOwners" label="著作权人" required>
              <a-input
                v-model="formData.copyrightOwners"
                placeholder="请输入著作权人（多个请用逗号分隔）"
              />
            </a-form-item>
            <a-form-item field="functionalDescription" label="功能简述" required>
              <a-textarea
                v-model="formData.functionalDescription"
                placeholder="请简要描述软件的主要功能和用途"
              />
            </a-form-item>
          </a-form>
          <div class="step-actions">
            <a-button type="primary" @click="nextStep"> 下一步 </a-button>
          </div>
        </div>

        <div v-if="currentStep === 1">
          <h2 class="form-section-title">上传登记材料</h2>
          <p class="upload-description">请上传以下材料用于著作权登记：</p>

          <a-form :model="formData" layout="vertical">
            <a-form-item field="sourceCode" label="源代码" required>
              <a-upload
                action="/"
                :custom-request="handleFileUpload"
                :file-list="formData.sourceCodeFiles"
                accept=".zip,.rar,.7z"
                @change="handleSourceCodeChange"
              >
                <template #upload-button>
                  <a-button>
                    <template #icon>
                      <icon-upload />
                    </template>
                    上传源代码（连续30页）
                  </a-button>
                </template>
                <template #extra>
                  <div class="upload-tip">源代码前后30页，PDF格式</div>
                </template>
              </a-upload>
            </a-form-item>

            <a-form-item field="documentationFiles" label="软件说明书" required>
              <a-upload
                action="/"
                :custom-request="handleFileUpload"
                :file-list="formData.documentationFiles"
                accept=".doc,.docx,.pdf"
                @change="handleDocumentationChange"
              >
                <template #upload-button>
                  <a-button>
                    <template #icon>
                      <icon-upload />
                    </template>
                    上传软件说明书
                  </a-button>
                </template>
              </a-upload>
            </a-form-item>

            <a-form-item field="identityProof" label="身份证明" required>
              <a-upload
                action="/"
                :custom-request="handleFileUpload"
                :file-list="formData.identityProofFiles"
                accept=".jpg,.jpeg,.png,.pdf"
                @change="handleIdentityProofChange"
              >
                <template #upload-button>
                  <a-button>
                    <template #icon>
                      <icon-upload />
                    </template>
                    上传身份证明
                  </a-button>
                </template>
                <template #extra>
                  <div class="upload-tip">个人提供身份证，企业提供营业执照</div>
                </template>
              </a-upload>
            </a-form-item>

            <a-form-item field="authorshipCertificate" label="著作权声明书" required>
              <a-upload
                action="/"
                :custom-request="handleFileUpload"
                :file-list="formData.authorshipCertificateFiles"
                accept=".doc,.docx,.pdf"
                @change="handleAuthorshipCertificateChange"
              >
                <template #upload-button>
                  <a-button>
                    <template #icon>
                      <icon-upload />
                    </template>
                    上传著作权声明书
                  </a-button>
                </template>
                <template #extra>
                  <div class="upload-tip">
                    <a href="javascript:void(0)" @click="downloadTemplate">下载声明书模板</a>
                  </div>
                </template>
              </a-upload>
            </a-form-item>
          </a-form>

          <div class="step-actions">
            <a-button @click="prevStep"> 上一步 </a-button>
            <a-button type="primary" :disabled="!canProceedToStep2" @click="nextStep">
              下一步
            </a-button>
          </div>
        </div>

        <div v-if="currentStep === 2">
          <h2 class="form-section-title">申请人信息</h2>
          <a-form :model="applicantData" layout="vertical">
            <a-form-item field="applicantType" label="申请人类型" required>
              <a-radio-group v-model="applicantData.applicantType">
                <a-radio value="personal"> 个人 </a-radio>
                <a-radio value="company"> 企业/组织 </a-radio>
              </a-radio-group>
            </a-form-item>

            <template v-if="applicantData.applicantType === 'personal'">
              <a-form-item field="name" label="姓名" required>
                <a-input v-model="applicantData.name" placeholder="请输入申请人姓名" />
              </a-form-item>
              <a-form-item field="idNumber" label="身份证号" required>
                <a-input v-model="applicantData.idNumber" placeholder="请输入身份证号码" />
              </a-form-item>
              <a-form-item field="address" label="通讯地址" required>
                <a-input v-model="applicantData.address" placeholder="请输入详细通讯地址" />
              </a-form-item>
            </template>

            <template v-if="applicantData.applicantType === 'company'">
              <a-form-item field="name" label="企业名称" required>
                <a-input v-model="applicantData.name" placeholder="请输入企业全称" />
              </a-form-item>
              <a-form-item field="businessLicense" label="统一社会信用代码" required>
                <a-input
                  v-model="applicantData.businessLicense"
                  placeholder="请输入统一社会信用代码"
                />
              </a-form-item>
              <a-form-item field="address" label="注册地址" required>
                <a-input v-model="applicantData.address" placeholder="请输入企业注册地址" />
              </a-form-item>
              <a-form-item field="contactPerson" label="联系人" required>
                <a-input v-model="applicantData.contactPerson" placeholder="请输入联系人姓名" />
              </a-form-item>
            </template>

            <a-form-item field="phone" label="联系电话" required>
              <a-input v-model="applicantData.phone" placeholder="请输入联系电话" />
            </a-form-item>
            <a-form-item field="email" label="电子邮箱" required>
              <a-input v-model="applicantData.email" placeholder="请输入电子邮箱" />
            </a-form-item>

            <a-form-item field="agreement">
              <a-checkbox v-model="applicantData.agreement">
                我已阅读并同意<a href="javascript:void(0)">《软件著作权登记服务协议》</a
                >，承诺所提供的信息真实有效。
              </a-checkbox>
            </a-form-item>
          </a-form>

          <div class="step-actions">
            <a-button @click="prevStep"> 上一步 </a-button>
            <a-button
              type="primary"
              :loading="submitting"
              :disabled="!applicantData.agreement"
              @click="submitRegistration"
            >
              提交申请
            </a-button>
          </div>
        </div>

        <div v-if="currentStep === 3">
          <div class="success-container">
            <icon-check-circle class="success-icon" />
            <h2 class="success-title">登记申请提交成功！</h2>
            <p class="success-message">
              您的软件著作权登记申请已成功提交，我们将尽快审核您的申请。
            </p>
            <div class="success-info">
              <p>
                <span class="label">申请编号：</span><span class="value">{{ registrationId }}</span>
              </p>
              <p>
                <span class="label">提交时间：</span><span class="value">{{ submissionTime }}</span>
              </p>
              <p><span class="label">预计审核：</span><span class="value">5-7个工作日</span></p>
            </div>
            <a-alert type="info" class="mt-4">
              <template #message>
                您可以在"个人中心-登记申请"中查看申请进度。审核通过后，将进入公示期（15天），公示期满无异议后将颁发软件著作权登记证书。
              </template>
            </a-alert>
            <div class="success-actions">
              <a-button type="primary" @click="goToApplicationList"> 查看我的申请 </a-button>
              <a-button @click="resetForm"> 继续登记 </a-button>
            </div>
          </div>
        </div>
      </a-card>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref, reactive, computed } from 'vue';
  import { useRouter } from 'vue-router';
  import { Message } from '@arco-design/web-vue';
  import { IconUpload, IconCheckCircle } from '@arco-design/web-vue/es/icon';
  import { createSoftwareRegistrationInfo } from '@/http/api/software/index';

  const router = useRouter();
  const currentStep = ref(0);
  const submitting = ref(false);
  const registrationId = ref('');
  const submissionTime = ref('');

  // 表单数据
  const formData = reactive({
    softwareName: '',
    softwareShortName: '',
    softwareVersion: '',
    completionDate: undefined as string | number | Date | undefined,
    developmentType: '',
    copyrightOwners: '',
    functionalDescription: '',
    sourceCodeFiles: [],
    documentationFiles: [],
    identityProofFiles: [],
    authorshipCertificateFiles: [],
  });

  // 申请人信息
  const applicantData = reactive({
    applicantType: 'personal',
    name: '',
    idNumber: '',
    businessLicense: '',
    address: '',
    contactPerson: '',
    phone: '',
    email: '',
    agreement: false,
  });

  // 检查是否可以进行到下一步
  const canProceedToStep2 = computed(() => {
    return (
      formData.sourceCodeFiles.length > 0 &&
      formData.documentationFiles.length > 0 &&
      formData.identityProofFiles.length > 0 &&
      formData.authorshipCertificateFiles.length > 0
    );
  });

  // 处理文件上传
  const handleFileUpload = (option: any) => {
    // 这里只是模拟上传，实际应用中需要对接后端上传接口
    setTimeout(() => {
      option.onSuccess();
    }, 1000);

    return {
      abort: () => {},
    };
  };

  // 处理各种文件变更
  const handleSourceCodeChange = fileList => {
    formData.sourceCodeFiles = fileList;
  };

  const handleDocumentationChange = fileList => {
    formData.documentationFiles = fileList;
  };

  const handleIdentityProofChange = fileList => {
    formData.identityProofFiles = fileList;
  };

  const handleAuthorshipCertificateChange = fileList => {
    formData.authorshipCertificateFiles = fileList;
  };

  // 下载模板
  const downloadTemplate = () => {
    Message.info('正在下载模板文件...');
    // 实际应用中这里需要对接下载接口
  };

  // 下一步
  const nextStep = () => {
    if (currentStep.value === 0) {
      // 验证第一步表单
      if (
        !formData.softwareName ||
        !formData.softwareVersion ||
        !formData.completionDate ||
        !formData.developmentType ||
        !formData.copyrightOwners ||
        !formData.functionalDescription
      ) {
        Message.error('请填写所有必填项');
        return;
      }
    } else if (currentStep.value === 1) {
      // 验证第二步表单
      if (!canProceedToStep2.value) {
        Message.error('请上传所有必要的文件材料');
        return;
      }
    }

    currentStep.value += 1;
  };

  // 上一步
  const prevStep = () => {
    currentStep.value -= 1;
  };

  // 提交注册申请
  const submitRegistration = async () => {
    if (!applicantData.agreement) {
      Message.error('请先同意《软件著作权登记服务协议》');
      return;
    }

    // 验证表单
    if (applicantData.applicantType === 'personal') {
      if (
        !applicantData.name ||
        !applicantData.idNumber ||
        !applicantData.address ||
        !applicantData.phone ||
        !applicantData.email
      ) {
        Message.error('请填写所有必填项');
        return;
      }
    } else {
      if (
        !applicantData.name ||
        !applicantData.businessLicense ||
        !applicantData.address ||
        !applicantData.contactPerson ||
        !applicantData.phone ||
        !applicantData.email
      ) {
        Message.error('请填写所有必填项');
        return;
      }
    }

    submitting.value = true;

    try {
      // 构造符合API要求的数据结构
      const registrationData = {
        softwareFullName: formData.softwareName,
        versionNumber: formData.softwareVersion,
        completionDate: formData.completionDate
          ? new Date(formData.completionDate).toISOString().split('T')[0]
          : '',
        developmentMethod: formData.developmentType,
        softwareDescription: formData.functionalDescription,
        registrantEmail: applicantData.email,
        softwareIntroduction: formData.functionalDescription,
        remark: '通过前端页面提交',
      };

      // 实际应用中这里调用后端API
      const response = await createSoftwareRegistrationInfo(registrationData);

      // 模拟成功响应
      registrationId.value = 'SW' + new Date().getTime();
      submissionTime.value = new Date().toLocaleString();

      Message.success('登记申请提交成功');
      currentStep.value = 3;
    } catch (error) {
      console.error('提交登记申请失败:', error);
      Message.error('提交失败，请稍后重试');
    } finally {
      submitting.value = false;
    }
  };

  // 查看申请列表
  const goToApplicationList = () => {
    router.push('/software/page/application-list');
  };

  // 重置表单开始新的登记
  const resetForm = () => {
    // 重置表单数据
    Object.keys(formData).forEach(key => {
      if (Array.isArray(formData[key])) {
        formData[key] = [];
      } else if (typeof formData[key] === 'boolean') {
        formData[key] = false;
      } else {
        formData[key] = '';
      }
    });

    // 重置申请人数据
    Object.keys(applicantData).forEach(key => {
      if (key === 'applicantType') {
        applicantData[key] = 'personal';
      } else if (typeof applicantData[key] === 'boolean') {
        applicantData[key] = false;
      } else {
        applicantData[key] = '';
      }
    });

    currentStep.value = 0;
  };
</script>

<style lang="scss" scoped>
  .page-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 40px 20px;
  }

  .page-header {
    text-align: center;
    margin-bottom: 40px;

    .page-title {
      font-size: 2rem;
      font-weight: bold;
      margin-bottom: 12px;
      color: #1d2129;
    }

    .page-description {
      font-size: 1.1rem;
      color: #4e5969;
    }
  }

  .content-wrapper {
    background: #fff;
    border-radius: 8px;
  }

  .form-card {
    margin-bottom: 40px;
  }

  .form-section-title {
    font-size: 1.5rem;
    margin-bottom: 24px;
    color: #1d2129;
    font-weight: 500;
  }

  .upload-description {
    margin-bottom: 20px;
    color: #4e5969;
  }

  .upload-tip {
    font-size: 0.9rem;
    color: #86909c;
    margin-top: 4px;
  }

  .step-actions {
    display: flex;
    justify-content: space-between;
    margin-top: 30px;

    a-button + a-button {
      margin-left: 12px;
    }
  }

  .success-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 40px 0;

    .success-icon {
      font-size: 64px;
      color: #00b42a;
      margin-bottom: 20px;
    }

    .success-title {
      font-size: 1.8rem;
      font-weight: bold;
      color: #1d2129;
      margin-bottom: 12px;
    }

    .success-message {
      font-size: 1.1rem;
      color: #4e5969;
      margin-bottom: 24px;
    }

    .success-info {
      background: #f2f3f5;
      padding: 16px 24px;
      border-radius: 4px;
      width: 100%;
      max-width: 500px;
      margin-bottom: 24px;

      p {
        margin-bottom: 8px;
        display: flex;

        &:last-child {
          margin-bottom: 0;
        }

        .label {
          color: #86909c;
          width: 100px;
        }

        .value {
          color: #1d2129;
          font-weight: 500;
        }
      }
    }

    .success-actions {
      display: flex;
      gap: 16px;
      margin-top: 24px;
    }
  }
</style>
