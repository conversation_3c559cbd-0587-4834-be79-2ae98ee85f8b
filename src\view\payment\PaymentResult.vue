<template>
  <div class="payment-result-container">
    <!-- 页面头部 -->
    <div class="result-header">
      <div class="header-content">
        <h1 class="page-title">支付结果</h1>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="result-main">
      <div class="main-content">
        <!-- 支付结果展示 -->
        <div class="result-card">
          <a-result :status="resultStatus" :title="resultTitle" :subtitle="resultSubtitle">
            <template #icon>
              <div class="result-icon">
                <icon-check-circle v-if="isSuccess" class="success-icon" />
                <icon-close-circle v-else-if="isFailed" class="error-icon" />
                <icon-clock-circle v-else-if="isTimeout" class="warning-icon" />
                <icon-exclamation-circle v-else class="info-icon" />
              </div>
            </template>

            <!-- 支付详情 -->
            <template #extra>
              <div class="payment-details">
                <!-- 订单信息 -->
                <div v-if="orderInfo" class="order-section">
                  <h3 class="section-title">订单信息</h3>
                  <a-descriptions :column="1" size="medium" bordered>
                    <a-descriptions-item label="订单号">
                      {{ orderInfo.orderId }}
                    </a-descriptions-item>
                    <a-descriptions-item label="商品名称">
                      {{ orderInfo.productName }}
                    </a-descriptions-item>
                    <a-descriptions-item label="支付金额">
                      <span class="amount-text">¥{{ formatAmount(orderInfo.finalAmount) }}</span>
                    </a-descriptions-item>
                    <a-descriptions-item v-if="paymentInfo" label="支付时间">
                      {{ formatTime(paymentInfo.paymentTime) }}
                    </a-descriptions-item>
                    <a-descriptions-item v-if="paymentInfo" label="支付方式">
                      {{ getPaymentMethodText() }}
                    </a-descriptions-item>
                    <a-descriptions-item v-if="paymentInfo?.transactionId" label="交易号">
                      {{ paymentInfo.transactionId }}
                    </a-descriptions-item>
                  </a-descriptions>
                </div>

                <!-- 支付失败原因 -->
                <div v-if="isFailed && failureReason" class="failure-section">
                  <h3 class="section-title">失败原因</h3>
                  <a-alert type="error" :show-icon="false">
                    <template #icon>
                      <icon-exclamation-circle />
                    </template>
                    {{ failureReason }}
                  </a-alert>
                </div>

                <!-- 操作按钮 -->
                <div class="action-buttons">
                  <a-space size="large">
                    <!-- 成功状态按钮 -->
                    <template v-if="isSuccess">
                      <a-button type="primary" @click="handleViewOrder"> 查看订单 </a-button>
                      <a-button @click="handleContinueShopping"> 继续购买 </a-button>
                    </template>

                    <!-- 失败状态按钮 -->
                    <template v-else-if="isFailed">
                      <a-button type="primary" @click="handleRetryPayment"> 重新支付 </a-button>
                      <a-button @click="handleContactSupport"> 联系客服 </a-button>
                    </template>

                    <!-- 超时状态按钮 -->
                    <template v-else-if="isTimeout">
                      <a-button type="primary" @click="handleCheckStatus"> 查询状态 </a-button>
                      <a-button @click="handleRetryPayment"> 重新支付 </a-button>
                    </template>

                    <!-- 其他状态按钮 -->
                    <template v-else>
                      <a-button type="primary" @click="handleCheckStatus"> 查询状态 </a-button>
                      <a-button @click="handleGoHome"> 返回首页 </a-button>
                    </template>
                  </a-space>
                </div>

                <!-- 温馨提示 -->
                <div class="tips-section">
                  <a-alert type="info" :show-icon="false">
                    <template #icon>
                      <icon-info-circle />
                    </template>
                    <div class="tips-content">
                      <p v-if="isSuccess">支付成功！您可以在"我的订单"中查看订单详情和使用状态。</p>
                      <p v-else-if="isFailed">
                        支付失败，请检查支付信息后重试，或联系客服获取帮助。
                      </p>
                      <p v-else-if="isTimeout">支付超时，请查询最新支付状态或重新发起支付。</p>
                      <p v-else>如有疑问，请联系客服：************</p>
                    </div>
                  </a-alert>
                </div>
              </div>
            </template>
          </a-result>
        </div>
      </div>
    </div>

    <!-- 状态查询弹窗 -->
    <a-modal v-model:visible="showStatusModal" title="支付状态查询" :footer="false" :width="500">
      <div class="status-modal-content">
        <div v-if="statusLoading" class="status-loading">
          <a-spin :size="24" />
          <p>正在查询支付状态...</p>
        </div>

        <div v-else class="status-result">
          <a-descriptions :column="1" size="small" bordered>
            <a-descriptions-item label="支付状态">
              <a-tag :color="getStatusColor(currentStatus)">
                {{ getStatusText(currentStatus) }}
              </a-tag>
            </a-descriptions-item>
            <a-descriptions-item v-if="paymentInfo?.paidAmount" label="已支付金额">
              ¥{{ formatAmount(paymentInfo.paidAmount) }}
            </a-descriptions-item>
            <a-descriptions-item v-if="paymentInfo?.paymentTime" label="支付时间">
              {{ formatTime(paymentInfo.paymentTime) }}
            </a-descriptions-item>
          </a-descriptions>

          <div class="status-actions">
            <a-space>
              <a-button @click="showStatusModal = false">关闭</a-button>
              <a-button type="primary" @click="handleRefreshStatus">刷新状态</a-button>
            </a-space>
          </div>
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
  import { ref, computed, onMounted } from 'vue';
  import { useRouter, useRoute } from 'vue-router';
  import { Message } from '@arco-design/web-vue';
  import {
    IconCheckCircle,
    IconCloseCircle,
    IconClockCircle,
    IconExclamationCircle,
    IconInfoCircle,
  } from '@arco-design/web-vue/es/icon';
  import { usePaymentStore } from '@/store/modules/payment';
  import * as paymentApi from '@/http/api/payment';
  import type { OrderInfo, PaymentStatusResponse } from '@/http/api/payment/types';

  // Router
  const router = useRouter();
  const route = useRoute();

  // Store
  const paymentStore = usePaymentStore();

  // 本地状态
  const orderInfo = ref<OrderInfo | null>(null);
  const paymentInfo = ref<any>(null);
  const loading = ref(false);
  const showStatusModal = ref(false);
  const statusLoading = ref(false);
  const currentStatus = ref<string>('');
  const failureReason = ref<string>('');

  // 计算属性
  const orderId = computed(() => route.query.orderId as string);
  const paymentId = computed(() => route.query.paymentId as string);
  const status = computed(() => route.query.status as string);

  const isSuccess = computed(() => status.value === 'success');
  const isFailed = computed(() => status.value === 'failed');
  const isTimeout = computed(() => status.value === 'timeout');

  const resultStatus = computed(() => {
    if (isSuccess.value) return 'success';
    if (isFailed.value) return 'error';
    if (isTimeout.value) return 'warning';
    return 'info';
  });

  const resultTitle = computed(() => {
    if (isSuccess.value) return '支付成功';
    if (isFailed.value) return '支付失败';
    if (isTimeout.value) return '支付超时';
    return '支付处理中';
  });

  const resultSubtitle = computed(() => {
    if (isSuccess.value) return '恭喜您，订单支付成功！';
    if (isFailed.value) return '很抱歉，支付过程中出现了问题';
    if (isTimeout.value) return '支付超时，请查询最新状态';
    return '正在处理您的支付请求';
  });

  // 方法
  async function initializeResult() {
    if (!orderId.value) {
      Message.error('订单ID不能为空');
      router.push('/');
      return;
    }

    try {
      loading.value = true;

      // 获取订单信息
      const orderResponse = await paymentApi.getOrderInfo({ orderId: orderId.value });
      if (orderResponse.success && orderResponse.data) {
        orderInfo.value = orderResponse.data;
      }

      // 如果有支付ID，获取支付状态
      if (paymentId.value) {
        await fetchPaymentStatus();
      }
    } catch (error: any) {
      console.error('初始化支付结果失败:', error);
      Message.error('获取支付信息失败');
    } finally {
      loading.value = false;
    }
  }

  async function fetchPaymentStatus() {
    if (!paymentId.value) return;

    try {
      const response = await paymentApi.queryPaymentStatus({
        paymentId: paymentId.value,
        orderId: orderId.value,
      });

      if (response.success && response.data) {
        paymentInfo.value = response.data;
        currentStatus.value = response.data.status;

        if (response.data.failureReason) {
          failureReason.value = response.data.failureReason;
        }
      }
    } catch (error: any) {
      console.error('获取支付状态失败:', error);
    }
  }

  function getPaymentMethodText(): string {
    // 根据支付信息返回支付方式文本
    return '在线支付'; // 简化处理
  }

  function getStatusColor(status: string): string {
    const colorMap: Record<string, string> = {
      success: 'green',
      failed: 'red',
      processing: 'blue',
      cancelled: 'gray',
    };
    return colorMap[status] || 'gray';
  }

  function getStatusText(status: string): string {
    const textMap: Record<string, string> = {
      success: '支付成功',
      failed: '支付失败',
      processing: '处理中',
      cancelled: '已取消',
    };
    return textMap[status] || '未知状态';
  }

  function formatAmount(amount: number): string {
    return amount.toFixed(2);
  }

  function formatTime(timeString?: string): string {
    if (!timeString) return '-';
    const date = new Date(timeString);
    return date.toLocaleString('zh-CN');
  }

  // 事件处理
  function handleViewOrder() {
    router.push('/user/tradeList');
  }

  function handleContinueShopping() {
    router.push('/');
  }

  function handleRetryPayment() {
    router.push({
      name: 'Cashier',
      query: {
        orderId: orderId.value,
        type: orderInfo.value?.orderType,
        amount: orderInfo.value?.finalAmount,
      },
    });
  }

  function handleContactSupport() {
    // 打开客服聊天或跳转到客服页面
    Message.info('客服功能开发中，请拨打客服电话：************');
  }

  async function handleCheckStatus() {
    showStatusModal.value = true;
    await handleRefreshStatus();
  }

  async function handleRefreshStatus() {
    statusLoading.value = true;
    try {
      await fetchPaymentStatus();
    } catch (error: any) {
      Message.error('查询状态失败');
    } finally {
      statusLoading.value = false;
    }
  }

  function handleGoHome() {
    router.push('/');
  }

  // 生命周期
  onMounted(() => {
    initializeResult();
  });
</script>

<style scoped lang="scss">
  .payment-result-container {
    min-height: 100vh;
    background-color: #f7f8fa;
  }

  .result-header {
    background-color: #ffffff;
    border-bottom: 1px solid #e5e6eb;
    padding: 20px 0;

    .header-content {
      max-width: 1200px;
      margin: 0 auto;
      padding: 0 20px;
      text-align: center;

      .page-title {
        font-size: 24px;
        font-weight: 600;
        color: $text-color;
        margin: 0;
      }
    }
  }

  .result-main {
    padding: 40px 0;

    .main-content {
      max-width: 800px;
      margin: 0 auto;
      padding: 0 20px;
    }
  }

  .result-card {
    background-color: #ffffff;
    border-radius: 12px;
    padding: 40px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  }

  .result-icon {
    .success-icon {
      font-size: 64px;
      color: $success-color;
    }

    .error-icon {
      font-size: 64px;
      color: $error-color;
    }

    .warning-icon {
      font-size: 64px;
      color: $warning-color;
    }

    .info-icon {
      font-size: 64px;
      color: $info-color;
    }
  }

  .payment-details {
    display: flex;
    flex-direction: column;
    gap: 24px;
    margin-top: 24px;
  }

  .section-title {
    font-size: 16px;
    font-weight: 600;
    color: $text-color;
    margin: 0 0 16px 0;
  }

  .order-section {
    .amount-text {
      font-size: 16px;
      font-weight: 600;
      color: $primary-color;
    }
  }

  .action-buttons {
    display: flex;
    justify-content: center;
    margin: 24px 0;
  }

  .tips-section {
    .tips-content p {
      margin: 0;
      font-size: 14px;
      color: $text-color-secondary;
      line-height: 1.5;
    }
  }

  .status-modal-content {
    .status-loading {
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 40px 20px;

      p {
        margin-top: 16px;
        color: $text-color-secondary;
      }
    }

    .status-result {
      .status-actions {
        display: flex;
        justify-content: center;
        margin-top: 20px;
      }
    }
  }

  // 响应式设计
  @media (max-width: 768px) {
    .result-header {
      padding: 16px 0;

      .header-content {
        padding: 0 16px;

        .page-title {
          font-size: 20px;
        }
      }
    }

    .result-main {
      padding: 24px 0;

      .main-content {
        padding: 0 16px;
      }
    }

    .result-card {
      padding: 24px;
      border-radius: 8px;
    }

    .payment-details {
      gap: 20px;
      margin-top: 20px;
    }

    .action-buttons {
      :deep(.arco-space) {
        flex-direction: column;
        width: 100%;

        .arco-space-item {
          width: 100%;

          .arco-btn {
            width: 100%;
          }
        }
      }
    }
  }
</style>
