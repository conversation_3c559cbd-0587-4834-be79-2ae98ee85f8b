# 文创链服务平台 2.0 - 用户系统前端

[![Vue](https://img.shields.io/badge/Vue-3.2.45-4FC08D?style=flat-square&logo=vue.js)](https://vuejs.org/)
[![TypeScript](https://img.shields.io/badge/TypeScript-4.9.3-3178C6?style=flat-square&logo=typescript)](https://www.typescriptlang.org/)
[![Vite](https://img.shields.io/badge/Vite-4.0.0-646CFF?style=flat-square&logo=vite)](https://vitejs.dev/)
[![Arco Design](https://img.shields.io/badge/Arco%20Design-2.42.0-165DFF?style=flat-square)](https://arco.design/)
[![License](https://img.shields.io/badge/License-MIT-green?style=flat-square)](LICENSE)

文创链服务平台是一个基于区块链技术的数字版权保护和知识产权服务平台。本项目为平台的用户前端应用，采用现代化的 Vue 3 + TypeScript + Vite 技术栈构建，提供数据存证、软件著作权登记、作品著作权登记、API开放平台等核心功能。

## 🚀 项目简介

文创链服务平台致力于为用户提供专业的数字版权保护服务，通过区块链技术确保数据的不可篡改性和可追溯性。本用户前端采用双API架构（SOP + PRO），支持多种业务场景和用户需求。

### 🎯 核心服务

- **📋 数据存证服务**：为各类数字资产提供区块链存证，确保数据的完整性和时间戳证明
- **💻 软件著作权服务**：提供计算机软件著作权登记和存证服务，保护软件知识产权
- **🎨 作品著作权服务**：提供文学、艺术和科学作品的著作权登记服务
- **🔌 API开放平台**：为开发者提供丰富的API接口和技术文档，支持第三方集成
- **👤 用户中心**：完整的用户管理、套餐购买、交易记录、资产管理等功能
- **💳 支付系统**：集成多种支付方式，支持余额、积分、优惠券等多元化支付

## 🌟 核心功能

### 🏠 首页门户

- **产品展示**：轮播横幅、产品能力展示、商家案例展示
- **导航系统**：清晰的产品分类和服务导航
- **响应式设计**：支持PC、平板、手机等多端访问
- **SEO优化**：完整的页面标题、描述和关键词配置

### 🔐 用户认证系统

- **多种登录方式**：账号密码登录、手机号+验证码登录
- **安全认证**：JWT Token认证、RSA加密传输
- **权限控制**：基于路由的权限控制和页面访问保护
- **自动重定向**：登录后自动跳转到目标页面
- **第三方认证**：支持UAC、合利宝等第三方认证服务

### 📋 数据存证服务

- **存证申请**：支持多种数据类型的区块链存证申请
- **文件管理**：支持多格式文件上传，单文件最大100MB
- **智能表单**：完整的表单验证、自动保存和数据恢复
- **进度跟踪**：实时查看存证申请的审核状态和处理进度
- **存证查询**：支持存证记录的查询、筛选和管理
- **证书下载**：提供存证证书和相关文档下载

### 💻 软件著作权服务

- **登记申请**：计算机软件著作权登记申请和材料提交
- **软件存证**：软件源代码和相关文档的区块链存证
- **进度查询**：登记申请进度的实时查询和状态更新
- **政策指导**：提供软件著作权相关政策和常见问题解答
- **文档管理**：支持登记材料的在线管理和下载

### 🎨 作品著作权服务

- **作品登记**：文学、艺术和科学作品的著作权登记
- **作品存证**：原创作品的区块链存证和版权保护
- **类型支持**：支持文字、图片、音频、视频等多种作品类型
- **权利声明**：完整的作品权利声明和授权管理

### 🔌 API开放平台

- **接口文档**：详细的API接口文档和调用示例
- **在线测试**：集成的API测试工具，支持在线调试
- **开发者管理**：开发者认证、应用管理和API密钥管理
- **调用统计**：API调用次数、成功率等统计分析
- **SDK支持**：提供多语言SDK和代码示例

### 👤 用户中心

- **个人信息**：用户资料管理、实名认证、企业认证
- **套餐管理**：产品套餐购买、续费和升级
- **交易记录**：完整的交易历史和账单查询
- **资产管理**：账户余额、积分、优惠券等资产管理
- **历史记录**：浏览记录、搜索历史和关注记录

### 💳 支付系统

- **多元支付**：支持余额、积分、优惠券和在线支付
- **支付方式**：集成多种第三方支付渠道
- **优惠策略**：支持优惠券、满减、积分抵扣等优惠方式
- **订单管理**：完整的订单生命周期管理
- **支付安全**：支付过程加密和安全验证

## 🛠️ 技术架构

### 前端技术栈

- 🚀 **[Vue 3](https://v3.vuejs.org/)** - 渐进式JavaScript框架，采用Composition API
- 🔥 **[Vite](https://vitejs.dev/)** - 下一代前端构建工具，快速热重载
- 🧰 **[TypeScript](https://www.typescriptlang.org/)** - 强类型JavaScript超集，提供类型安全
- 📦 **[PNPM](https://pnpm.io/)** - 快速、节省磁盘空间的包管理器
- 🎨 **[Arco Design](https://arco.design/)** - 企业级设计系统和组件库
- 🌐 **[Vue Router](https://next.router.vuejs.org/)** - Vue.js 的官方路由管理
- 📊 **[Pinia](https://pinia.vuejs.org/)** - Vue 状态管理，支持持久化存储
- 🧩 **[Vue I18n](https://vue-i18n.intlify.dev/)** - 国际化解决方案
- 💄 **[Tailwind CSS](https://tailwindcss.com/)** - 实用优先的CSS框架
- 🎨 **[SCSS](https://sass-lang.com/)** - CSS预处理器，支持变量和混入

### 开发工具链

- 🛠️ **[ESLint](https://eslint.org/)** + **[Prettier](https://prettier.io/)** - 代码质量检查和格式化
- 🧪 **[Vitest](https://vitest.dev/)** - 单元测试框架，支持覆盖率报告
- 🎭 **[Playwright](https://playwright.dev/)** - 端到端测试，支持多浏览器
- 📚 **[JSDoc](https://jsdoc.app/)** - 文档生成工具，支持Vue组件文档
- 🔄 **[Husky](https://typicode.github.io/husky/)** - Git钩子管理
- 📝 **[CommitLint](https://commitlint.js.org/)** - 提交信息规范检查
- 🧹 **[Lint-staged](https://github.com/okonet/lint-staged)** - 暂存文件检查

### 构建和部署

- 🚢 **CI/CD** - GitHub Actions自动化工作流
- 📱 **PWA支持** - 离线访问和应用安装功能
- 📊 **性能分析** - Rollup Bundle Analyzer构建分析
- 🗜️ **代码压缩** - Terser压缩和Gzip压缩
- 🐳 **容器化部署** - Docker和Docker Compose支持
- 🌐 **Nginx配置** - 生产环境Web服务器配置

### API架构

- 🔌 **双API架构** - SOP (旧版API) + PRO (新版API) 并存
- 🔐 **认证系统** - JWT Token + RSA加密
- 📡 **HTTP客户端** - Axios封装，支持请求/响应拦截
- 🛡️ **错误处理** - 统一错误处理和用户提示
- 🔄 **自动重试** - 网络请求失败自动重试机制

## 设计系统

### 🎨 主色调体系

文创链服务平台采用完整的设计系统，确保整个应用的视觉一致性和品牌识别度。

#### 品牌主色 - 文创链蓝

```css
/* 主品牌色系 */
--primary-color: #165dff; /* 主品牌色 - 用于主要按钮、链接、重要信息 */
--primary-light: #4080ff; /* 浅色变体 - 用于悬停状态、次要强调 */
--primary-dark: #0f58d3; /* 深色变体 - 用于激活状态、深色主题 */
--primary-lightest: #e8f3ff; /* 最浅色 - 用于背景、标签 */
```

#### 功能色系

```css
/* 状态色 */
--success-color: #00b42a; /* 成功色 - 成功状态、确认操作 */
--warning-color: #ff7d00; /* 警告色 - 警告状态、需要注意的信息 */
--error-color: #f53f3f; /* 错误色 - 错误状态、危险操作 */
--info-color: #165dff; /* 信息色 - 信息提示（使用主品牌色） */
```

#### 中性色系

```css
/* 文本色 */
--text-primary: #1d2129; /* 主文本 - 标题、重要文本 */
--text-secondary: #4e5969; /* 次要文本 - 正文、描述文本 */
--text-tertiary: #86909c; /* 第三级文本 - 辅助信息、标签 */
--text-disabled: #c9cdd4; /* 禁用文本 */

/* 背景色 */
--bg-primary: #ffffff; /* 主背景 - 页面主背景 */
--bg-secondary: #f7f8fa; /* 次要背景 - 卡片、面板背景 */
--bg-tertiary: #f2f3f5; /* 第三级背景 - 分割区域、输入框背景 */

/* 边框色 */
--border-color: #e5e6eb; /* 主边框 - 默认边框 */
--border-light: #f2f3f5; /* 浅边框 - 分割线、浅色边框 */
--border-dark: #c9cdd4; /* 深边框 - 强调边框 */
```

### 🎯 使用示例

#### Tailwind CSS 类名

```html
<!-- 品牌色使用 -->
<button class="bg-primary hover:bg-primary-400 text-white">主要按钮</button>
<a class="text-primary hover:text-primary-400">品牌色链接</a>

<!-- 功能色使用 -->
<div class="bg-success text-white">成功提示</div>
<div class="bg-warning text-white">警告提示</div>
<div class="bg-error text-white">错误提示</div>

<!-- 文本色使用 -->
<h1 class="text-text-primary">主要标题</h1>
<p class="text-text-secondary">正文内容</p>
<span class="text-text-tertiary">辅助信息</span>

<!-- 背景色使用 -->
<div class="bg-bg-primary">主背景</div>
<div class="bg-bg-secondary">卡片背景</div>
```

#### SCSS 变量使用

```scss
// 在 SCSS 文件中使用预定义变量
.custom-button {
  background-color: $primary-color;
  color: $text-color-inverse;
  border: 1px solid $border-color;

  &:hover {
    background-color: $primary-light;
  }

  &:active {
    background-color: $primary-dark;
  }
}
```

### 📐 设计规范

- **对比度**: 确保文本与背景有足够的对比度（至少4.5:1）
- **一致性**: 相同功能使用相同颜色，保持整个应用的色彩使用一致性
- **层次结构**: 使用主品牌色突出最重要的操作和信息
- **可访问性**: 不仅依靠颜色传达信息，结合图标、文字等元素

### 🔧 自定义主题

项目支持主题自定义，可以通过修改以下文件来调整色彩系统：

- `src/styles/_variables.scss` - SCSS变量定义
- `tailwind.config.cjs` - Tailwind CSS颜色配置
- `src/styles/color-guide.md` - 完整的色彩使用指南

## 📋 环境要求

### 开发环境

- **Node.js**: 18.0.0 或更高版本 (推荐使用 LTS 版本)
- **包管理器**: PNPM 8.0+ (推荐) 或 NPM 9.0+
- **操作系统**: Windows 10+, macOS 10.15+, Linux (Ubuntu 18.04+)
- **IDE**: VS Code (推荐) 或其他支持 TypeScript 的编辑器

### 浏览器支持

- **现代浏览器**: Chrome 90+, Firefox 88+, Safari 14+, Edge 90+
- **移动端**: iOS Safari 14+, Android Chrome 90+
- **不支持**: Internet Explorer (任何版本)

### 推荐的VS Code扩展

- Vue Language Features (Volar)
- TypeScript Vue Plugin (Volar)
- ESLint
- Prettier - Code formatter
- Tailwind CSS IntelliSense
- Auto Rename Tag
- Bracket Pair Colorizer

## 🚀 快速开始

### 1. 克隆项目

```bash
# 使用 HTTPS
git clone https://github.com/your-org/wenchuang-chain-user-html-2.0.git
cd wenchuang-chain-user-html-2.0

# 或使用 SSH
<NAME_EMAIL>:your-org/wenchuang-chain-user-html-2.0.git
cd wenchuang-chain-user-html-2.0
```

### 2. 安装依赖

```bash
# 推荐使用 PNPM (更快、更节省空间)
pnpm install

# 或使用 NPM
npm install

# 或使用 Yarn
yarn install
```

### 3. 环境配置

创建环境变量文件：

```bash
# 开发环境配置
cp .env.example .env.development

# 生产环境配置
cp .env.example .env.production
```

主要环境变量说明：

```env
# API服务地址配置
VITE_SOP_API_TARGET=http://localhost:8080    # SOP API 服务地址
VITE_PRO_API_TARGET=http://localhost:8081    # PRO API 服务地址

# 应用配置
VITE_BASE_URL=./                             # 应用基础路径
VITE_DROP_CONSOLE=false                      # 生产环境是否移除 console
VITE_APP_TITLE=文创链服务平台                 # 应用标题

# 其他配置
NODE_ENV=development                         # 环境标识
```

### 4. 启动开发服务器

```bash
# 启动开发服务器
pnpm dev

# 或指定端口启动
pnpm dev --port 3000
```

应用将在 `http://localhost:5173` 启动，支持热重载和自动刷新。

### 5. 构建生产版本

```bash
# 构建生产版本
pnpm build

# 预览构建结果
pnpm preview
```

## 📜 可用脚本

### 开发相关

```bash
# 启动开发服务器 (支持热重载)
pnpm dev

# 构建生产版本 (优化压缩)
pnpm build

# 构建开发版本 (保留调试信息)
pnpm build:dev

# 构建生产版本 (等同于 pnpm build)
pnpm build:prod

# 构建并打包发布版本 (包含zip压缩)
pnpm build:release

# 预览构建结果 (本地服务器)
pnpm preview
```

### 代码质量

```bash
# 代码检查和自动修复 (ESLint)
pnpm lint

# 代码格式化 (Prettier)
pnpm format

# Git提交前的代码检查 (Husky)
pnpm prepare
```

### 测试

```bash
# 运行单元测试 (监听模式，开发时使用)
pnpm test

# 运行单元测试 (单次执行，CI/CD使用)
pnpm test:unit

# 生成测试覆盖率报告 (HTML + JSON)
pnpm test:coverage

# 运行端到端测试 (Playwright)
pnpm test:e2e
```

### 工具和分析

```bash
# 构建性能分析 (生成可视化报告)
pnpm analyze

# 构建分析 (等同于 analyze)
pnpm build:analyze

# 清理缓存和构建文件
pnpm clean

# 生成 JSDoc 文档
pnpm docs
```

### 依赖管理

```bash
# 安装依赖
pnpm install

# 更新依赖到最新版本
pnpm update

# 检查过时的依赖
pnpm outdated

# 审计安全漏洞
pnpm audit
```

## 📁 项目结构

```text
wenchuang-chain-user-html-2.0/
├── .github/                    # GitHub Actions工作流配置
│   └── workflows/              # CI/CD工作流文件
├── .husky/                     # Git钩子配置
│   ├── pre-commit              # 提交前代码检查
│   └── commit-msg              # 提交信息检查
├── .vscode/                    # VS Code调试配置
│   ├── launch.json             # 调试配置
│   └── settings.json           # 工作区设置
├── dist/                       # 构建输出目录
├── docs/                       # 项目文档和开发指南
│   ├── auth-implementation.md  # 认证实现文档
│   ├── upload-usage.md         # 文件上传指南
│   └── development-plan/       # 开发计划文档
├── e2e/                        # 端到端测试
│   └── example.spec.ts         # 测试用例示例
├── public/                     # 静态资源
│   ├── favicon.ico             # 网站图标
│   ├── robots.txt              # 搜索引擎爬虫配置
│   └── sitemap.xml             # 网站地图
├── scripts/                    # 构建和部署脚本
│   └── zip-dist.js             # 打包脚本
├── src/                        # 源代码目录
│   ├── assets/                 # 静态资源
│   │   ├── images/             # 图片资源
│   │   └── fonts/              # 字体文件
│   ├── components/             # 公共组件
│   │   ├── header.vue          # 页面头部组件
│   │   └── footer.vue          # 页面底部组件
│   ├── http/                   # HTTP请求配置
│   │   ├── api/                # API接口定义
│   │   │   ├── authentication/ # 认证相关API
│   │   │   ├── data/           # 数据存证API
│   │   │   ├── software/       # 软件著作权API
│   │   │   ├── works/          # 作品著作权API
│   │   │   ├── user/           # 用户相关API
│   │   │   ├── payment/        # 支付相关API
│   │   │   ├── evidence/       # 存证相关API
│   │   │   └── global/         # 全局API
│   │   ├── axios.ts            # Axios基础配置
│   │   ├── proHttp.ts          # PRO API请求实例
│   │   └── sopHttp.ts          # SOP API请求实例
│   ├── i18n/                   # 国际化配置
│   │   ├── index.ts            # i18n配置入口
│   │   └── locales/            # 语言包文件
│   ├── router/                 # 路由配置
│   │   └── index.ts            # 路由定义和守卫
│   ├── store/                  # Pinia状态管理
│   │   ├── index.ts            # Store入口
│   │   └── modules/            # 状态模块
│   │       ├── user/           # 用户状态
│   │       ├── payment.ts      # 支付状态
│   │       └── searchHistory/  # 搜索历史
│   ├── styles/                 # 全局样式文件
│   │   ├── _variables.scss     # SCSS变量定义
│   │   ├── index.scss          # 样式入口文件
│   │   ├── color-guide.md      # 色彩使用指南
│   │   └── responsive.css      # 响应式样式
│   ├── utils/                  # 工具函数
│   │   ├── auth.ts             # 认证工具
│   │   ├── upload.ts           # 文件上传工具
│   │   ├── tool.ts             # 通用工具函数
│   │   ├── errorHandler.ts     # 错误处理工具
│   │   └── __tests__/          # 工具函数测试
│   ├── view/                   # 页面组件
│   │   ├── home.vue            # 首页
│   │   ├── login/              # 登录相关页面
│   │   │   ├── index.vue       # 登录页面
│   │   │   ├── register.vue    # 注册页面
│   │   │   └── forget.vue      # 忘记密码页面
│   │   ├── user/               # 用户中心
│   │   ├── data/               # 数据存证页面
│   │   ├── software/           # 软件著作权页面
│   │   ├── works/              # 作品著作权页面
│   │   ├── api/                # API文档页面
│   │   ├── apiFiles/           # API文件管理页面
│   │   ├── products/           # 产品页面
│   │   ├── payment/            # 支付页面
│   │   ├── evidence/           # 存证页面
│   │   ├── authentication/     # 认证页面
│   │   └── test/               # 测试页面
│   ├── App.vue                 # 根组件
│   ├── main.ts                 # 应用入口
│   ├── vite-env.d.ts           # Vite类型声明
│   └── responsive-adapter.js   # 响应式适配器
├── .editorconfig               # 编辑器配置
├── .env.example                # 环境变量示例
├── .eslintrc.js                # ESLint配置
├── .gitignore                  # Git忽略文件
├── .prettierrc.js              # Prettier配置
├── commitlint.config.cjs       # CommitLint配置
├── Dockerfile                  # Docker镜像配置
├── docker-compose.yml          # Docker Compose配置
├── index.html                  # HTML模板
├── jsdoc.config.json           # JSDoc配置
├── nginx.conf                  # Nginx配置
├── package.json                # 项目依赖和脚本
├── playwright.config.ts        # Playwright测试配置
├── pnpm-lock.yaml              # PNPM锁定文件
├── postcss.config.cjs          # PostCSS配置
├── tailwind.config.cjs         # Tailwind CSS配置
├── tsconfig.json               # TypeScript配置
├── tsconfig.node.json          # Node.js TypeScript配置
├── vite.config.ts              # Vite构建配置
└── vitest.config.ts            # Vitest测试配置
```

## 🔧 开发工具配置

### VS Code调试配置

项目包含了完整的VS Code调试配置，支持多种调试场景：

1. **Debug Vue App (Edge)** - 在Microsoft Edge中调试Vue应用
2. **Debug Vue App (Chrome)** - 在Google Chrome中调试Vue应用
3. **Debug Vitest Tests** - 调试Vitest单元测试

使用方法：

1. 在VS Code中打开项目
2. 按 `F5` 或点击调试面板的运行按钮
3. 选择对应的调试配置
4. 在代码中设置断点进行调试

### ESLint和Prettier配置

项目使用ESLint进行代码质量检查，Prettier进行代码格式化：

```bash
# 检查代码质量
pnpm lint

# 自动修复可修复的问题
pnpm lint --fix

# 格式化代码
pnpm format
```

### Git钩子配置

使用Husky管理Git钩子，确保代码质量：

- **pre-commit**: 提交前自动运行代码检查和格式化
- **commit-msg**: 检查提交信息是否符合规范

## 🚀 自动化工作流

项目配置了完整的GitHub Actions工作流：

### 1. CI/CD工作流

- **代码检查**: ESLint代码质量检查
- **类型检查**: TypeScript类型检查
- **单元测试**: Vitest单元测试执行
- **构建测试**: 多环境构建测试
- **E2E测试**: Playwright端到端测试

### 2. 依赖管理工作流

- **依赖更新**: 自动检查和更新依赖
- **安全审计**: 定期检查依赖安全漏洞
- **版本兼容性**: 检查依赖版本兼容性

### 3. 自动发布工作流

- **语义化版本**: 基于Conventional Commits自动生成版本号
- **变更日志**: 自动生成CHANGELOG.md
- **标签创建**: 自动创建Git标签
- **发布部署**: 自动构建和部署到生产环境

### 4. 质量保证工作流

- **代码覆盖率**: 生成和上传测试覆盖率报告
- **性能监控**: 构建性能分析和监控
- **安全扫描**: 代码安全漏洞扫描

## 💡 核心功能详解

### 🔐 用户认证与权限系统

项目实现了企业级的用户认证系统：

#### 认证方式

- **账号密码登录**: 支持用户名/手机号+密码登录
- **短信验证码登录**: 支持手机号+验证码快速登录
- **第三方认证**: 集成UAC、合利宝等第三方认证服务
- **RSA加密**: 敏感信息传输采用RSA加密保护

#### 权限控制

- **路由守卫**: 基于`requiresAuth`配置的页面访问控制
- **Token管理**: JWT Token自动管理、刷新和过期处理
- **自动重定向**: 登录后自动跳转到目标页面
- **登录状态检查**: 实时检查用户登录状态，自动处理401错误

#### 安全特性

- **密码加密**: 前端密码MD5加密传输
- **Token存储**: 安全的Token存储和管理
- **会话管理**: 完整的用户会话生命周期管理

### 📋 数据存证功能

提供专业的区块链数据存证服务：

#### 存证申请

- **多类型支持**: 支持文档、图片、音频、视频等多种数据类型
- **智能表单**: 动态表单验证、自动保存和数据恢复
- **批量上传**: 支持多文件批量上传，单文件最大100MB
- **进度显示**: 实时显示上传进度和处理状态

#### 存证管理

- **状态跟踪**: 实时查看存证申请的审核状态和处理进度
- **存证查询**: 支持多条件查询和筛选存证记录
- **证书下载**: 提供存证证书和相关文档下载
- **历史记录**: 完整的存证操作历史记录

#### 区块链集成

- **哈希计算**: 自动计算文件哈希值确保数据完整性
- **区块链存储**: 将存证信息写入区块链确保不可篡改
- **时间戳证明**: 提供精确的时间戳证明

### 🔌 API集成架构

采用双API架构设计，支持新旧系统平滑过渡：

#### 双API系统

- **SOP API**: 旧版API系统，兼容现有业务
- **PRO API**: 新版API系统，提供更强大的功能
- **智能路由**: 根据业务需求自动选择合适的API

#### 请求处理

- **统一拦截**: 自动添加认证信息和公共参数
- **错误处理**: 统一的错误处理和用户友好提示
- **重试机制**: 网络请求失败自动重试
- **缓存策略**: 智能的请求缓存和数据缓存

#### 接口文档

- **在线文档**: 集成Swagger风格的API文档
- **在线测试**: 支持在线调试和测试API接口
- **代码生成**: 自动生成多语言调用代码示例

## 📖 开发指南

### 环境变量配置

#### 环境文件结构

```bash
# 开发环境配置
.env.development

# 生产环境配置
.env.production

# 本地开发配置 (可选，不提交到版本控制)
.env.local
```

#### 完整配置示例

```env
# ===========================================
# API服务配置
# ===========================================
VITE_SOP_API_TARGET=http://localhost:8080    # SOP API服务地址
VITE_PRO_API_TARGET=http://localhost:8081    # PRO API服务地址

# ===========================================
# 应用基础配置
# ===========================================
VITE_BASE_URL=./                             # 应用基础路径
VITE_APP_TITLE=文创链服务平台                 # 应用标题
VITE_APP_DESCRIPTION=数字版权保护和知识产权服务平台

# ===========================================
# 构建配置
# ===========================================
VITE_DROP_CONSOLE=false                      # 是否移除console (生产环境建议true)
VITE_DROP_DEBUGGER=true                      # 是否移除debugger
VITE_SOURCEMAP=false                         # 是否生成sourcemap

# ===========================================
# 功能开关
# ===========================================
VITE_ENABLE_PWA=true                         # 是否启用PWA
VITE_ENABLE_MOCK=false                       # 是否启用Mock数据
VITE_ENABLE_DEVTOOLS=true                    # 是否启用开发工具

# ===========================================
# 第三方服务配置
# ===========================================
VITE_SENTRY_DSN=                             # Sentry错误监控DSN
VITE_GA_ID=                                  # Google Analytics ID
```

### 代码规范和质量

#### 代码规范工具链

- **ESLint**: 代码质量检查和错误检测
- **Prettier**: 代码格式化和风格统一
- **Husky**: Git钩子管理和自动化检查
- **CommitLint**: 提交信息规范检查
- **Lint-staged**: 暂存文件检查

#### 提交前检查清单

```bash
# 1. 代码质量检查
pnpm lint

# 2. 自动修复可修复的问题
pnpm lint --fix

# 3. 代码格式化
pnpm format

# 4. 类型检查
pnpm type-check

# 5. 运行测试
pnpm test

# 6. 构建测试
pnpm build
```

#### 提交信息规范

项目使用[Conventional Commits](https://conventionalcommits.org/)规范：

```bash
# 功能开发
git commit -m "feat: 添加用户登录功能"

# 问题修复
git commit -m "fix: 修复文件上传失败的问题"

# 文档更新
git commit -m "docs: 更新API文档"

# 样式调整
git commit -m "style: 调整按钮样式"

# 代码重构
git commit -m "refactor: 重构用户认证模块"

# 性能优化
git commit -m "perf: 优化图片加载性能"

# 测试相关
git commit -m "test: 添加用户登录测试用例"

# 构建相关
git commit -m "build: 更新构建配置"

# CI/CD相关
git commit -m "ci: 更新GitHub Actions配置"
```

## 🐳 Docker部署

### 快速部署

#### 使用预构建镜像

```bash
# 拉取镜像
docker pull wenchuang-chain-frontend:latest

# 运行容器
docker run -d \
  --name wenchuang-frontend \
  -p 80:80 \
  wenchuang-chain-frontend:latest
```

#### 本地构建部署

```bash
# 构建Docker镜像
docker build -t wenchuang-chain-frontend:local .

# 运行容器
docker run -d \
  --name wenchuang-frontend \
  -p 80:80 \
  -v $(pwd)/nginx.conf:/etc/nginx/conf.d/default.conf \
  wenchuang-chain-frontend:local
```

### Docker Compose部署

#### 基础部署

```bash
# 启动所有服务
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f

# 停止服务
docker-compose down
```

#### 生产环境配置

```yaml
# docker-compose.prod.yml
version: '3.8'
services:
  frontend:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - '80:80'
      - '443:443'
    volumes:
      - ./nginx.conf:/etc/nginx/conf.d/default.conf
      - ./ssl:/etc/nginx/ssl
    environment:
      - NODE_ENV=production
    restart: unless-stopped
    networks:
      - wenchuang-network

networks:
  wenchuang-network:
    driver: bridge
```

### 生产环境部署

#### 1. 构建优化

```bash
# 构建生产版本
pnpm build:prod

# 构建并分析包大小
pnpm build:analyze

# 构建并打包
pnpm build:release
```

#### 2. Nginx部署

```bash
# 复制构建文件
sudo cp -r dist/* /var/www/html/

# 复制Nginx配置
sudo cp nginx.conf /etc/nginx/sites-available/wenchuang-chain

# 启用站点
sudo ln -s /etc/nginx/sites-available/wenchuang-chain /etc/nginx/sites-enabled/

# 测试配置
sudo nginx -t

# 重载Nginx
sudo systemctl reload nginx
```

#### 3. SSL配置

```nginx
server {
    listen 443 ssl http2;
    server_name your-domain.com;

    ssl_certificate /path/to/your/certificate.crt;
    ssl_certificate_key /path/to/your/private.key;

    # SSL配置
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;

    # 其他配置...
}
```

#### 4. 性能优化

```nginx
# 启用Gzip压缩
gzip on;
gzip_vary on;
gzip_min_length 1024;
gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;

# 启用缓存
location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
    expires 1y;
    add_header Cache-Control "public, immutable";
}

# 启用HTTP/2推送
location / {
    http2_push /static/css/main.css;
    http2_push /static/js/main.js;
}
```

## ❓ 常见问题

### 开发环境问题

#### Q: 启动时出现端口占用错误

**A**: 解决方案：

```bash
# 方法1: 指定其他端口启动
pnpm dev --port 3000

# 方法2: 修改vite.config.ts中的端口配置
server: {
  port: 3000
}

# 方法3: 查找并终止占用端口的进程
# Windows
netstat -ano | findstr :5173
taskkill /PID <PID> /F

# macOS/Linux
lsof -ti:5173 | xargs kill -9
```

#### Q: API请求跨域问题

**A**: 检查和配置代理：

```typescript
// vite.config.ts
server: {
  proxy: {
    '/api': {
      target: 'http://localhost:8080',
      changeOrigin: true,
      rewrite: path => path.replace(/^\/api/, '')
    }
  }
}
```

#### Q: 文件上传失败

**A**: 排查步骤：

1. 检查文件大小是否超过100MB限制
2. 检查文件格式是否在允许范围内
3. 查看浏览器控制台错误信息
4. 检查网络连接和API服务状态

#### Q: TypeScript类型错误

**A**: 解决方案：

```bash
# 重新生成类型声明
pnpm type-check

# 清理缓存重新安装
pnpm clean && pnpm install

# 检查tsconfig.json配置
```

### 构建部署问题

#### Q: 构建后页面空白

**A**: 检查配置：

```env
# 确保基础路径配置正确
VITE_BASE_URL=./

# 如果部署在子目录，设置正确的路径
VITE_BASE_URL=/your-subdirectory/
```

#### Q: 静态资源加载失败

**A**: Nginx配置检查：

```nginx
# 确保静态资源路径正确
location /static/ {
    alias /var/www/html/static/;
    expires 1y;
}

# 处理SPA路由
location / {
    try_files $uri $uri/ /index.html;
}
```

#### Q: Docker容器启动失败

**A**: 排查步骤：

```bash
# 查看容器日志
docker logs wenchuang-frontend

# 检查端口占用
docker ps -a

# 重新构建镜像
docker build --no-cache -t wenchuang-chain-frontend .
```

### 性能问题

#### Q: 首屏加载慢

**A**: 优化建议：

1. 启用代码分割和懒加载
2. 优化图片资源（使用WebP格式）
3. 启用Gzip压缩
4. 使用CDN加速静态资源
5. 检查网络请求是否过多

#### Q: 内存占用过高

**A**: 排查方案：

1. 检查是否有内存泄漏
2. 优化大型组件的渲染
3. 使用虚拟滚动处理大列表
4. 及时清理事件监听器和定时器

## 📚 技术文档

项目包含详细的技术文档，位于 `docs/` 目录：

### 核心功能文档

- [认证实现文档](docs/auth-implementation.md) - 用户认证和权限控制实现
- [存证功能实现](docs/evidence-submit-implementation.md) - 数据存证功能详细说明
- [文件上传指南](docs/upload-usage.md) - 文件上传功能使用指南
- [支付系统文档](docs/payment-cashier-usage.md) - 支付收银台使用说明

### 开发指南文档

- [API更新适配](docs/backend-api-update-adaptation.md) - 后端API更新适配指南
- [错误处理指南](docs/401-error-handling.md) - 401错误处理实现
- [Git钩子配置](docs/husky-configuration.md) - Husky配置和使用指南
- [开发计划文档](docs/development-plan/) - 项目开发计划和需求文档

### API参考文档

- [支付API参考](docs/payment-api-reference.md) - 支付相关API接口文档
- [API测试清单](docs/api-update-test-checklist.md) - API更新测试检查清单

## 🤝 贡献指南

我们欢迎所有形式的贡献，包括但不限于：

- 🐛 报告Bug和问题
- 💡 提出新功能建议
- 📝 改进文档
- 🔧 提交代码修复
- ⚡ 性能优化
- 🧪 添加测试用例

### 贡献流程

1. **Fork项目** - 点击右上角的Fork按钮
2. **克隆仓库** - `git clone https://github.com/your-username/wenchuang-chain-user-html-2.0.git`
3. **创建分支** - `git checkout -b feature/your-feature-name`
4. **开发功能** - 编写代码并确保通过所有测试
5. **提交代码** - `git commit -m 'feat: add your feature'`
6. **推送分支** - `git push origin feature/your-feature-name`
7. **创建PR** - 在GitHub上创建Pull Request

### 提交规范

请遵循 [Conventional Commits](https://conventionalcommits.org/) 规范：

```bash
# 功能类型
feat: 新功能开发
fix: Bug修复
docs: 文档更新
style: 代码格式调整（不影响功能）
refactor: 代码重构（不新增功能，不修复bug）
perf: 性能优化
test: 测试相关
build: 构建系统或外部依赖变更
ci: CI配置文件和脚本变更
chore: 其他不修改src或test文件的变更
revert: 回滚之前的提交

# 示例
feat(auth): 添加第三方登录支持
fix(upload): 修复大文件上传失败问题
docs(readme): 更新安装说明
```

### 代码审查标准

- ✅ 代码符合项目规范（ESLint + Prettier）
- ✅ 包含必要的测试用例
- ✅ 文档更新（如有必要）
- ✅ 提交信息清晰明确
- ✅ 不破坏现有功能
- ✅ 性能影响可接受

## 📄 许可证

此项目基于 [MIT 许可证](LICENSE) 开源。

```
MIT License

Copyright (c) 2024 文创链服务平台 - 用户前端

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all
copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
SOFTWARE.
```

---

## 🙏 致谢

感谢所有为这个项目做出贡献的开发者和用户！

### 技术栈致谢

- [Vue.js](https://vuejs.org/) - 渐进式JavaScript框架
- [Vite](https://vitejs.dev/) - 下一代前端构建工具
- [Arco Design](https://arco.design/) - 企业级设计系统
- [TypeScript](https://www.typescriptlang.org/) - JavaScript的超集

### 特别感谢

- 文创链团队的产品设计和需求支持
- 后端开发团队的API接口支持
- 测试团队的质量保证
- 运维团队的部署和维护支持

---

<div align="center">

**如果这个项目对您有帮助，请给我们一个 ⭐️**

[报告问题](https://github.com/your-org/wenchuang-chain-user-html-2.0/issues) · [功能请求](https://github.com/your-org/wenchuang-chain-user-html-2.0/issues) · [贡献代码](https://github.com/your-org/wenchuang-chain-user-html-2.0/pulls)

</div>
