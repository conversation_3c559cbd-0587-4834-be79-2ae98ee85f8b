/* 响应式布局辅助样式 */

/* 响应式容器 */
.container-responsive {
  width: 100%;
  margin: 0 auto;
  padding: 0 1rem;
  box-sizing: border-box;
}

/* 移动设备 - 小屏幕 */
@media (min-width: 320px) {
  .container-responsive {
    max-width: 100%;
  }
}

/* 移动设备 - 中等屏幕 */
@media (min-width: 480px) {
  .container-responsive {
    max-width: 480px;
  }
}

/* 平板设备 - 小屏幕 */
@media (min-width: 640px) {
  .container-responsive {
    max-width: 640px;
  }
}

/* 平板设备 - 中等屏幕 */
@media (min-width: 768px) {
  .container-responsive {
    max-width: 768px;
  }
}

/* 桌面设备 - 小屏幕 */
@media (min-width: 1024px) {
  .container-responsive {
    max-width: 1024px;
  }
}

/* 桌面设备 - 中等屏幕 */
@media (min-width: 1280px) {
  .container-responsive {
    max-width: 1200px;
    padding: 0;
  }
}

/* 桌面设备 - 大屏幕 */
@media (min-width: 1440px) {
  .container-responsive {
    max-width: 1400px;
    padding: 0;
  }
}

/* 响应式布局工具类 */
/* 显示/隐藏类 */
.hidden-xs {
  display: none;
}

.hidden-sm {
  display: none;
}

.hidden-md {
  display: none;
}

.hidden-lg {
  display: none;
}

.hidden-sm-up {
  display: none;
}

.hidden-md-up {
  display: none;
}

.hidden-lg-up {
  display: none;
}

/* 移动设备 - 小屏幕 */
@media (max-width: 479px) {
  .xs-only {
    display: block;
  }

  .hidden-xs-only {
    display: none !important;
  }
}

/* 移动设备 - 中等屏幕 */
@media (min-width: 480px) and (max-width: 639px) {
  .sm-only {
    display: block;
  }

  .hidden-sm-only {
    display: none !important;
  }
}

/* 平板设备 */
@media (min-width: 640px) and (max-width: 1023px) {
  .md-only {
    display: block;
  }

  .hidden-md-only {
    display: none !important;
  }

  .hidden-xs {
    display: block;
  }

  .xs-only {
    display: none;
  }
}

/* 桌面设备 */
@media (min-width: 1024px) {
  .lg-only {
    display: block;
  }

  .hidden-lg-only {
    display: none !important;
  }

  .hidden-xs {
    display: block;
  }

  .hidden-sm {
    display: block;
  }

  .hidden-md {
    display: block;
  }

  .xs-only,
  .sm-only,
  .md-only {
    display: none;
  }
}

/* 响应式栅格 */
.grid-responsive {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 1rem;
}

@media (min-width: 640px) {
  .grid-responsive {
    grid-template-columns: repeat(8, 1fr);
  }
}

@media (min-width: 1024px) {
  .grid-responsive {
    grid-template-columns: repeat(12, 1fr);
  }
}

/* 响应式 Flex 布局 */
.flex-responsive {
  display: flex;
  flex-direction: column;
}

@media (min-width: 768px) {
  .flex-responsive {
    flex-direction: row;
  }
}

/* 响应式间距 */
.spacing-responsive {
  margin-bottom: 1.5rem;
}

@media (min-width: 768px) {
  .spacing-responsive {
    margin-bottom: 2rem;
  }
}

@media (min-width: 1024px) {
  .spacing-responsive {
    margin-bottom: 3rem;
  }
}

/* 响应式文本对齐 */
.text-center-xs {
  text-align: center;
}

@media (min-width: 640px) {
  .text-left-sm {
    text-align: left;
  }

  .text-center-sm {
    text-align: center;
  }

  .text-right-sm {
    text-align: right;
  }
}

@media (min-width: 1024px) {
  .text-left-lg {
    text-align: left;
  }

  .text-center-lg {
    text-align: center;
  }

  .text-right-lg {
    text-align: right;
  }
}

/* 响应式内边距 */
.padding-responsive {
  padding: 1rem;
}

@media (min-width: 640px) {
  .padding-responsive {
    padding: 1.5rem;
  }
}

@media (min-width: 1024px) {
  .padding-responsive {
    padding: 2rem;
  }
}

/* 响应式字体大小 */
.text-responsive {
  font-size: 14px;
}

@media (min-width: 640px) {
  .text-responsive {
    font-size: 16px;
  }
}

@media (min-width: 1024px) {
  .text-responsive {
    font-size: 18px;
  }
}
