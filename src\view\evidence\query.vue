<template>
  <div class="page-container">
    <div class="page-header">
      <h1 class="page-title">区块链存证查询</h1>
      <p class="page-description">查询和验证区块链存证记录，确认数字资产的存证状态</p>
    </div>

    <div class="content-wrapper">
      <!-- 查询表单 -->
      <div class="query-section">
        <a-card title="存证查询" class="query-card">
          <a-form :model="queryForm" layout="vertical">
            <a-row :gutter="16">
              <a-col :span="12">
                <a-form-item field="queryType" label="查询方式">
                  <a-select v-model="queryForm.queryType" placeholder="请选择查询方式">
                    <a-option value="evidenceNumber">存证编号</a-option>
                    <a-option value="transactionHash">交易哈希</a-option>
                    <a-option value="fileHash">文件哈希</a-option>
                    <a-option value="applicantName">申请人姓名</a-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item field="queryValue" label="查询内容">
                  <a-input
                    v-model="queryForm.queryValue"
                    :placeholder="getQueryPlaceholder()"
                    allow-clear
                  />
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="16">
              <a-col :span="12">
                <a-form-item field="evidenceType" label="存证类型">
                  <a-select v-model="queryForm.evidenceType" placeholder="全部类型" allow-clear>
                    <a-option value="document">文档存证</a-option>
                    <a-option value="image">图片存证</a-option>
                    <a-option value="audio">音频存证</a-option>
                    <a-option value="video">视频存证</a-option>
                    <a-option value="code">代码存证</a-option>
                    <a-option value="contract">合同存证</a-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item field="dateRange" label="存证时间">
                  <a-range-picker v-model="queryForm.dateRange" />
                </a-form-item>
              </a-col>
            </a-row>
            <div class="query-actions">
              <a-button type="primary" :loading="querying" @click="handleQuery">
                <template #icon>
                  <icon-search />
                </template>
                查询
              </a-button>
              <a-button @click="resetQuery">重置</a-button>
            </div>
          </a-form>
        </a-card>
      </div>

      <!-- 查询结果 -->
      <div v-if="queryResults.length > 0" class="results-section">
        <a-card title="查询结果" class="results-card">
          <div class="result-list">
            <div v-for="item in queryResults" :key="item.id" class="result-item">
              <div class="result-header">
                <h3 class="result-title">{{ item.evidenceTitle }}</h3>
                <a-tag :color="getStatusColor(item.status)">{{ getStatusText(item.status) }}</a-tag>
              </div>
              <div class="result-content">
                <a-row :gutter="16">
                  <a-col :span="12">
                    <div class="result-info">
                      <div class="info-item">
                        <span class="label">存证编号：</span>
                        <span class="value">{{ item.evidenceNumber }}</span>
                      </div>
                      <div class="info-item">
                        <span class="label">存证类型：</span>
                        <span class="value">{{ getEvidenceTypeText(item.evidenceType) }}</span>
                      </div>
                      <div class="info-item">
                        <span class="label">申请人：</span>
                        <span class="value">{{ item.applicantName }}</span>
                      </div>
                      <div class="info-item">
                        <span class="label">存证时间：</span>
                        <span class="value">{{ item.evidenceTime }}</span>
                      </div>
                    </div>
                  </a-col>
                  <a-col :span="12">
                    <div class="result-info">
                      <div class="info-item">
                        <span class="label">交易哈希：</span>
                        <span class="value hash-value">{{ item.transactionHash }}</span>
                        <a-button type="text" size="mini" @click="copyText(item.transactionHash)">
                          <icon-copy />
                        </a-button>
                      </div>
                      <div class="info-item">
                        <span class="label">区块高度：</span>
                        <span class="value">{{ item.blockHeight }}</span>
                      </div>
                      <div class="info-item">
                        <span class="label">区块链网络：</span>
                        <span class="value">{{ getNetworkText(item.blockchainNetwork) }}</span>
                      </div>
                      <div class="info-item">
                        <span class="label">文件哈希：</span>
                        <span class="value hash-value">{{ item.fileHash }}</span>
                        <a-button type="text" size="mini" @click="copyText(item.fileHash)">
                          <icon-copy />
                        </a-button>
                      </div>
                    </div>
                  </a-col>
                </a-row>
              </div>
              <div class="result-actions">
                <a-button type="primary" size="small" @click="viewDetails(item.id)">
                  查看详情
                </a-button>
                <a-button size="small" @click="verifyEvidence(item)"> 验证存证 </a-button>
                <a-button size="small" @click="downloadCertificate(item.id)"> 下载证书 </a-button>
              </div>
            </div>
          </div>

          <div class="pagination-wrapper">
            <a-pagination
              v-model:current="currentPage"
              :total="totalResults"
              :page-size="pageSize"
              show-total
              show-jumper
              show-size-changer
              @change="handlePageChange"
              @page-size-change="handlePageSizeChange"
            />
          </div>
        </a-card>
      </div>

      <!-- 验证结果模态框 -->
      <a-modal
        v-model:visible="verifyModalVisible"
        title="存证验证结果"
        :footer="false"
        width="600px"
      >
        <div v-if="verifyResult" class="verify-result">
          <div class="verify-status">
            <icon-check-circle v-if="verifyResult.isValid" class="success-icon" />
            <icon-close-circle v-else class="error-icon" />
            <span class="status-text">
              {{ verifyResult.isValid ? '验证成功' : '验证失败' }}
            </span>
          </div>
          <div class="verify-details">
            <div class="detail-item">
              <span class="label">验证时间：</span>
              <span class="value">{{ verifyResult.verifyTime }}</span>
            </div>
            <div class="detail-item">
              <span class="label">区块链确认：</span>
              <span class="value">{{ verifyResult.blockConfirmations }}个确认</span>
            </div>
            <div class="detail-item">
              <span class="label">数据完整性：</span>
              <span class="value">{{ verifyResult.dataIntegrity ? '完整' : '已损坏' }}</span>
            </div>
            <div class="detail-item">
              <span class="label">验证说明：</span>
              <span class="value">{{ verifyResult.message }}</span>
            </div>
          </div>
        </div>
      </a-modal>

      <!-- 空状态 -->
      <div v-if="showEmptyState" class="empty-state">
        <a-empty description="暂无查询结果">
          <template #image>
            <icon-search />
          </template>
          <a-button type="primary" @click="resetQuery">重新查询</a-button>
        </a-empty>
      </div>

      <!-- 快速查询示例 -->
      <div class="quick-examples">
        <a-card title="快速查询示例" class="examples-card">
          <div class="example-list">
            <div class="example-item" @click="setQuickQuery('evidenceNumber', 'EV2024001001')">
              <div class="example-title">存证编号查询</div>
              <div class="example-desc">EV2024001001</div>
            </div>
            <div
              class="example-item"
              @click="setQuickQuery('transactionHash', '0x1234567890abcdef...')"
            >
              <div class="example-title">交易哈希查询</div>
              <div class="example-desc">0x1234567890abcdef...</div>
            </div>
            <div
              class="example-item"
              @click="setQuickQuery('fileHash', 'sha256:abcdef1234567890...')"
            >
              <div class="example-title">文件哈希查询</div>
              <div class="example-desc">sha256:abcdef1234567890...</div>
            </div>
          </div>
        </a-card>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref, computed } from 'vue';
  import { useRouter } from 'vue-router';
  import { Message } from '@arco-design/web-vue';
  import {
    IconSearch,
    IconCopy,
    IconCheckCircle,
    IconCloseCircle,
  } from '@arco-design/web-vue/es/icon';
  import { queryBlockchainEvidence, verifyBlockchainEvidence } from '@/http/api/evidence';

  const router = useRouter();

  // 查询表单
  const queryForm = ref({
    queryType: 'evidenceNumber',
    queryValue: '',
    evidenceType: '',
    dateRange: [],
  });

  // 查询状态
  const querying = ref(false);
  const queryResults = ref<any[]>([]);
  const currentPage = ref(1);
  const pageSize = ref(10);
  const totalResults = ref(0);

  // 验证相关
  const verifyModalVisible = ref(false);
  const verifyResult = ref<any>(null);

  // 计算属性
  const showEmptyState = computed(() => {
    return !querying.value && queryResults.value.length === 0 && queryForm.value.queryValue;
  });

  // 查询方法
  const handleQuery = async () => {
    if (!queryForm.value.queryValue.trim()) {
      Message.warning('请输入查询内容');
      return;
    }

    querying.value = true;
    try {
      // 构建查询参数
      const params = {
        [queryForm.value.queryType]: queryForm.value.queryValue,
        evidenceType: queryForm.value.evidenceType,
        pageNo: currentPage.value.toString(),
        pageSize: pageSize.value.toString(),
      };

      // 调用API查询
      // const response = await queryBlockchainEvidence(params);

      // 模拟查询结果
      queryResults.value = [
        {
          id: 1,
          evidenceNumber: 'EV2024001001',
          evidenceTitle: '原创文章《区块链技术发展趋势》',
          evidenceType: 'document',
          applicantName: '张某某',
          evidenceTime: '2024-01-20 14:30:25',
          transactionHash: '******************************************',
          blockHeight: '2345678',
          blockchainNetwork: 'ethereum',
          fileHash: 'sha256:abcdef1234567890abcdef1234567890abcdef12',
          status: 'confirmed',
        },
        {
          id: 2,
          evidenceNumber: 'EV2024001002',
          evidenceTitle: '摄影作品《城市夜景》',
          evidenceType: 'image',
          applicantName: '李某某',
          evidenceTime: '2024-01-20 14:25:18',
          transactionHash: '******************************************',
          blockHeight: '2345677',
          blockchainNetwork: 'polygon',
          fileHash: 'sha256:1234567890abcdef1234567890abcdef12345678',
          status: 'confirmed',
        },
      ];
      totalResults.value = 25;

      Message.success(`查询成功，找到 ${totalResults.value} 条记录`);
    } catch (error) {
      Message.error('查询失败，请重试');
      console.error('查询失败:', error);
    } finally {
      querying.value = false;
    }
  };

  const resetQuery = () => {
    queryForm.value = {
      queryType: 'evidenceNumber',
      queryValue: '',
      evidenceType: '',
      dateRange: [],
    };
    queryResults.value = [];
    currentPage.value = 1;
  };

  const handlePageChange = (page: number) => {
    currentPage.value = page;
    handleQuery();
  };

  const handlePageSizeChange = (size: number) => {
    pageSize.value = size;
    currentPage.value = 1;
    handleQuery();
  };

  // 工具方法
  const getQueryPlaceholder = () => {
    const placeholderMap: Record<string, string> = {
      evidenceNumber: '请输入存证编号，如：EV2024001001',
      transactionHash: '请输入交易哈希，如：0x1234567890abcdef...',
      fileHash: '请输入文件哈希，如：sha256:abcdef1234567890...',
      applicantName: '请输入申请人姓名',
    };
    return placeholderMap[queryForm.value.queryType] || '请输入查询内容';
  };

  const getEvidenceTypeText = (type: string) => {
    const typeMap: Record<string, string> = {
      document: '文档存证',
      image: '图片存证',
      audio: '音频存证',
      video: '视频存证',
      code: '代码存证',
      contract: '合同存证',
    };
    return typeMap[type] || type;
  };

  const getNetworkText = (network: string) => {
    const networkMap: Record<string, string> = {
      ethereum: '以太坊主网',
      polygon: 'Polygon',
      bsc: 'BSC',
      private: '私有链',
    };
    return networkMap[network] || network;
  };

  const getStatusColor = (status: string) => {
    const colorMap: Record<string, string> = {
      pending: 'orange',
      confirmed: 'green',
      failed: 'red',
    };
    return colorMap[status] || 'gray';
  };

  const getStatusText = (status: string) => {
    const textMap: Record<string, string> = {
      pending: '待确认',
      confirmed: '已确认',
      failed: '失败',
    };
    return textMap[status] || '未知';
  };

  const copyText = (text: string) => {
    navigator.clipboard.writeText(text);
    Message.success('已复制到剪贴板');
  };

  const setQuickQuery = (type: string, value: string) => {
    queryForm.value.queryType = type;
    queryForm.value.queryValue = value;
  };

  // 操作方法
  const viewDetails = (id: number) => {
    router.push(`/evidence/details/${id}`);
  };

  const verifyEvidence = async (item: any) => {
    try {
      // 调用验证API
      // const response = await verifyBlockchainEvidence({
      //   evidenceHash: item.fileHash,
      //   transactionHash: item.transactionHash,
      //   blockNumber: item.blockHeight,
      // });

      // 模拟验证结果
      verifyResult.value = {
        isValid: true,
        verifyTime: new Date().toLocaleString(),
        blockConfirmations: 1024,
        dataIntegrity: true,
        message: '存证数据完整，区块链记录有效，验证通过。',
      };

      verifyModalVisible.value = true;
    } catch (error) {
      Message.error('验证失败，请重试');
      console.error('验证失败:', error);
    }
  };

  const downloadCertificate = (id: number) => {
    // 这里应该实现下载证书的逻辑
    Message.info('存证证书下载功能开发中...');
  };
</script>

<style lang="scss" scoped>
  .page-container {
    width: 100%;
    min-height: 100vh;
    background-color: #f7f8fa;
    padding-bottom: 40px;
  }

  .page-header {
    background: linear-gradient(135deg, #165dff 0%, #4080ff 100%);
    color: white;
    padding: 40px 0;
    text-align: center;

    .page-title {
      font-size: 32px;
      font-weight: 600;
      margin-bottom: 12px;
    }

    .page-description {
      font-size: 16px;
      opacity: 0.9;
      margin: 0;
    }
  }

  .content-wrapper {
    max-width: 1200px;
    margin: 0 auto;
    padding: 40px 24px;
  }

  .query-section {
    margin-bottom: 32px;

    .query-card {
      border-radius: 12px;
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
    }

    .query-actions {
      display: flex;
      gap: 16px;
      justify-content: center;
      margin-top: 24px;
    }
  }

  .results-section {
    margin-bottom: 32px;

    .results-card {
      border-radius: 12px;
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
    }

    .result-list {
      .result-item {
        padding: 24px;
        border: 1px solid #e5e6eb;
        border-radius: 8px;
        margin-bottom: 16px;
        background: white;

        &:last-child {
          margin-bottom: 0;
        }

        .result-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 16px;

          .result-title {
            font-size: 16px;
            font-weight: 600;
            color: #1d2129;
            margin: 0;
          }
        }

        .result-content {
          margin-bottom: 16px;

          .result-info {
            .info-item {
              display: flex;
              align-items: center;
              margin-bottom: 8px;
              gap: 8px;

              .label {
                color: #86909c;
                width: 80px;
                flex-shrink: 0;
                font-size: 14px;
              }

              .value {
                color: #1d2129;
                font-weight: 500;
                flex: 1;

                &.hash-value {
                  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
                  font-size: 12px;
                  word-break: break-all;
                }
              }
            }
          }
        }

        .result-actions {
          display: flex;
          gap: 12px;
          flex-wrap: wrap;
        }
      }
    }

    .pagination-wrapper {
      display: flex;
      justify-content: center;
      margin-top: 32px;
    }
  }

  .verify-result {
    .verify-status {
      display: flex;
      align-items: center;
      gap: 12px;
      margin-bottom: 24px;
      padding: 16px;
      border-radius: 8px;
      background: #f7f8fa;

      .success-icon {
        font-size: 24px;
        color: #00b42a;
      }

      .error-icon {
        font-size: 24px;
        color: #f53f3f;
      }

      .status-text {
        font-size: 16px;
        font-weight: 600;
        color: #1d2129;
      }
    }

    .verify-details {
      .detail-item {
        display: flex;
        margin-bottom: 12px;

        .label {
          color: #86909c;
          width: 100px;
          flex-shrink: 0;
        }

        .value {
          color: #1d2129;
          font-weight: 500;
          flex: 1;
        }
      }
    }
  }

  .empty-state {
    text-align: center;
    padding: 80px 0;
    color: #86909c;

    .arco-icon {
      font-size: 64px;
      margin-bottom: 16px;
    }
  }

  .quick-examples {
    .examples-card {
      border-radius: 12px;
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
    }

    .example-list {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: 16px;

      .example-item {
        padding: 16px;
        border: 1px solid #e5e6eb;
        border-radius: 8px;
        cursor: pointer;
        transition: all 0.3s ease;

        &:hover {
          border-color: #165dff;
          background: #f0f5ff;
        }

        .example-title {
          font-size: 14px;
          font-weight: 600;
          color: #1d2129;
          margin-bottom: 8px;
        }

        .example-desc {
          font-size: 12px;
          color: #86909c;
          font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
        }
      }
    }
  }

  :deep(.arco-card-header) {
    border-bottom: 1px solid #e5e6eb;
  }

  :deep(.arco-card-header-title) {
    font-size: 16px;
    font-weight: 600;
    color: #1d2129;
  }

  :deep(.arco-form-item-label) {
    font-weight: 500;
    color: #1d2129;
  }

  @media (max-width: 768px) {
    .content-wrapper {
      padding: 24px 16px;
    }

    .page-header {
      padding: 32px 16px;

      .page-title {
        font-size: 24px;
      }

      .page-description {
        font-size: 14px;
      }
    }

    .query-actions {
      flex-direction: column;

      .arco-btn {
        width: 100%;
      }
    }

    .result-item {
      .result-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 12px;
      }

      .result-actions {
        justify-content: center;

        .arco-btn {
          flex: 1;
          min-width: 80px;
        }
      }
    }

    .example-list {
      grid-template-columns: 1fr;
    }

    .verify-result .verify-status {
      flex-direction: column;
      text-align: center;
    }
  }
</style>
