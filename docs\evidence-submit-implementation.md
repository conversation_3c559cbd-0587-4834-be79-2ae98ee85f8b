# 数据存证提交功能实现文档

## 概述

根据接口文档 `http://127.0.0.1:4523/export/openapi/12?version=3.0`，我们已经完善了数据存证申请页面的提交功能。

## 主要改进

### 1. API接口更新

- **接口地址**: 从 `/admin-api/bisness/certificate-info/create` 更新为 `/app-api/bisness/certificate-info/create`
- **请求方式**: 使用 POST 请求体方式（符合用户偏好）
- **参数映射**: 完整映射表单字段到接口参数

### 2. 表单验证

实现了基础表单验证，包括：
- 数据名称（必填）
- 存证单位（必填）
- 数据分类（必选）
- 数据级别（必选）
- 数据生成时间（必选）

### 3. 文件上传处理

- 每项文件上传限制为1个文件
- 自动收集已上传文件的路径
- 支持多种类型的佐证材料上传
- 文件路径以逗号分隔的字符串形式提交（虽然现在每项只有1个文件）

### 4. 数据映射

完整的表单字段到接口参数映射：

| 表单字段 | 接口参数 | 说明 |
|---------|---------|------|
| dataId | dataNumber | 数据编号 |
| dataName | dataName | 数据名称 |
| dataUnit | certificationUnit | 存证单位 |
| dataCategory | dataClassification | 数据分类 |
| dataLevel | dataLevel | 数据级别 |
| dataGenerationTime | dataGenerationTime | 数据生成时间（ISO格式） |
| dataScaleValue + dataScaleUnit | dataScale | 数据规模 |
| dataResourceType | dataResourceForm | 数据资源形态 |
| dataResourceOwnership | dataResourceOwnership | 数据资源权属申明（数组转逗号分隔） |
| fileList | dataSampleFile | 数据样本文件路径 |
| sourceEvidenceFiles | dataSourceEvidenceMaterial | 数据来源佐证材料路径 |
| controlMeasureFiles | effectiveControlEvidenceMaterial | 有效控制措施佐证材料路径 |
| personalInfoFiles | personalInfoCollectionEvidenceMaterial | 个人信息采集佐证材料路径 |

### 5. 用户体验优化

- 提交按钮显示加载状态
- 详细的错误提示信息
- 提交成功后自动重置表单
- 自动清除暂存数据

### 6. 错误处理

- 网络请求异常处理
- 接口返回错误处理
- 表单验证失败处理
- 用户友好的错误提示

## 使用方法

1. 填写必填字段（数据名称、存证单位、数据分类、数据级别、数据生成时间）
2. 选填其他字段和上传相关文件
3. 点击"提交存证申请"按钮
4. 系统会自动验证表单并提交到后端
5. 提交成功后表单会自动重置

## 技术实现

### 类型定义

```typescript
interface CreateCertificateResponse {
  code: number;
  data?: number; // 返回创建的存证ID
  msg?: string;
}

interface GenerateDataNumberResponse {
  code: number;
  data?: string; // 返回生成的数据编号
  msg?: string;
}
```

### 核心函数

- `validateForm()`: 表单验证
- `collectFilePaths()`: 收集文件路径
- `submitForm()`: 提交表单
- `resetFormData()`: 重置表单数据

## 文件上传限制修改

### 修改内容

将所有文件上传组件的限制从多文件上传改为单文件上传：

1. **数据样本文件**: 从最多5个文件改为只能上传1个文件
2. **数据来源佐证材料**: 从最多10个文件改为只能上传1个文件
3. **有效控制措施佐证材料**: 从最多10个文件改为只能上传1个文件
4. **个人信息采集佐证材料**: 从最多10个文件改为只能上传1个文件

### 技术实现

- 移除了所有 `multiple` 属性
- 将 `:limit` 属性统一设置为 `1`
- 更新了上传提示文案，从"最多上传X个文件"改为"只能上传1个文件"
- 更新了相关注释，标明文件上传限制

### 代码示例

```vue
<a-upload
  action="/"
  :file-list="fileList"
  :custom-request="customUploadRequest"
  :accept="uploadConfig.acceptTypes.join(',')"
  :limit="1"
  @change="handleFileChange"
>
  <template #extra>
    <div class="upload-tip">
      支持格式：PDF、Word、Excel、PPT、图片、压缩包等，
      单个文件不超过100MB，只能上传1个文件
    </div>
  </template>
</a-upload>
```

## 注意事项

1. 确保后端接口 `/app-api/bisness/certificate-info/create` 已正确实现
2. 文件上传功能依赖于现有的文件上传工具
3. 数据生成时间会转换为 ISO 格式字符串
4. 数组类型字段（如数据资源权属申明）会转换为逗号分隔的字符串
5. **新增**: 每项文件上传现在限制为1个文件，但文件路径收集逻辑保持不变

## 代码注释说明

### 数据项注释

为了提高代码可维护性，我们为所有数据项添加了详细注释：

#### 1. 表单数据结构注释
```typescript
const formData = ref({
  // === 基础存证信息 ===
  dataId: '', // 数据编号，系统自动生成的唯一标识符
  dataName: '', // 数据名称，用户输入的数据资源名称
  dataUnit: '', // 存证单位，申请存证的机构或个人名称
  // ... 其他字段
});
```

#### 2. 文件列表注释
```typescript
// === 文件上传列表 ===
const fileList = ref([]); // 数据样本文件列表
const sourceEvidenceFiles = ref([]); // 数据来源佐证材料文件列表
// ... 其他文件列表
```

#### 3. 数据字典选项注释
```typescript
// === 数据字典选项和加载状态 ===
// 数据分类相关（data_classification）
const dataCategoryOptions = ref<{ label: string; value: string }[]>([]); // 数据分类选项列表
const dataCategoryLoading = ref(false); // 数据分类加载状态
```

#### 4. 提交数据映射注释
```typescript
const submitData = {
  // 基础存证信息
  dataNumber: formData.value.dataId, // 数据编号，系统自动生成的唯一标识
  dataName: formData.value.dataName, // 数据名称，用户输入的数据资源名称
  // ... 详细的字段说明
};
```

### 函数注释

#### 1. 核心函数注释
- `collectFilePaths()`: 收集文件路径，包含参数说明和返回值说明
- `validateForm()`: 表单验证，说明验证规则和返回值
- `submitForm()`: 提交表单，详细的流程步骤说明
- `resetFormData()`: 重置表单数据，说明重置范围
- `saveTemporary()`: 暂存表单数据，说明保存机制

#### 2. 注释规范
- 使用JSDoc格式的函数注释
- 详细说明函数功能、参数和返回值
- 在关键步骤添加行内注释
- 对复杂逻辑进行分步骤说明

## 测试建议

1. 测试必填字段验证
2. 测试文件上传和路径收集
3. 测试提交成功和失败的情况
4. 测试表单重置功能
5. 测试暂存和恢复功能
