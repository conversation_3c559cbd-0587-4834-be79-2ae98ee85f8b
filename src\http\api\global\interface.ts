// export interface PublicKey {
// 	nickName: string,
// }

export interface BannerItem {
  id: string | number;
  bannerCover: string;
  mainTitle: string;
  subTitle: string;
  introduce: string;
}

export interface MerchantCase {
  id: string | number;
  column: string;
  casesCover: string;
  title: string;
  introduce: string;
}

export interface ProductCapabilityItem {
  id: string | number;
  iconImg: string;
  mainTitle: string;
  subTitle: string;
}

export interface Product {
  id: string | number;
  name: string;
  icon?: string;
  description?: string;
  children?: Product[];
}

export interface ApiItem {
  id: string | number;
  apiName: string;
  description?: string;
  category?: string;
  status?: string;
}

export interface UploadParams {
  file: File;
  contentType: string;
  md5Base64: string;
  url: string;
}

export interface ApiResponse<T = any> {
  code: number;
  message: string;
  data: T;
  success: boolean;
  list?: T[];
  total?: number;
}
