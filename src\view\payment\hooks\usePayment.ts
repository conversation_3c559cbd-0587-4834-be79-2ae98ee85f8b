/** @format */

import { ref, computed, watch, onMounted, onUnmounted } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { Message } from '@arco-design/web-vue';
import { usePaymentStore } from '@/store/modules/payment';
import type { PaymentMethod, PaymentResult } from '@/http/api/payment/types';

/**
 * 支付逻辑Hook
 */
export function usePayment() {
  const router = useRouter();
  const route = useRoute();
  const paymentStore = usePaymentStore();

  // 本地状态
  const isSubmitting = ref(false);
  const paymentTimer = ref<number | null>(null);
  const paymentTimeout = ref(300); // 5分钟超时

  // 计算属性
  const orderId = computed(() => route.query.orderId as string);
  const orderType = computed(() => route.query.type as string);
  const orderAmount = computed(() => parseFloat(route.query.amount as string) || 0);

  // 支付状态监听
  const isPaymentProcessing = computed(() => paymentStore.paymentStatus === 'processing');
  const isPaymentSuccess = computed(() => paymentStore.paymentStatus === 'success');
  const isPaymentFailed = computed(() => paymentStore.paymentStatus === 'failed');

  /**
   * 初始化支付页面
   */
  async function initializePayment() {
    if (!orderId.value) {
      Message.error('订单ID不能为空');
      router.back();
      return;
    }

    try {
      await paymentStore.initPayment(orderId.value);
      
      // 验证订单状态
      if (paymentStore.currentOrder?.status !== 'created') {
        Message.warning('订单状态异常，无法支付');
        router.back();
        return;
      }

      // 检查订单是否过期
      const expireTime = new Date(paymentStore.currentOrder.expireTime).getTime();
      const currentTime = new Date().getTime();
      
      if (currentTime > expireTime) {
        Message.error('订单已过期');
        router.back();
        return;
      }

    } catch (error: any) {
      Message.error(error.message || '初始化支付失败');
      console.error('初始化支付失败:', error);
    }
  }

  /**
   * 提交支付
   */
  async function submitPayment(): Promise<PaymentResult | null> {
    if (!paymentStore.isPaymentValid) {
      Message.error('请选择支付方式');
      return null;
    }

    if (isSubmitting.value) {
      return null;
    }

    try {
      isSubmitting.value = true;
      
      const result = await paymentStore.createPayment();
      
      if (result.success) {
        Message.success('支付订单创建成功');
        
        // 根据支付方式处理后续逻辑
        await handlePaymentResult(result);
        
        return result;
      } else {
        Message.error(result.message || '支付失败');
        return null;
      }
      
    } catch (error: any) {
      Message.error(error.message || '支付失败');
      console.error('支付失败:', error);
      return null;
    } finally {
      isSubmitting.value = false;
    }
  }

  /**
   * 处理支付结果
   */
  async function handlePaymentResult(result: PaymentResult) {
    const hasOnlinePayment = paymentStore.hasOnlinePayment;
    
    if (hasOnlinePayment) {
      // 在线支付需要跳转或显示二维码
      if (result.redirectUrl) {
        // 跳转支付
        window.location.href = result.redirectUrl;
      } else if (result.qrCode) {
        // 二维码支付，开始轮询支付状态
        startPaymentPolling(result.paymentId);
      }
    } else {
      // 纯余额/积分/优惠券支付，直接检查结果
      if (result.paymentStatus === 'success') {
        Message.success('支付成功');
        redirectToResult('success');
      } else {
        // 开始轮询支付状态
        startPaymentPolling(result.paymentId);
      }
    }
  }

  /**
   * 开始支付状态轮询
   */
  function startPaymentPolling(paymentId: string) {
    let pollCount = 0;
    const maxPolls = paymentTimeout.value; // 最大轮询次数
    
    paymentTimer.value = window.setInterval(async () => {
      pollCount++;
      
      try {
        const status = await paymentStore.checkPaymentStatus(paymentId);
        
        if (status?.status === 'success') {
          stopPaymentPolling();
          Message.success('支付成功');
          redirectToResult('success');
        } else if (status?.status === 'failed') {
          stopPaymentPolling();
          Message.error('支付失败');
          redirectToResult('failed');
        } else if (pollCount >= maxPolls) {
          // 超时
          stopPaymentPolling();
          Message.warning('支付超时，请稍后查看支付结果');
          redirectToResult('timeout');
        }
        
      } catch (error) {
        console.error('查询支付状态失败:', error);
        
        if (pollCount >= maxPolls) {
          stopPaymentPolling();
          Message.error('查询支付状态失败');
          redirectToResult('error');
        }
      }
    }, 1000); // 每秒查询一次
  }

  /**
   * 停止支付状态轮询
   */
  function stopPaymentPolling() {
    if (paymentTimer.value) {
      clearInterval(paymentTimer.value);
      paymentTimer.value = null;
    }
  }

  /**
   * 跳转到结果页面
   */
  function redirectToResult(status: 'success' | 'failed' | 'timeout' | 'error') {
    router.push({
      name: 'PaymentResult',
      query: {
        orderId: orderId.value,
        status,
        paymentId: paymentStore.paymentResult?.paymentId
      }
    });
  }

  /**
   * 取消支付
   */
  async function cancelPayment() {
    if (!paymentStore.paymentResult?.paymentId) {
      router.back();
      return;
    }

    try {
      await paymentStore.cancelPayment(
        paymentStore.paymentResult.paymentId,
        '用户主动取消'
      );
      
      Message.success('支付已取消');
      router.back();
      
    } catch (error: any) {
      Message.error(error.message || '取消支付失败');
    }
  }

  /**
   * 重新支付
   */
  function retryPayment() {
    paymentStore.resetPaymentState();
    initializePayment();
  }

  /**
   * 选择支付方式
   */
  function selectPaymentMethod(method: PaymentMethod, amount?: number) {
    paymentStore.selectPaymentMethod(method, amount);
  }

  /**
   * 移除支付方式
   */
  function removePaymentMethod(methodType: string) {
    paymentStore.removePaymentMethod(methodType);
  }

  /**
   * 验证支付金额
   */
  function validatePaymentAmount(): boolean {
    const calculation = paymentStore.paymentCalculation;
    
    // 检查余额是否足够
    if (calculation.balanceUsed > (paymentStore.userAssets?.balance || 0)) {
      Message.error('余额不足');
      return false;
    }
    
    // 检查积分是否足够
    if (calculation.pointsUsed > (paymentStore.userAssets?.points || 0)) {
      Message.error('积分不足');
      return false;
    }
    
    // 检查最终金额
    if (calculation.finalAmount < 0) {
      Message.error('支付金额计算错误');
      return false;
    }
    
    return true;
  }

  // 监听路由变化
  watch(() => route.query, (newQuery) => {
    if (newQuery.orderId && newQuery.orderId !== orderId.value) {
      initializePayment();
    }
  });

  // 页面离开时清理
  onUnmounted(() => {
    stopPaymentPolling();
    paymentStore.resetPaymentState();
  });

  // 页面加载时初始化
  onMounted(() => {
    initializePayment();
  });

  return {
    // 状态
    isSubmitting,
    paymentTimeout,
    
    // 计算属性
    orderId,
    orderType,
    orderAmount,
    isPaymentProcessing,
    isPaymentSuccess,
    isPaymentFailed,
    
    // 方法
    initializePayment,
    submitPayment,
    cancelPayment,
    retryPayment,
    selectPaymentMethod,
    removePaymentMethod,
    validatePaymentAmount,
    startPaymentPolling,
    stopPaymentPolling,
    
    // Store状态和方法
    paymentStore
  };
}
