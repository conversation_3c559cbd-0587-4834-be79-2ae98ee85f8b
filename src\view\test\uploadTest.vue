<template>
  <div class="upload-test-container">
    <h2>文件上传测试页面</h2>

    <div class="test-section">
      <h3>环境信息</h3>
      <div class="info-item">
        <span>上传模式:</span>
        <span>{{ uploadMode }}</span>
      </div>
      <div class="info-item">
        <span>PRO API地址:</span>
        <span>{{ proApiUrl }}</span>
      </div>
      <div class="info-item">
        <span>上传URL:</span>
        <span>{{ uploadUrl }}</span>
      </div>
      <div class="info-item">
        <span>是否客户端上传:</span>
        <span>{{ isClientUpload }}</span>
      </div>
      <div class="info-item">
        <span>实际上传地址:</span>
        <span>{{ actualUploadUrl }}</span>
      </div>
    </div>

    <div class="test-section">
      <h3>API测试</h3>
      <div class="test-buttons">
        <a-button type="outline" @click="handleTestServerUpload"> 测试后端上传 </a-button>
        <a-button type="outline" @click="handleTestClientUpload"> 测试前端直连 </a-button>
      </div>
    </div>

    <div class="test-section">
      <h3>文件上传测试</h3>
      <a-upload
        :custom-request="customUploadRequest as any"
        :accept="uploadConfig.acceptTypes.join(',')"
        :limit="3"
        multiple
        @change="handleFileChange"
        @before-upload="handleBeforeUpload"
        @success="handleUploadSuccess"
        @error="handleUploadError"
      >
        <template #upload-button>
          <a-button type="primary">
            <icon-upload />
            选择文件上传
          </a-button>
        </template>
        <template #extra>
          <div class="upload-tip">
            支持格式：{{ uploadConfig.acceptTypes.join(', ') }}<br />
            单个文件不超过{{ formatFileSize(uploadConfig.maxSize) }}，最多上传3个文件
          </div>
        </template>
      </a-upload>
    </div>

    <div class="test-section">
      <h3>上传日志</h3>
      <div class="log-container">
        <div v-for="(log, index) in logs" :key="index" :class="['log-item', log.type]">
          <span class="log-time">{{ log.time }}</span>
          <span class="log-message">{{ log.message }}</span>
          <pre v-if="log.data" class="log-data">{{ JSON.stringify(log.data, null, 2) }}</pre>
        </div>
      </div>
      <a-button size="small" @click="clearLogs"> 清空日志 </a-button>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref, onMounted } from 'vue';
  import { IconUpload } from '@arco-design/web-vue/es/icon';
  import { useUpload, formatFileSize, validateFileType, validateFileSize } from '@/utils/upload';
  import { debugUpload, testServerUpload, testClientUpload } from '@/utils/uploadDebug';

  // 日志记录
  interface LogItem {
    time: string;
    type: 'info' | 'success' | 'error' | 'warning';
    message: string;
    data?: any;
  }

  const logs = ref<LogItem[]>([]);

  const addLog = (type: LogItem['type'], message: string, data?: any) => {
    logs.value.push({
      time: new Date().toLocaleTimeString(),
      type,
      message,
      data,
    });
  };

  const clearLogs = () => {
    logs.value = [];
  };

  // 环境信息
  const uploadMode = ref(import.meta.env.VITE_UPLOAD_TYPE || 'server');
  const proApiUrl = ref(import.meta.env.VITE_PRO_API_TARGET || '/pro');
  const isClientUpload = ref(uploadMode.value === 'client');
  const actualUploadUrl = ref('');

  // 文件上传配置
  const uploadConfig = {
    maxSize: 10 * 1024 * 1024, // 10MB (测试用较小值)
    acceptTypes: ['.pdf', '.doc', '.docx', '.txt', '.jpg', '.png'],
  };

  // 使用上传工具
  const { customRequest, uploadUrl } = useUpload();

  // 上传前验证
  const beforeUpload = (file: File) => {
    addLog('info', `开始验证文件: ${file.name}`);

    // 验证文件类型
    if (!validateFileType(file, uploadConfig.acceptTypes)) {
      addLog('error', `不支持的文件类型: ${file.name}`);
      return false;
    }

    // 验证文件大小
    if (!validateFileSize(file, uploadConfig.maxSize)) {
      addLog('error', `文件大小超过限制: ${formatFileSize(uploadConfig.maxSize)}`);
      return false;
    }

    addLog('success', `文件验证通过: ${file.name}`);
    return true;
  };

  // 自定义上传请求
  const customUploadRequest = (options: any) => {
    console.log('=== customUploadRequest 被调用 ===');
    console.log('完整参数:', options);

    // Arco Design Upload 组件的参数结构可能不同，让我们检查一下
    const file = options.file || options.fileItem?.file || options.fileItem;

    addLog('info', '开始上传文件', {
      options: options,
      file: file,
      fileName: file?.name,
      fileSize: file?.size,
      fileType: file?.type,
    });

    if (!file) {
      const error = new Error('未找到文件对象');
      addLog('error', '未找到文件对象', options);
      options.onError?.(error);
      return { abort: () => {} };
    }

    // 上传前验证
    if (!beforeUpload(file)) {
      const error = new Error('文件验证失败');
      addLog('error', '文件验证失败');
      options.onError?.(error);
      return { abort: () => {} };
    }

    // 包装回调函数以添加日志
    const wrappedOptions = {
      file: file, // 确保传递正确的文件对象
      onProgress: (percent: number) => {
        addLog('info', `上传进度: ${percent}%`);
        options.onProgress?.({ percent });
      },
      onSuccess: (response: any) => {
        addLog('success', '文件上传成功', response);
        options.onSuccess?.(response);
      },
      onError: (error: any) => {
        addLog('error', '文件上传失败', error);
        options.onError?.(error);
      },
    };

    // 调用上传工具
    try {
      const result = customRequest(wrappedOptions);
      if (result && typeof result.then === 'function') {
        // 如果返回Promise，包装成UploadRequest对象
        return { abort: () => {} };
      }
      return result || { abort: () => {} };
    } catch (error) {
      addLog('error', '上传调用失败', error);
      return { abort: () => {} };
    }
  };

  // 事件处理
  const handleFileChange = (fileList: any[]) => {
    addLog('info', '文件列表变化', fileList);
  };

  const handleBeforeUpload = (file: File): Promise<boolean | File> => {
    addLog('info', `准备上传文件: ${file.name}`);
    return Promise.resolve(true);
  };

  const handleUploadSuccess = (response: any) => {
    addLog('success', '上传成功回调', response);
  };

  const handleUploadError = (error: any) => {
    addLog('error', '上传失败回调', error);
  };

  // 测试函数
  const handleTestServerUpload = async () => {
    addLog('info', '开始测试后端上传');
    try {
      const result = await testServerUpload();
      addLog('success', '后端上传测试成功', result);
    } catch (error) {
      addLog('error', '后端上传测试失败', error);
    }
  };

  const handleTestClientUpload = async () => {
    addLog('info', '开始测试前端直连');
    try {
      const result = await testClientUpload();
      addLog('success', '前端直连测试成功', result);
    } catch (error) {
      addLog('error', '前端直连测试失败', error);
    }
  };

  onMounted(() => {
    addLog('info', '页面加载完成');
    addLog('info', `当前上传模式: ${uploadMode.value}`);
    addLog('info', `PRO API地址: ${proApiUrl.value}`);

    // 计算实际上传地址
    actualUploadUrl.value = proApiUrl.value + '/app-api/infra/file/upload';
    addLog('info', `实际上传地址: ${actualUploadUrl.value}`);

    // 运行调试信息
    const debugInfo = debugUpload();
    addLog('info', '调试信息已输出到控制台', debugInfo);
  });
</script>

<style scoped>
  .upload-test-container {
    padding: 20px;
    max-width: 800px;
    margin: 0 auto;
  }

  .test-section {
    margin-bottom: 30px;
    padding: 20px;
    border: 1px solid #e5e6eb;
    border-radius: 6px;
  }

  .test-section h3 {
    margin-top: 0;
    margin-bottom: 15px;
    color: #1d2129;
  }

  .info-item {
    display: flex;
    margin-bottom: 8px;
  }

  .info-item span:first-child {
    width: 120px;
    font-weight: 500;
  }

  .test-buttons {
    display: flex;
    gap: 10px;
    margin-bottom: 10px;
  }

  .upload-tip {
    font-size: 12px;
    color: #86909c;
    margin-top: 8px;
    line-height: 1.4;
  }

  .log-container {
    max-height: 400px;
    overflow-y: auto;
    border: 1px solid #e5e6eb;
    border-radius: 4px;
    padding: 10px;
    background-color: #f7f8fa;
    margin-bottom: 10px;
  }

  .log-item {
    margin-bottom: 8px;
    padding: 4px 0;
    border-bottom: 1px solid #e5e6eb;
  }

  .log-item:last-child {
    border-bottom: none;
  }

  .log-time {
    color: #86909c;
    font-size: 12px;
    margin-right: 10px;
  }

  .log-message {
    font-size: 14px;
  }

  .log-data {
    margin-top: 4px;
    padding: 8px;
    background-color: #fff;
    border-radius: 4px;
    font-size: 12px;
    color: #4e5969;
    white-space: pre-wrap;
    word-break: break-all;
  }

  .log-item.info .log-message {
    color: #1d2129;
  }

  .log-item.success .log-message {
    color: #00b42a;
  }

  .log-item.error .log-message {
    color: #f53f3f;
  }

  .log-item.warning .log-message {
    color: #ff7d00;
  }
</style>
