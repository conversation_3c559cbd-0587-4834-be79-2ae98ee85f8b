<template>
  <div class="pb-10">
    <div style="position: relative">
      <img
        class="banner-img"
        src="https://lf3-starry.byteimg.com/obj/starry/image/3h5ntpx432k_%E5%86%85%E5%AE%B9%E5%AE%9A%E5%88%B6.jpg"
        alt=""
        srcset=""
      />
      <div style="position: absolute; left: 19%; top: 24%">
        <div class="text-gray-700 text-5xl font-bold">文创链 开放文档</div>
        <div class="text-gray-700 text-2xl font-bold mt-4 mb-6">从这里开始，快速集成文创链API</div>
        <a-input-search
          v-model="searchValue"
          size="large"
          :style="{ width: '500px' }"
          search-button
          allow-clear
          @search="searchFun"
        >
          <template #button-icon>
            <icon-search />
          </template>
          <template #button-default> 搜索 </template>
        </a-input-search>
      </div>
    </div>
    <div class="module">
      <template v-if="!showSearchResult">
        <a-card title="能力动态" :bordered="false">
          <!-- <template #extra>
                        <a-link>查看更多</a-link>
                    </template> -->
          <div class="flex flex-col justify-start">
            <a-link
              v-for="item in apiDynamicInfoList"
              :key="item.id"
              icon
              style="display: inline-block"
            >
              {{ item.title }}
            </a-link>
          </div>
        </a-card>
        <a-tabs lazy-load>
          <a-tab-pane v-for="(item, index) in apiList" :key="index" :title="item.name">
            <div class="py-2">
              <a-space wrap align="start">
                <a-card
                  v-for="ele in item.child"
                  :key="ele.id"
                  :style="{ width: '360px' }"
                  :title="ele.name"
                  hoverable
                  @click="handleLink(ele)"
                >
                  <template #extra>
                    <a-link>了解更多</a-link>
                  </template>
                  {{ ele.desc }}
                </a-card>
              </a-space>
            </div>
          </a-tab-pane>
        </a-tabs>
      </template>
      <!-- 搜索结果 -->
      <template v-else>
        <a-card title="搜索结果" :bordered="false">
          <template #extra>
            <a-link
              @click="
                () => {
                  showSearchResult = false;
                  searchValue = '';
                }
              "
            >
              返回推荐
            </a-link>
          </template>
          <template v-if="!searchResultList.length">
            <a-empty />
          </template>
          <a-tabs v-else lazy-load>
            <a-tab-pane v-for="(item, index) in searchResultList" :key="index" :title="item.name">
              <div class="py-2">
                <a-space wrap align="start">
                  <a-card
                    v-for="ele in item.child"
                    :key="ele.id"
                    :style="{ width: '360px' }"
                    :title="ele.name"
                    hoverable
                    @click="handleLink(ele)"
                  >
                    <template #extra>
                      <a-link>了解更多</a-link>
                    </template>
                    {{ ele.desc }}
                  </a-card>
                </a-space>
              </div>
            </a-tab-pane>
          </a-tabs>
        </a-card>
      </template>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { ref, onMounted } from 'vue';
  import { getApiDynamicInfo, getApiList } from '@/http/api/global';
  import { useRouter } from 'vue-router';
  import { IconSearch } from '@arco-design/web-vue/es/icon';

  // 定义接口
  interface ApiDynamicInfo {
    id: string | number;
    title: string;
    content?: string;
    date?: string;
  }

  interface ApiChild {
    id: string | number;
    name: string;
    desc: string;
    routeName?: string;
  }

  interface ApiCategory {
    id?: string | number;
    name: string;
    child: ApiChild[];
  }

  const router = useRouter();
  const showSearchResult = ref(false);
  const searchValue = ref('');
  const searchLoading = ref(false);
  const searchResultList = ref<ApiCategory[]>([]);

  const searchFun = () => {
    if (searchValue.value) {
      searchLoading.value = true;
      showSearchResult.value = true;
      getApiList(searchValue.value).then(res => {
        if (res instanceof Array) {
          searchResultList.value = res as ApiCategory[];
        }
        searchLoading.value = false;
      });
    }
  };

  // API分类列表
  const apiList = ref<ApiCategory[]>([]);
  const getApiListFn = function (value: string = '') {
    getApiList(value).then(res => {
      if (res instanceof Array) {
        apiList.value = res as ApiCategory[];
      }
      searchLoading.value = false;
    });
  };
  getApiListFn();

  // 能力动态
  const apiDynamicInfoList = ref<ApiDynamicInfo[]>([]);
  const getApiDynamicInfoFn = function () {
    getApiDynamicInfo().then(res => {
      if (res instanceof Array) {
        apiDynamicInfoList.value = res as ApiDynamicInfo[];
      }
    });
  };
  getApiDynamicInfoFn();

  const handleLink = (item: ApiChild) => {
    router.push('/api/detail?id=' + item.id + '&title=' + item.name);
  };

  onMounted(() => {
    // 可以添加初始化逻辑
  });
</script>
<style lang="scss" scoped>
  .module {
    width: 1200px;
    margin: auto;
    background-color: white;
    margin-top: -40px;
    z-index: 1;
    position: relative;
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0px 0px 3px rgba(26, 26, 26, 0.2);
  }

  .banner-img {
    height: 480px;
    width: 100vw;
  }
</style>
