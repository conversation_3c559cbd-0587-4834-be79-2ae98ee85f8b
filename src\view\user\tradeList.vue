<template>
  <div class="p-4 bg-grey">
    <a-page-header
      :show-back="false"
      :style="{ background: 'var(--color-bg-2)' }"
      title="交易列表"
    />
    <a-card :bordered="false">
      <a-row>
        <a-col :flex="1">
          <a-form
            :model="form"
            :label-col-props="{ span: 7 }"
            :wrapper-col-props="{ span: 17 }"
            label-align="left"
          >
            <a-row :gutter="16">
              <a-col :span="8">
                <a-form-item field="name" label="标的名称">
                  <a-input v-model="form.name" placeholder="请输入.." />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item field="post" label="订单号">
                  <a-input v-model="form.post" placeholder="请输入.." />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item field="contentType" label="标的类型">
                  <a-select v-model="form.name" placeholder="请选择..">
                    <a-option value="section one"> Section One </a-option>
                    <a-option value="section two"> Section Two </a-option>
                    <a-option value="section three"> Section Three </a-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item field="filterType" label="交易状态">
                  <a-select v-model="form.name" placeholder="请选择..">
                    <a-option value="section one"> Section One </a-option>
                    <a-option value="section two"> Section Two </a-option>
                    <a-option value="section three"> Section Three </a-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item field="createdTime" label="交易日期">
                  <!-- v-model="form.createdTime" -->
                  <a-range-picker style="width: 100%" />
                </a-form-item>
              </a-col>
              <!-- <a-col :span="8">
                                <a-form-item field="status" :label="$t('searchTable.form.status')">
                                    <a-select v-model="form.status" :options="statusOptions"
                                        :placeholder="$t('searchTable.form.selectDefault')" />
                                </a-form-item>
                            </a-col> -->
            </a-row>
          </a-form>
        </a-col>
        <a-divider style="height: 84px" direction="vertical" />
        <a-col :flex="'86px'" style="text-align: right">
          <a-space direction="vertical" :size="18">
            <a-button type="primary" @click="search">
              <template #icon>
                <icon-search />
              </template>
              查询
            </a-button>
            <a-button @click="reset">
              <template #icon>
                <icon-refresh />
              </template>
              重置
            </a-button>
          </a-space>
        </a-col>
      </a-row>
      <a-divider style="margin-top: 0" />
      <a-row style="margin-bottom: 16px">
        <a-col :span="12">
          <a-radio-group
            type="button"
            :model-value="radioValue"
            @change="
              value => {
                radioValue = value as string;
              }
            "
          >
            <a-radio value=""> 全部 </a-radio>
            <a-radio value="1"> 待支付 </a-radio>
            <a-radio value="2"> 已取消 </a-radio>
            <a-radio value="3"> 已支付 </a-radio>
          </a-radio-group>
        </a-col>
        <a-col :span="12" style="display: flex; align-items: center; justify-content: end">
          <a-button>
            <template #icon>
              <icon-download />
            </template>
            导出
          </a-button>
        </a-col>
      </a-row>
      <a-table
        :loading="loading"
        row-key="key"
        :columns="columns as [TableColumnData]"
        :scroll="{
          x: 1300,
          y: 480,
        }"
        :scrollbar="true"
        :data="data"
        :bordered="false"
        :sticky-header="true"
        :table-layout-fixed="true"
      >
        <template #index="{ rowIndex }">
          {{ rowIndex + 1 + (searchParams.current - 1) * searchParams.size }}
        </template>
        <template #name="{ record }">
          <a-avatar v-if="record.imgUrl" :size="32" shape="square">
            <img alt="avatar" :src="record.imgUrl" />
          </a-avatar>
          <span class="ml-2">{{ record.name }}</span>
        </template>
        <template #price="{ record }">
          <span>￥</span>
          <span style="margin-left: 2px">{{ record.priceStart }}</span>
          <span>.</span>
          <span class="text-sm">{{ record.priceEnd }}</span>
        </template>
        <template #status="{ record }">
          <a-tag v-if="record.status === 0" color="green"> 交易成功 </a-tag>
          <a-tag v-else-if="record.status === 1" color="red"> 交易失败 </a-tag>
          <a-tag v-else-if="record.status === 2" color="gray"> 交易取消 </a-tag>
        </template>
        <template #operation>
          <a-button type="text" size="small"> 继续支付 </a-button>
          <a-button type="text" size="small"> 取消订单 </a-button>
        </template>
      </a-table>
    </a-card>
  </div>
</template>

<script lang="ts" setup>
  import { getAssetsFile, IntegerToFloat } from '@/utils/tool';
  import { ref, reactive, onMounted } from 'vue';
  import { IconSearch, IconRefresh, IconPlus, IconDownload } from '@arco-design/web-vue/es/icon';
  import {
    browseRecordRecord,
    deleteBrowseRecord,
    addAttentionRecord,
    deleteAttentionRecord,
  } from '@/http/api/user';
  import { Message } from '@arco-design/web-vue';

  const form = reactive({
    name: '',
    post: '',
    isRead: false,
  });
  const loading = ref(false);
  const radioValue = ref('');
  // 查询
  const search = () => {};
  // 重置
  const reset = () => {};
  const searchParams = ref({
    total: 0,
    current: 1,
    size: 9,
  });
  interface TableColumnData {
    dataIndex?: string;
    title?: string;
    width?: number;
    align?: 'left' | 'center' | 'right';
    fixed?: 'left' | 'right';
    ellipsis?: boolean;
    tooltip?: boolean | Record<string, any>;
    slotName?: string;
    titleSlotName?: string;
    isLastLeftFixed?: boolean;
    isFirstRightFixed?: boolean;
    colSpan?: number;
    rowSpan?: number;
    index?: number;
    parent?: TableColumnData;
  }
  const columns = [
    {
      title: '序号',
      slotName: 'index',
      align: 'center',
      width: 60,
    },
    {
      title: '标的物名称',
      dataIndex: 'name',
      slotName: 'name',
      ellipsis: true, // 文本超出省略
      tooltip: true, // 显示省略号时显示文本提示
      width: 200,
    },
    {
      title: '订单号',
      dataIndex: 'number',
      width: 150,
    },
    {
      title: '订单金额',
      dataIndex: 'price',
      slotName: 'price',
      width: 120,
    },
    {
      title: '订单状态',
      dataIndex: 'status',
      slotName: 'status',
      width: 120,
    },
    {
      title: '下单时间',
      dataIndex: 'createTime',
      width: 160,
    },
    {
      title: '订单备注',
      dataIndex: 'email',
      ellipsis: true, // 文本超出省略
      tooltip: true, // 显示省略号时显示文本提示
      width: 150,
    },
    {
      title: '操作',
      slotName: 'operation',
      fixed: 'right',
      align: 'center',
      width: 200,
    },
  ];
  const data = ref([
    {
      key: '1',
      name: '标的物标的物标的物标的物标的物标的物标的物标的物标的物标的物标的物标的物',
      number: new Date().getTime(),
      price: 22,
      status: 0,
      imgUrl: 'https://i.bmark.cn/bmark-cover/36866782-66d1-4966-832a-af7c138745f9.jpg',
      createTime: '2022-1-1 11:11:11',
    },
    {
      key: '1',
      name: '标的物标的物标的物标的物标的物标的物标的物标的物标的物标的物标的物标的物',
      number: new Date().getTime(),
      price: 22,
      status: 1,
      imgUrl: 'https://i.bmark.cn/bmark-cover/36866782-66d1-4966-832a-af7c138745f9.jpg',
      createTime: '2022-1-1 11:11:11',
    },
    {
      key: '1',
      name: '标的物标的物标的物标的物标的物标的物标的物标的物标的物标的物标的物标的物',
      number: new Date().getTime(),
      price: 22,
      status: 2,
      imgUrl: 'https://i.bmark.cn/bmark-cover/36866782-66d1-4966-832a-af7c138745f9.jpg',
      createTime: '2022-1-1 11:11:11',
    },
    {
      key: '1',
      name: '标的物标的物标的物标的物标的物标的物标的物标的物标的物标的物标的物标的物',
      number: new Date().getTime(),
      price: 22,
      status: 0,
      imgUrl: 'https://i.bmark.cn/bmark-cover/36866782-66d1-4966-832a-af7c138745f9.jpg',
      createTime: '2022-1-1 11:11:11',
    },
    {
      key: '1',
      name: '标的物标的物标的物标的物标的物标的物标的物标的物标的物标的物标的物标的物',
      number: new Date().getTime(),
      price: 22,
      status: 1,
      imgUrl: 'https://i.bmark.cn/bmark-cover/36866782-66d1-4966-832a-af7c138745f9.jpg',
      createTime: '2022-1-1 11:11:11',
    },
    {
      key: '1',
      name: '标的物标的物标的物标的物标的物标的物标的物标的物标的物标的物标的物标的物',
      number: new Date().getTime(),
      price: 22,
      status: 2,
      imgUrl: 'https://i.bmark.cn/bmark-cover/36866782-66d1-4966-832a-af7c138745f9.jpg',
      createTime: '2022-1-1 11:11:11',
    },
    {
      key: '1',
      name: '标的物标的物标的物标的物标的物标的物标的物标的物标的物标的物标的物标的物',
      number: new Date().getTime(),
      price: 22,
      status: 0,
      imgUrl: 'https://i.bmark.cn/bmark-cover/36866782-66d1-4966-832a-af7c138745f9.jpg',
      createTime: '2022-1-1 11:11:11',
    },
    {
      key: '1',
      name: '标的物标的物标的物标的物标的物标的物标的物标的物标的物标的物标的物标的物',
      number: new Date().getTime(),
      price: 22,
      status: 1,
      imgUrl: 'https://i.bmark.cn/bmark-cover/36866782-66d1-4966-832a-af7c138745f9.jpg',
      createTime: '2022-1-1 11:11:11',
    },
    {
      key: '1',
      name: '标的物标的物标的物标的物标的物标的物标的物标的物标的物标的物标的物标的物',
      number: new Date().getTime(),
      price: 22,
      status: 2,
      imgUrl: 'https://i.bmark.cn/bmark-cover/36866782-66d1-4966-832a-af7c138745f9.jpg',
      createTime: '2022-1-1 11:11:11',
    },
  ]);
  data.value = data.value.map(el => {
    el.price = IntegerToFloat(el.price);
    // el.priceStart = el.price.toString().split('.')[0];
    // el.priceEnd = el.price.toString().split('.')[1];
    Reflect.set(el, 'priceStart', el.price.toString().split('.')[0]);
    Reflect.set(el, 'priceEnd', el.price.toString().split('.')[0]);
    return el;
  });
</script>
<style lang="scss" scoped>
  :deep(.arco-form-item-layout-inline) {
    margin-right: 0 !important;
  }
</style>
