<template>
  <a-modal
    v-model:visible="visible"
    :width="900"
    title="存证证书"
    :footer="false"
    @cancel="handleCancel"
  >
    <div class="certificate-container">
      <!-- 证书预览区域 -->
      <div id="certificate-content" class="certificate-content">
        <div class="certificate-background">
          <img
            :src="certificateTemplate"
            alt="证书模板"
            class="certificate-template"
            @load="onTemplateLoad"
          />

          <!-- 证书信息覆盖层 -->
          <div class="certificate-overlay">
            <!-- 证书标题 -->
            <div class="field certificate-title">数据存证证书</div>

            <!-- 字段容器 -->
            <div class="fields-container">
              <!-- 数据编号 -->
              <div class="field data-number">
                <span class="field-label">数据编号：</span>
                <span class="field-value">{{ certificateData.dataNumber || '-' }}</span>
              </div>

              <!-- 数据名称 -->
              <div class="field data-name">
                <span class="field-label">数据名称：</span>
                <span class="field-value">{{ certificateData.dataName || '-' }}</span>
              </div>

              <!-- 存证单位 -->
              <div class="field certification-unit">
                <span class="field-label">存证单位：</span>
                <span class="field-value">{{ certificateData.certificationUnit || '-' }}</span>
              </div>

              <!-- 数据分类 -->
              <div class="field data-classification">
                <span class="field-label">数据分类：</span>
                <span class="field-value">{{
                  getDataClassificationText(certificateData.dataClassification)
                }}</span>
              </div>

              <!-- 数据级别 -->
              <div class="field data-level">
                <span class="field-label">数据级别：</span>
                <span class="field-value">{{ getDataLevelText(certificateData.dataLevel) }}</span>
              </div>

              <!-- 数据生成时间 -->
              <div class="field data-generation-time">
                <span class="field-label">数据生成时间：</span>
                <span class="field-value">{{
                  formatTime(certificateData.dataGenerationTime)
                }}</span>
              </div>

              <!-- 数据规模 -->
              <div class="field data-scale">
                <span class="field-label">数据规模：</span>
                <span class="field-value">{{
                  getDataScaleText(certificateData.dataScale, certificateData.dataScaleUnit)
                }}</span>
              </div>

              <!-- 数据资源形态 -->
              <div class="field data-resource-form">
                <span class="field-label">数据资源形态：</span>
                <span class="field-value">{{
                  getDataResourceFormText(certificateData.dataResourceForm)
                }}</span>
              </div>

              <!-- 数据资源权属声明 -->
              <div class="field data-ownership-declaration">
                <span class="field-label">数据资源权属声明：</span>
                <span class="field-value">{{
                  getDataResourceOwnershipText(certificateData.dataResourceOwnership)
                }}</span>
              </div>

              <!-- 数据块哈希值 -->
              <div class="field data-hash">
                <span class="field-label">数据块哈希值：</span>
                <span class="field-value">{{ certificateData.dataBlockHashValue || '-' }}</span>
              </div>

              <!-- 区块链hash行 -->
              <div class="field blockchain-hash">
                <span class="field-label">区块链hash：</span>
                <span class="field-value">{{ getBlockchainHash() }}</span>
              </div>
            </div>

            <!-- 创建时间 -->
            <div class="field create-time">
              <span class="field-label">存证时间：</span>
              <span class="field-value">{{ getBlockchainCreateTime() }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 操作按钮区域 -->
      <div class="certificate-actions">
        <a-space>
          <a-button @click="handleCancel"> 关闭 </a-button>
          <a-button type="primary" :loading="downloading" @click="downloadPDF">
            <template #icon>
              <icon-download />
            </template>
            下载证书
          </a-button>
        </a-space>
      </div>
    </div>
  </a-modal>
</template>

<script setup lang="ts">
  import { ref, computed, nextTick, onMounted } from 'vue';
  import { Message } from '@arco-design/web-vue';
  import { IconDownload } from '@arco-design/web-vue/es/icon';
  import jsPDF from 'jspdf';
  import html2canvas from 'html2canvas';
  import type { CertificateInfoRespVO } from '@/http/api/data/index';
  import { getDictDataListByType } from '@/http/api/global/proIndex';

  // 导入证书模板图片
  import certificateTemplateImg from '@/assets/images/certification/deposit_certificate_new.png';

  // 字典数据类型定义
  interface DictDataItem {
    id: number;
    label: string;
    value: string;
    dictType: string;
  }

  interface DictDataResponse {
    code: number;
    data?: DictDataItem[];
    msg?: string;
  }

  interface Props {
    visible: boolean;
    certificateData: CertificateInfoRespVO;
  }

  interface Emits {
    (e: 'update:visible', value: boolean): void;
  }

  const props = withDefaults(defineProps<Props>(), {
    visible: false,
    certificateData: () => ({}) as CertificateInfoRespVO,
  });

  const emit = defineEmits<Emits>();

  // 响应式数据
  const downloading = ref(false);
  const templateLoaded = ref(false);

  // 字典选项
  const dataClassificationOptions = ref<{ label: string; value: string }[]>([]);
  const dataLevelOptions = ref<{ label: string; value: string }[]>([]);
  const dataScaleUnitOptions = ref<{ label: string; value: string }[]>([]);
  const dataResourceFormOptions = ref<{ label: string; value: string }[]>([]);
  const dataResourceOwnershipOptions = ref<{ label: string; value: string }[]>([]);

  // 计算属性
  const visible = computed({
    get: () => props.visible,
    set: (value: boolean) => emit('update:visible', value),
  });

  const certificateTemplate = computed(() => certificateTemplateImg);

  // 方法
  const formatTime = (time?: string) => {
    if (!time) return '-';
    return new Date(time).toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
    });
  };

  const getBlockchainHash = () => {
    // 获取第一个区块链链接的交易哈希
    const firstBlockchainLink = props.certificateData.blockchainLinks?.[0];
    return firstBlockchainLink?.transactionHash || '-';
  };

  const getBlockchainCreateTime = () => {
    // 获取第一个区块链链接的创建时间
    const firstBlockchainLink = props.certificateData.blockchainLinks?.[0];
    const createDate = firstBlockchainLink?.createDate;

    if (!createDate) return '-';

    // 将时间戳转换为普通时间格式
    return new Date(Number(createDate)).toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
    });
  };

  const onTemplateLoad = () => {
    templateLoaded.value = true;
  };

  const handleCancel = () => {
    visible.value = false;
  };

  // 获取字典数据的方法
  const fetchDictOptions = async () => {
    try {
      // 并行获取所有字典数据
      const [
        dataClassificationRes,
        dataLevelRes,
        dataScaleUnitRes,
        dataResourceFormRes,
        dataResourceOwnershipRes,
      ] = await Promise.all([
        getDictDataListByType('data_classification') as Promise<DictDataResponse>,
        getDictDataListByType('data_level') as Promise<DictDataResponse>,
        getDictDataListByType('data_scale_unit') as Promise<DictDataResponse>,
        getDictDataListByType('data_resource_form') as Promise<DictDataResponse>,
        getDictDataListByType('data_resource_ownership') as Promise<DictDataResponse>,
      ]);

      // 处理数据分类
      if (dataClassificationRes?.data) {
        dataClassificationOptions.value = dataClassificationRes.data.map((item: DictDataItem) => ({
          label: item.label,
          value: item.value,
        }));
      }

      // 处理数据级别
      if (dataLevelRes?.data) {
        dataLevelOptions.value = dataLevelRes.data.map((item: DictDataItem) => ({
          label: item.label,
          value: item.value,
        }));
      }

      // 处理数据规模单位
      if (dataScaleUnitRes?.data) {
        dataScaleUnitOptions.value = dataScaleUnitRes.data.map((item: DictDataItem) => ({
          label: item.label,
          value: item.value,
        }));
      }

      // 处理数据资源形态
      if (dataResourceFormRes?.data) {
        dataResourceFormOptions.value = dataResourceFormRes.data.map((item: DictDataItem) => ({
          label: item.label,
          value: item.value,
        }));
      }

      // 处理数据资源权属
      if (dataResourceOwnershipRes?.data) {
        dataResourceOwnershipOptions.value = dataResourceOwnershipRes.data.map(
          (item: DictDataItem) => ({
            label: item.label,
            value: item.value,
          })
        );
      }
    } catch (error) {
      console.error('获取字典数据失败:', error);
    }
  };

  // 字典值转换方法
  const getDataClassificationText = (value?: string) => {
    if (!value) return '-';
    const item = dataClassificationOptions.value.find(item => item.value === value);
    return item ? item.label : value;
  };

  const getDataLevelText = (value?: string) => {
    if (!value) return '-';
    const item = dataLevelOptions.value.find(item => item.value === value);
    return item ? item.label : value;
  };

  const getDataScaleUnitText = (value?: string) => {
    if (!value) return '';
    const item = dataScaleUnitOptions.value.find(item => item.value === value);
    return item ? item.label : value;
  };

  const getDataScaleText = (dataScale?: string, dataScaleUnit?: string) => {
    if (!dataScale) return '-';
    const unitText = getDataScaleUnitText(dataScaleUnit);
    return unitText ? `${dataScale} ${unitText}` : dataScale;
  };

  const getDataResourceFormText = (value?: string) => {
    if (!value) return '-';
    const item = dataResourceFormOptions.value.find(item => item.value === value);
    return item ? item.label : value;
  };

  const getDataResourceOwnershipSingleText = (value?: string) => {
    if (!value) return '';
    const item = dataResourceOwnershipOptions.value.find(item => item.value === value);
    return item ? item.label : value;
  };

  const getDataResourceOwnershipText = (value?: string) => {
    if (!value) return '-';

    try {
      // 尝试解析JSON
      const parsedValue = JSON.parse(value);

      if (Array.isArray(parsedValue)) {
        // 如果是数组，映射每个值并用逗号连接
        return parsedValue
          .map(item => getDataResourceOwnershipSingleText(item))
          .filter(text => text) // 过滤空值
          .join('，');
      } else {
        // 如果不是数组，直接处理单个值
        return getDataResourceOwnershipSingleText(parsedValue) || '-';
      }
    } catch (error) {
      // 如果JSON解析失败，尝试作为单个值处理
      return getDataResourceOwnershipSingleText(value) || '-';
    }
  };

  const downloadPDF = async () => {
    if (!templateLoaded.value) {
      Message.warning('证书模板正在加载中，请稍后重试');
      return;
    }

    downloading.value = true;

    try {
      await nextTick();

      const element = document.getElementById('certificate-content');
      if (!element) {
        throw new Error('找不到证书内容元素');
      }

      // 获取元素的实际显示尺寸
      const elementWidth = element.offsetWidth;
      const elementHeight = element.offsetHeight;

      // 根据元素宽高比判断PDF方向
      const isLandscape = elementWidth > elementHeight;

      // 使用html2canvas截取证书内容，使用元素的实际显示尺寸
      const canvas = await html2canvas(element, {
        scale: 2, // 提高清晰度
        useCORS: true,
        allowTaint: true,
        backgroundColor: '#ffffff',
        width: elementWidth,
        height: elementHeight,
      });

      // 创建PDF，根据canvas尺寸设置合适的PDF尺寸
      const imgData = canvas.toDataURL('image/png');

      // 根据canvas尺寸计算合适的PDF页面尺寸（转换为mm）
      const canvasRatio = canvas.width / canvas.height;

      // 基于canvas比例创建适合的PDF尺寸
      const pdfDimensions = isLandscape
        ? { width: 297, height: 297 / canvasRatio } // 横向：以A4宽度为基准
        : { width: 297 * canvasRatio, height: 297 }; // 竖向：以A4高度为基准

      const pdf = new jsPDF({
        orientation: isLandscape ? 'landscape' : 'portrait',
        unit: 'mm',
        format: [pdfDimensions.width, pdfDimensions.height],
      });

      // 计算图片在PDF中的尺寸
      const pdfWidth = pdf.internal.pageSize.getWidth();
      const pdfHeight = pdf.internal.pageSize.getHeight();
      const imgWidth = canvas.width;
      const imgHeight = canvas.height;

      // 保持宽高比，尽可能填满PDF页面
      const ratio = Math.min(pdfWidth / imgWidth, pdfHeight / imgHeight);
      const finalWidth = imgWidth * ratio;
      const finalHeight = imgHeight * ratio;

      // 居中显示
      const x = (pdfWidth - finalWidth) / 2;
      const y = (pdfHeight - finalHeight) / 2;

      pdf.addImage(imgData, 'PNG', x, y, finalWidth, finalHeight);

      // 下载PDF
      const fileName = `存证证书_${props.certificateData.dataNumber || props.certificateData.id || 'unknown'}.pdf`;
      pdf.save(fileName);

      Message.success('证书下载成功');
    } catch (error) {
      console.error('下载证书失败:', error);
      Message.error('下载证书失败，请稍后重试');
    } finally {
      downloading.value = false;
    }
  };

  // 组件挂载时获取字典数据
  onMounted(() => {
    fetchDictOptions();
  });
</script>

<style scoped lang="scss">
  .certificate-container {
    .certificate-content {
      position: relative;
      width: 100%;
      max-width: 800px;
      margin: 0 auto;
      background: #fff;

      .certificate-background {
        position: relative;
        width: 100%;

        .certificate-template {
          width: 100%;
          height: auto;
          display: block;
        }

        .certificate-overlay {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;

          .fields-container {
            position: absolute;
            top: 29%;
            left: 25%;
            width: 60%;
            display: flex;
            flex-direction: column;
            gap: 16px;

            .field {
              width: 100%;

              // 针对某些字段设置特殊宽度
              &.data-name {
                max-width: 85%;
              }

              &.certification-unit {
                max-width: 80%;
              }

              &.data-ownership-declaration,
              &.data-hash,
              &.blockchain-hash {
                max-width: 95%;
              }
            }
          }

          .field {
            color: #333;
            font-weight: 500;
            font-size: 14px;
            line-height: 1.5;
            display: flex;
            flex-direction: row;
            align-items: flex-start;
            height: auto;
            min-height: fit-content;

            .field-label {
              font-weight: 600;
              color: #1a1a1a;
              margin-right: 4px;
              white-space: nowrap;
              flex-shrink: 0;
            }

            .field-value {
              word-wrap: break-word;
              word-break: break-word;
              white-space: normal;
              flex: 1;
            }

            // 证书标题和存证时间保持绝对定位
            &.certificate-title {
              position: absolute;
              top: 20%;
              left: 50%;
              transform: translateX(-50%);
              font-size: 24px;
              font-weight: bold;
              color: #1a1a1a;
              text-align: center;
            }

            &.create-time {
              position: absolute;
              top: 68%;
              right: 15%;
              margin-top: 18px;
            }
          }
        }
      }
    }

    .certificate-actions {
      margin-top: 20px;
      text-align: center;
      padding: 16px 0;
      border-top: 1px solid #e5e6eb;
    }
  }

  // 打印样式
  @media print {
    .certificate-actions {
      display: none;
    }
  }
</style>
