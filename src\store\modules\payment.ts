/** @format */

import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import type {
  OrderInfo,
  PaymentMethod,
  PaymentSelection,
  PaymentCalculation,
  UserAssets,
  CouponInfo,
  PaymentStatus,
  PaymentResult,
} from '@/http/api/payment/types';
import * as paymentApi from '@/http/api/payment';

/**
 * 支付状态管理Store
 */
export const usePaymentStore = defineStore('payment', () => {
  // 状态定义
  const currentOrder = ref<OrderInfo | null>(null);
  const paymentMethods = ref<PaymentMethod[]>([]);
  const userAssets = ref<UserAssets | null>(null);
  const selectedMethods = ref<PaymentMethod[]>([]);
  const selectedCoupons = ref<CouponInfo[]>([]);
  const paymentCalculation = ref<PaymentCalculation>({
    originalAmount: 0,
    couponDiscount: 0,
    pointsUsed: 0,
    pointsValue: 0,
    balanceUsed: 0,
    onlinePayAmount: 0,
    finalAmount: 0,
    savings: 0,
  });
  const paymentStatus = ref<PaymentStatus>('pending');
  const paymentResult = ref<PaymentResult | null>(null);
  const loading = ref(false);
  const error = ref<string | null>(null);

  // 计算属性
  const isPaymentValid = computed(() => {
    return paymentCalculation.value.finalAmount >= 0 && selectedMethods.value.length > 0;
  });

  const totalSavings = computed(() => {
    return paymentCalculation.value.savings;
  });

  const hasOnlinePayment = computed(() => {
    return selectedMethods.value.some(
      method => method.type === 'wechat' || method.type === 'alipay'
    );
  });

  // Actions

  /**
   * 初始化支付数据
   */
  async function initPayment(orderId: string) {
    try {
      loading.value = true;
      error.value = null;

      // 并行获取订单信息和用户资产
      const [orderResponse, assetsResponse] = await Promise.all([
        paymentApi.getOrderInfo({ orderId }),
        paymentApi.getUserAssets(),
      ]);

      if (orderResponse.success && orderResponse.data) {
        currentOrder.value = orderResponse.data;
        paymentCalculation.value.originalAmount = orderResponse.data.finalAmount;
        paymentCalculation.value.finalAmount = orderResponse.data.finalAmount;
      }

      if (assetsResponse.success && assetsResponse.data) {
        userAssets.value = assetsResponse.data;
      }

      // 获取支付方式
      await fetchPaymentMethods(orderId, currentOrder.value?.finalAmount || 0);
    } catch (err: any) {
      error.value = err.message || '初始化支付数据失败';
      console.error('初始化支付数据失败:', err);
    } finally {
      loading.value = false;
    }
  }

  /**
   * 获取支付方式列表
   */
  async function fetchPaymentMethods(orderId: string, amount: number) {
    try {
      const response = await paymentApi.getPaymentMethods({ orderId, amount });

      if (response.success && response.data) {
        paymentMethods.value = response.data.paymentMethods || [];

        // 更新用户资产信息
        if (response.data.userAssets) {
          userAssets.value = response.data.userAssets;
        }
      }
    } catch (err: any) {
      error.value = err.message || '获取支付方式失败';
      console.error('获取支付方式失败:', err);
    }
  }

  /**
   * 选择支付方式
   */
  function selectPaymentMethod(method: PaymentMethod, amount?: number) {
    const existingIndex = selectedMethods.value.findIndex(m => m.type === method.type);

    if (existingIndex >= 0) {
      // 更新已选择的支付方式
      selectedMethods.value[existingIndex] = {
        ...method,
        amount: amount || method.amount,
      };
    } else {
      // 添加新的支付方式
      selectedMethods.value.push({
        ...method,
        amount: amount || method.amount,
      });
    }

    // 重新计算支付金额
    calculatePayment();
  }

  /**
   * 移除支付方式
   */
  function removePaymentMethod(methodType: string) {
    selectedMethods.value = selectedMethods.value.filter(m => m.type !== methodType);
    calculatePayment();
  }

  /**
   * 选择优惠券
   */
  async function selectCoupon(coupon: CouponInfo) {
    const existingIndex = selectedCoupons.value.findIndex(c => c.couponId === coupon.couponId);

    if (existingIndex >= 0) {
      // 移除已选择的优惠券
      selectedCoupons.value.splice(existingIndex, 1);
    } else {
      // 添加优惠券
      selectedCoupons.value.push(coupon);
    }

    // 重新计算支付金额
    await calculatePayment();
  }

  /**
   * 移除优惠券
   */
  async function removeCoupon(couponId: string) {
    selectedCoupons.value = selectedCoupons.value.filter(c => c.couponId !== couponId);
    // 重新计算支付金额
    await calculatePayment();
  }

  /**
   * 计算支付金额
   */
  async function calculatePayment() {
    if (!currentOrder.value) return;

    try {
      const params = {
        orderId: currentOrder.value.orderId,
        originalAmount: currentOrder.value.finalAmount,
        couponIds: selectedCoupons.value.map(c => c.couponId),
        pointsUsed: selectedMethods.value.find(m => m.type === 'points')?.amount || 0,
        balanceUsed: selectedMethods.value.find(m => m.type === 'balance')?.amount || 0,
      };

      const response = await paymentApi.calculatePayment(params);

      if (response.success && response.data) {
        paymentCalculation.value = {
          originalAmount: response.data.originalAmount,
          couponDiscount: response.data.couponDiscount,
          pointsUsed: params.pointsUsed,
          pointsValue: response.data.pointsDiscount,
          balanceUsed: params.balanceUsed,
          onlinePayAmount: response.data.finalAmount,
          finalAmount: response.data.finalAmount,
          savings: response.data.savings,
        };
      }
    } catch (err: any) {
      error.value = err.message || '计算支付金额失败';
      console.error('计算支付金额失败:', err);
    }
  }

  /**
   * 创建支付订单
   */
  async function createPayment() {
    if (!currentOrder.value || !isPaymentValid.value) {
      throw new Error('支付信息无效');
    }

    try {
      loading.value = true;
      paymentStatus.value = 'processing';

      const params = {
        orderId: currentOrder.value.orderId,
        paymentMethods: selectedMethods.value.map(method => ({
          type: method.type,
          amount: method.amount || 0,
          couponId:
            method.type === 'coupon'
              ? selectedCoupons.value.find(c => c.applicable)?.couponId
              : undefined,
        })),
        totalAmount: paymentCalculation.value.finalAmount,
        clientInfo: {
          userAgent: navigator.userAgent,
          ip: '', // 后端获取
          platform: /Mobile|Android|iPhone|iPad/.test(navigator.userAgent)
            ? ('mobile' as const)
            : ('web' as const),
        },
      };

      const response = await paymentApi.createPaymentOrder(params);

      if (response.success && response.data) {
        paymentResult.value = response.data;
        paymentStatus.value = response.data.paymentStatus;
        return response.data;
      } else {
        throw new Error(response.message || '创建支付订单失败');
      }
    } catch (err: any) {
      paymentStatus.value = 'failed';
      error.value = err.message || '创建支付订单失败';
      throw err;
    } finally {
      loading.value = false;
    }
  }

  /**
   * 查询支付状态
   */
  async function checkPaymentStatus(paymentId: string) {
    try {
      const response = await paymentApi.queryPaymentStatus({
        paymentId,
        orderId: currentOrder.value?.orderId,
      });

      if (response.success && response.data) {
        paymentStatus.value = response.data.status;

        if (paymentResult.value) {
          paymentResult.value.paymentStatus = response.data.status;
          paymentResult.value.paidAmount = response.data.paidAmount;
          paymentResult.value.paymentTime = response.data.paymentTime;
        }

        return response.data;
      }
    } catch (err: any) {
      error.value = err.message || '查询支付状态失败';
      console.error('查询支付状态失败:', err);
    }
  }

  /**
   * 取消支付
   */
  async function cancelPayment(paymentId: string, reason?: string) {
    try {
      loading.value = true;

      const response = await paymentApi.cancelPayment({
        paymentId,
        orderId: currentOrder.value?.orderId || '',
        reason,
      });

      if (response.success) {
        paymentStatus.value = 'cancelled';
        return response.data;
      } else {
        throw new Error(response.message || '取消支付失败');
      }
    } catch (err: any) {
      error.value = err.message || '取消支付失败';
      throw err;
    } finally {
      loading.value = false;
    }
  }

  /**
   * 重置支付状态
   */
  function resetPaymentState() {
    currentOrder.value = null;
    paymentMethods.value = [];
    userAssets.value = null;
    selectedMethods.value = [];
    selectedCoupons.value = [];
    paymentCalculation.value = {
      originalAmount: 0,
      couponDiscount: 0,
      pointsUsed: 0,
      pointsValue: 0,
      balanceUsed: 0,
      onlinePayAmount: 0,
      finalAmount: 0,
      savings: 0,
    };
    paymentStatus.value = 'pending';
    paymentResult.value = null;
    loading.value = false;
    error.value = null;
  }

  return {
    // 状态
    currentOrder,
    paymentMethods,
    userAssets,
    selectedMethods,
    selectedCoupons,
    paymentCalculation,
    paymentStatus,
    paymentResult,
    loading,
    error,

    // 计算属性
    isPaymentValid,
    totalSavings,
    hasOnlinePayment,

    // 方法
    initPayment,
    fetchPaymentMethods,
    selectPaymentMethod,
    removePaymentMethod,
    selectCoupon,
    removeCoupon,
    calculatePayment,
    createPayment,
    checkPaymentStatus,
    cancelPayment,
    resetPaymentState,
  };
});
