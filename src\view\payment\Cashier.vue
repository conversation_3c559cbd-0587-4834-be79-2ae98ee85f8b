<template>
  <div class="cashier-container" :class="{ 'mobile-layout': isMobile, 'tablet-layout': isTablet }">
    <!-- 页面头部 -->
    <div class="cashier-header">
      <div class="header-content">
        <div class="header-left">
          <a-button type="text" @click="handleGoBack">
            <template #icon>
              <icon-arrow-left />
            </template>
            返回
          </a-button>
        </div>

        <div class="header-center">
          <h1 class="page-title">收银台</h1>
          <div class="page-subtitle">安全支付，放心购买</div>
        </div>

        <div class="header-right">
          <a-button type="text" @click="handleRefresh">
            <template #icon>
              <icon-refresh />
            </template>
            刷新
          </a-button>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="cashier-main">
      <div class="main-content">
        <!-- 新的垂直布局 -->
        <div class="content-vertical">
          <!-- 1. 服务类型选择 -->
          <ServiceTypeSelector
            :loading="paymentStore.loading"
            :selected-type="selectedServiceType"
            @select="handleServiceTypeSelect"
            @change="handleServiceTypeChange"
          />

          <!-- 2. 优惠券选择 -->
          <CouponSelector
            :available-coupons="paymentStore.userAssets?.availableCoupons || []"
            :selected-coupons="paymentStore.selectedCoupons"
            :order-amount="paymentStore.currentOrder?.finalAmount || 0"
            :loading="paymentStore.loading"
            @select="handleCouponSelect"
            @remove="handleCouponRemove"
          />

          <!-- 3. 支付方式选择 -->
          <PaymentMethodSelector
            :payment-methods="paymentStore.paymentMethods"
            :user-assets="paymentStore.userAssets || undefined"
            :order-amount="paymentStore.paymentCalculation.finalAmount"
            :loading="paymentStore.loading"
            @payment-change="handlePaymentMethodChange"
            @amount-change="handleAmountChange"
          />

          <!-- 4. 支付确认 -->
          <PaymentConfirmation
            :final-amount="paymentStore.paymentCalculation.finalAmount"
            :savings="paymentStore.paymentCalculation.savings"
            :submitting="isSubmitting"
            :show-cancel-button="true"
            @submit="handleSubmitPayment"
            @cancel="handleCancelPayment"
          />
        </div>

        <!-- 右侧订单信息和费用明细 -->
        <div class="content-sidebar">
          <!-- 订单信息 -->
          <OrderInfo
            :order-info="paymentStore.currentOrder"
            :loading="paymentStore.loading"
            :error="paymentStore.error"
            @retry="handleRetryInit"
          />

          <!-- 费用明细 -->
          <PaymentCalculation
            :calculation="paymentStore.paymentCalculation"
            :loading="calculationLoading"
            :error="calculationError"
            @recalculate="handleRecalculate"
          />
        </div>
      </div>
    </div>

    <!-- 支付处理中遮罩 -->
    <div v-if="isSubmitting" class="payment-processing-overlay">
      <div class="processing-content">
        <a-spin :size="24" />
        <h3 class="processing-title">支付处理中</h3>
        <p class="processing-text">请勿关闭页面，正在为您处理支付...</p>

        <!-- 支付二维码 -->
        <div v-if="qrCodeUrl" class="qr-code-section">
          <div class="qr-code-container">
            <img :src="qrCodeUrl" alt="支付二维码" class="qr-code" />
          </div>
          <p class="qr-code-tip">请使用手机扫描二维码完成支付</p>
        </div>

        <!-- 取消支付按钮 -->
        <a-button
          v-if="!qrCodeUrl"
          type="outline"
          @click="handleCancelPayment"
          class="cancel-processing-button"
        >
          取消支付
        </a-button>
      </div>
    </div>

    <!-- 错误提示弹窗 -->
    <a-modal
      v-model:visible="showErrorModal"
      title="支付失败"
      :footer="false"
      :mask-closable="false"
    >
      <div class="error-modal-content">
        <a-result status="error" :title="errorMessage">
          <template #extra>
            <a-space>
              <a-button @click="handleRetryPayment">重试支付</a-button>
              <a-button type="primary" @click="handleGoBack">返回</a-button>
            </a-space>
          </template>
        </a-result>
      </div>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
  import { ref, computed, onMounted, onUnmounted, watch } from 'vue';
  import { useRouter } from 'vue-router';
  import { Message } from '@arco-design/web-vue';
  import { IconArrowLeft, IconRefresh } from '@arco-design/web-vue/es/icon';
  import { usePaymentStore } from '@/store/modules/payment';
  import { usePayment } from './hooks/usePayment';
  import { usePaymentCalculation } from './hooks/usePaymentCalculation';
  import { usePaymentValidation } from './hooks/usePaymentValidation';
  import { useMobileAdaptation } from './hooks/useMobileAdaptation';
  import OrderInfo from './components/OrderInfo.vue';
  import ServiceTypeSelector from './components/ServiceTypeSelector.vue';
  import CouponSelector from './components/CouponSelector.vue';
  import PaymentMethodSelector from './components/PaymentMethodSelector.vue';
  import PaymentCalculation from './components/PaymentCalculation.vue';
  import PaymentConfirmation from './components/PaymentConfirmation.vue';
  import type { PaymentMethod, PaymentResult } from '@/http/api/payment/types';

  // Router
  const router = useRouter();

  // Store和Hooks
  const paymentStore = usePaymentStore();
  const { initializePayment, submitPayment, cancelPayment, retryPayment, orderId } = usePayment();
  const paymentCalculation = usePaymentCalculation();
  const { validatePayment } = usePaymentValidation();
  const { isMobile, isTablet, deviceType, isTouchDevice, isWechat, isAlipay } =
    useMobileAdaptation();

  // 本地状态
  const isSubmitting = ref(false);
  const calculationLoading = ref(false);
  const calculationError = ref<string | null>(null);
  const qrCodeUrl = ref<string>('');
  const showErrorModal = ref(false);
  const errorMessage = ref<string>('');
  const selectedServiceType = ref<string>('');

  // 计算属性
  const pageTitle = computed(() => {
    const order = paymentStore.currentOrder;
    if (order) {
      return `支付订单 - ${order.productName}`;
    }
    return '收银台';
  });

  // 方法
  async function handleRetryInit() {
    try {
      await initializePayment();
    } catch (error: any) {
      Message.error(error.message || '初始化失败');
    }
  }

  function handleServiceTypeSelect(serviceType: any) {
    console.log('选择服务类型:', serviceType);
    // 这里可以根据服务类型更新订单信息
  }

  function handleServiceTypeChange(serviceTypeValue: string) {
    selectedServiceType.value = serviceTypeValue;
    console.log('服务类型变化:', serviceTypeValue);
  }

  function handleCouponSelect(coupon: any) {
    paymentStore.selectCoupon(coupon);
    console.log('选择优惠券:', coupon);
  }

  function handleCouponRemove(couponId: string) {
    paymentStore.removeCoupon(couponId);
    console.log('移除优惠券:', couponId);
  }

  function handlePaymentMethodChange(methods: PaymentMethod[]) {
    // 支付方式变化时的处理逻辑已在store中处理
    console.log('支付方式变化:', methods);
  }

  function handleAmountChange(amount: number) {
    // 金额变化时的处理逻辑
    console.log('支付金额变化:', amount);
  }

  async function handleRecalculate() {
    try {
      calculationLoading.value = true;
      calculationError.value = null;
      await paymentStore.calculatePayment();
    } catch (error: any) {
      calculationError.value = error.message || '计算失败';
      Message.error('重新计算失败');
    } finally {
      calculationLoading.value = false;
    }
  }

  async function handleSubmitPayment() {
    if (isSubmitting.value) return;

    try {
      isSubmitting.value = true;
      qrCodeUrl.value = '';

      // 验证支付信息
      const isValid = await validatePayment();
      if (!isValid) {
        return;
      }

      // 提交支付
      const result = await submitPayment();

      if (result) {
        await handlePaymentResult(result);
      }
    } catch (error: any) {
      console.error('支付失败:', error);
      showPaymentError(error.message || '支付失败，请重试');
    } finally {
      isSubmitting.value = false;
    }
  }

  async function handlePaymentResult(result: PaymentResult) {
    if (result.success) {
      if (result.qrCode) {
        // 显示二维码支付
        qrCodeUrl.value = result.qrCode;
      } else if (result.redirectUrl) {
        // 跳转支付
        window.location.href = result.redirectUrl;
      } else if (result.paymentStatus === 'success') {
        // 直接支付成功
        Message.success('支付成功');
        redirectToResult('success', result.paymentId);
      }
    } else {
      showPaymentError(result.message || '支付失败');
    }
  }

  function showPaymentError(message: string) {
    errorMessage.value = message;
    showErrorModal.value = true;
    isSubmitting.value = false;
    qrCodeUrl.value = '';
  }

  async function handleCancelPayment() {
    try {
      if (paymentStore.paymentResult?.paymentId) {
        await cancelPayment();
      }

      isSubmitting.value = false;
      qrCodeUrl.value = '';

      Message.info('已取消支付');
      router.back();
    } catch (error: any) {
      Message.error(error.message || '取消支付失败');
    }
  }

  function handleRetryPayment() {
    showErrorModal.value = false;
    errorMessage.value = '';
    retryPayment();
  }

  function handleGoBack() {
    if (isSubmitting.value) {
      handleCancelPayment();
    } else {
      router.back();
    }
  }

  function handleRefresh() {
    window.location.reload();
  }

  function redirectToResult(status: string, paymentId?: string) {
    router.push({
      name: 'PaymentResult',
      query: {
        orderId: orderId.value,
        status,
        paymentId,
      },
    });
  }

  // 页面标题设置
  function updatePageTitle() {
    document.title = pageTitle.value;
  }

  // 生命周期
  onMounted(() => {
    updatePageTitle();
  });

  onUnmounted(() => {
    // 清理定时器等资源
    paymentStore.resetPaymentState();
  });

  // 监听页面标题变化
  watch(pageTitle, updatePageTitle);
</script>

<style scoped lang="scss">
  @import './styles/mobile.scss';

  .cashier-container {
    min-height: 100vh;
    background-color: #f7f8fa;
    position: relative;
  }

  .cashier-header {
    background-color: #ffffff;
    border-bottom: 1px solid #e5e6eb;
    padding: 16px 0;
    position: sticky;
    top: 0;
    z-index: 100;

    .header-content {
      max-width: 1200px;
      margin: 0 auto;
      padding: 0 20px;
      display: flex;
      align-items: center;
      justify-content: space-between;
    }

    .header-left,
    .header-right {
      flex: 0 0 120px;
    }

    .header-center {
      flex: 1;
      text-align: center;

      .page-title {
        font-size: 20px;
        font-weight: 600;
        color: $text-color;
        margin: 0 0 4px 0;
      }

      .page-subtitle {
        font-size: 13px;
        color: $text-color-secondary;
      }
    }

    .header-right {
      display: flex;
      justify-content: flex-end;
    }
  }

  .cashier-main {
    padding: 24px 0;

    .main-content {
      max-width: 1400px;
      margin: 0 auto;
      padding: 0 20px;
      display: grid;
      grid-template-columns: 1fr 350px;
      gap: 32px;
    }
  }

  .content-vertical {
    display: flex;
    flex-direction: column;
    gap: 24px;
  }

  .content-sidebar {
    display: flex;
    flex-direction: column;
    gap: 20px;
    position: sticky;
    top: 120px;
    height: fit-content;
  }

  .payment-processing-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;

    .processing-content {
      background-color: #ffffff;
      border-radius: 12px;
      padding: 40px;
      text-align: center;
      max-width: 400px;
      width: 90%;

      .processing-title {
        font-size: 18px;
        font-weight: 600;
        color: $text-color;
        margin: 16px 0 8px 0;
      }

      .processing-text {
        font-size: 14px;
        color: $text-color-secondary;
        margin-bottom: 24px;
      }
    }
  }

  .qr-code-section {
    margin: 24px 0;

    .qr-code-container {
      display: flex;
      justify-content: center;
      margin-bottom: 16px;

      .qr-code {
        width: 200px;
        height: 200px;
        border: 1px solid #e5e6eb;
        border-radius: 8px;
      }
    }

    .qr-code-tip {
      font-size: 14px;
      color: $text-color-secondary;
      margin: 0;
    }
  }

  .cancel-processing-button {
    margin-top: 16px;
  }

  .error-modal-content {
    text-align: center;
  }

  // 响应式设计
  @media (max-width: 1024px) {
    .cashier-main {
      .main-content {
        grid-template-columns: 1fr;
        gap: 20px;
      }
    }

    .content-right {
      position: static;
    }
  }

  @media (max-width: 768px) {
    .cashier-header {
      padding: 12px 0;

      .header-content {
        padding: 0 16px;
      }

      .header-left,
      .header-right {
        flex: 0 0 80px;
      }

      .header-center {
        .page-title {
          font-size: 18px;
        }

        .page-subtitle {
          font-size: 12px;
        }
      }
    }

    .cashier-main {
      padding: 16px 0;

      .main-content {
        padding: 0 16px;
        gap: 16px;
      }
    }

    .content-left {
      gap: 16px;
    }

    .content-right {
      gap: 16px;
    }

    .payment-processing-overlay {
      .processing-content {
        padding: 24px;

        .processing-title {
          font-size: 16px;
        }

        .processing-text {
          font-size: 13px;
        }
      }
    }

    .qr-code-section {
      .qr-code-container {
        .qr-code {
          width: 160px;
          height: 160px;
        }
      }
    }
  }
</style>
