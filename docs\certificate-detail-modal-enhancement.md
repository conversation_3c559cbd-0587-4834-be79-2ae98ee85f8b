# 存证详情弹窗完善说明

## 概述

根据后端OpenAPI接口文档，完善了存证详情弹窗功能，使其能够调用真实的API接口获取完整的存证信息并展示。

## 主要改进

### 1. 新增API接口

在 `src/http/api/data/index.ts` 中新增了客户端获取存证详情的接口：

```typescript
// 客户端 - 获取单个存证信息详情
export function getCertificateInfoClient(id: number): Promise<CommonResultCertificateInfoRespVO> {
  return proHttp.get('/app-api/bisness/certificate-info/get', {
    params: { id },
  }) as Promise<CommonResultCertificateInfoRespVO>;
}
```

### 2. 完善类型定义

更新了 `CertificateInfoRespVO` 接口，添加了缺失的字段：

```typescript
export interface CertificateInfoRespVO {
  // ... 原有字段
  creator?: string;
  updater?: string;
  updateTime?: string;
}
```

### 3. 优化详情弹窗

#### 3.1 调用真实API
- 将原来的模拟数据替换为真实的API调用
- 添加了错误处理和加载状态管理

#### 3.2 完善字段显示
根据OpenAPI文档，添加了所有可用字段的显示：
- 基本信息：数据编号、数据名称、存证单位等
- 分类信息：数据分类、数据级别、数据资源形态等
- 技术信息：数据块哈希值、访问地址、扩展信息等
- 来源信息：数据来源信息、佐证材料等
- 系统信息：创建时间、创建人、更新时间、更新人等

#### 3.3 文件管理优化
- 自动收集所有相关文件（数据样本文件、佐证材料等）
- 根据文件扩展名显示对应的图标
- 支持文件查看和下载功能

#### 3.4 界面优化
- 增加弹窗宽度到1000px，提供更好的显示空间
- 使用2列布局的描述列表，提高信息密度
- 添加滚动条支持，处理内容过多的情况
- 优化文件展示，使用卡片网格布局

### 4. 文件类型识别

新增了 `getFileType` 函数，根据文件扩展名识别文件类型：

```typescript
const getFileType = (url?: string) => {
  if (!url) return 'file';
  const extension = url.split('.').pop()?.toLowerCase();
  switch (extension) {
    case 'pdf': return 'pdf';
    case 'jpg': case 'jpeg': case 'png': case 'gif': case 'bmp': case 'webp': return 'image';
    case 'doc': case 'docx': case 'xls': case 'xlsx': case 'ppt': case 'pptx': return 'office';
    case 'txt': case 'md': return 'text';
    case 'zip': case 'rar': case '7z': return 'archive';
    default: return 'file';
  }
};
```

### 5. 下载功能增强

改进了文件下载功能，支持真实的文件下载：

```typescript
const downloadFile = (file: EvidenceFile) => {
  if (!file.url) {
    Message.warning('文件地址不存在');
    return;
  }

  try {
    const link = document.createElement('a');
    link.href = file.url;
    link.download = file.name;
    link.style.display = 'none';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    Message.success(`开始下载文件：${file.name}`);
  } catch (error) {
    console.error('下载文件失败:', error);
    Message.error('下载文件失败，请稍后重试');
  }
};
```

## 测试

创建了单元测试文件 `src/http/api/data/index.test.ts`，验证API接口的正确性：

- 测试获取单个存证详情接口
- 测试分页获取存证列表接口
- 测试错误处理机制

所有测试均通过，确保接口功能正常。

## 使用方式

1. 在存证列表页面点击"查看"按钮
2. 系统会调用API获取完整的存证详情
3. 在弹窗中查看所有存证信息
4. 可以查看和下载相关文件

## 注意事项

1. 确保后端API接口 `/app-api/bisness/certificate-info/get` 已正确实现
2. 文件下载功能依赖于文件URL的有效性
3. 部分字段可能为空，界面会显示"无"
4. 大文件下载可能需要额外的进度提示功能

## 后续优化建议

1. 添加文件预览功能（如PDF预览）
2. 添加文件下载进度提示
3. 支持批量文件下载
4. 添加存证验证功能
5. 优化移动端显示效果
