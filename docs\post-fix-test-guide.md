# POST请求修复测试指南

## 快速测试步骤

### 1. 打开页面并准备
1. 打开数据存证申请页面
2. 打开浏览器开发者工具（F12）
3. 切换到 Network 标签
4. 清除现有的网络请求记录

### 2. 填写表单
填写以下必填字段：
- 数据名称：`测试数据修复`
- 存证单位：`测试单位`
- 数据分类：选择任意选项
- 数据级别：选择任意选项
- 数据生成时间：选择任意日期
- **数据资源权属申明**：至少选择一个选项（新增必填）

### 3. 提交并检查网络请求
1. 点击"提交存证申请"按钮
2. 在 Network 标签中找到 `/app-api/bisness/certificate-info/create` 请求
3. 检查以下内容：

#### ✅ 正确的请求格式
- **Method**: `POST`
- **URL**: 应该包含 query 参数，类似：
  ```
  /app-api/bisness/certificate-info/create?dataNumber=DATA_20241216_001&dataName=测试数据修复&certificationUnit=测试单位&dataClassification=xxx&dataLevel=xxx&dataGenerationTime=2024-12-16T10:00:00.000Z&dataResourceOwnership=xxx&statusCd=PENDING
  ```
- **Request Body**: 应该为空或显示 `null`

#### ❌ 错误的请求格式（修复前）
- Request Body 包含数据
- URL 不包含 query 参数

### 4. 检查控制台日志
在 Console 标签中应该看到：
```
=== 开始收集文件路径 ===
fileList.value: []
sourceEvidenceFiles.value: []
controlMeasureFiles.value: []
personalInfoFiles.value: []

=== 文件路径收集结果 ===
dataSampleFilePaths: 
sourceEvidencePaths: 
controlMeasurePaths: 
personalInfoPaths: 

提交存证数据: {
  dataNumber: "DATA_20241216_001",
  dataName: "测试数据修复",
  certificationUnit: "测试单位",
  dataClassification: "xxx",
  dataLevel: "xxx",
  dataGenerationTime: "2024-12-16T10:00:00.000Z",
  dataResourceOwnership: "xxx",
  statusCd: "PENDING"
}
```

## 详细验证清单

### 网络请求验证

#### Request Headers
- [ ] `Content-Type: application/json`
- [ ] `Authorization: Bearer xxx` (如果已登录)
- [ ] `tenant-id: 163`

#### Request URL
- [ ] 包含 `dataNumber` 参数
- [ ] 包含 `dataName` 参数
- [ ] 包含 `certificationUnit` 参数
- [ ] 包含 `dataClassification` 参数
- [ ] 包含 `dataLevel` 参数
- [ ] 包含 `dataGenerationTime` 参数（ISO格式）
- [ ] 包含 `dataResourceOwnership` 参数（不为空）
- [ ] 包含 `statusCd=PENDING` 参数

#### Request Body
- [ ] 为空或显示 `null`
- [ ] 不包含任何 JSON 数据

#### Response
- [ ] 状态码为 200
- [ ] 返回 JSON 格式数据
- [ ] 包含 `code` 字段
- [ ] 如果成功，`code` 应该为 0

### 功能验证

#### 表单验证
- [ ] 缺少必填字段时显示错误
- [ ] 数据资源权属申明未选择时显示错误
- [ ] 所有必填字段填写后可以提交

#### 提交流程
- [ ] 提交按钮显示加载状态
- [ ] 提交成功后显示成功消息
- [ ] 提交成功后表单自动重置
- [ ] 提交失败时显示错误消息

### 文件上传验证（可选）

如果上传了文件：
- [ ] 文件上传成功
- [ ] 文件路径正确收集
- [ ] 相应的文件路径参数包含在请求中

## 常见问题排查

### 问题1: 请求体不为空
**症状**: Request Body 包含 JSON 数据
**原因**: 修复未生效或缓存问题
**解决**: 
1. 清除浏览器缓存
2. 硬刷新页面（Ctrl+F5）
3. 检查代码是否正确部署

### 问题2: URL 不包含参数
**症状**: URL 只有路径，没有 query 参数
**原因**: 参数传递有问题
**解决**:
1. 检查 `submitData` 对象是否正确构建
2. 检查 `createCertificateInfo` 函数调用
3. 查看控制台错误信息

### 问题3: 参数值为空
**症状**: URL 包含参数但值为空
**原因**: 表单数据收集有问题
**解决**:
1. 检查表单字段绑定
2. 检查数据映射逻辑
3. 使用 `window.debugFileStatus()` 检查状态

### 问题4: 请求失败
**症状**: 网络请求返回错误
**原因**: 后端接口问题或参数格式错误
**解决**:
1. 检查后端接口是否正常
2. 检查参数格式是否符合后端期望
3. 查看后端日志

## 成功标志

当看到以下情况时，说明修复成功：

1. **网络请求正确**
   - POST 请求
   - URL 包含所有参数
   - Request Body 为空

2. **功能正常**
   - 表单验证工作正常
   - 提交流程顺畅
   - 成功/失败消息正确显示

3. **数据完整**
   - 所有必填字段都在 URL 参数中
   - 参数值正确且不为空
   - 文件路径（如果有）正确传递

## 回滚方案

如果修复导致其他问题，可以临时回滚：

1. **恢复原有 post 方法**:
```typescript
post(url: string, params?: any) {
  return new Promise((resolve, reject) => {
    this.instance
      .post(url, JSON.stringify(params))
      .then(res => resolve(res.data))
      .catch(err => reject(err.data));
  });
}
```

2. **使用 GET 请求临时替代**:
```typescript
// 临时方案
return proHttp.get('/app-api/bisness/certificate-info/create', { params });
```

## 联系支持

如果测试过程中遇到问题，请提供：
1. 完整的网络请求截图
2. 控制台错误信息
3. 表单填写的数据
4. 浏览器和版本信息
