import { defineStore } from 'pinia';

export const searchHistory = defineStore('searchData', {
  persist: true, // 开启持久化存储
  state: () => ({
	  search_data: []
  }),
  getters: {
    getData(state) {
		return state.search_data
	},
  },
  actions: {
	async addHistory(data) {
		if(this.search_data.indexOf(data) == -1) {
			this.search_data.unshift(data)
			if(this.search_data.length>20) {
				this.search_data.pop()
			}
			return true
		}
	},
	async deleteHistory(data) {
		this.search_data.splice(this.search_data.indexOf(data), 1)
		return true
	},
	async userOutLogin() {
		this.search_data = []
		return true
	}
  },
});
