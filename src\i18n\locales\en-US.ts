export default {
  common: {
    confirm: 'Confirm',
    cancel: 'Cancel',
    submit: 'Submit',
    reset: 'Reset',
    search: 'Search',
    loading: 'Loading...',
    success: 'Operation Successful',
    failed: 'Operation Failed',
    noData: 'No Data',
    more: 'More',
    back: 'Back',
    home: 'Home',
  },
  header: {
    home: 'Home',
    products: 'Products',
    api: 'API Documentation',
    login: 'Login',
    register: 'Register',
    userCenter: 'User Center',
    logout: 'Logout',
  },
  footer: {
    copyright: '© 2023 Chengdu Jiutian Starspace Technology Co., Ltd. All Rights Reserved',
    icp: 'Sichuan ICP License xxxxxxxx',
  },
  login: {
    title: 'User Login',
    username: 'Userna<PERSON>',
    password: 'Password',
    remember: 'Remember Me',
    forget: 'Forgot Password',
    register: 'Register Account',
    loginBtn: 'Login',
  },
  register: {
    title: 'User Registration',
    username: 'Username',
    password: 'Password',
    confirmPassword: 'Confirm Password',
    email: 'Email',
    phone: 'Phone Number',
    code: 'Verification Code',
    agreement: 'I have read and agree to the',
    terms: 'Terms of Service',
    privacy: 'Privacy Policy',
    registerBtn: 'Register',
  },
  user: {
    userInfo: 'User Information',
    account: 'Account Information',
    products: 'My Products',
    package: 'Package Purchase',
    usage: 'Usage Query',
    follow: 'Follow Records',
    browse: 'Browse Records',
    trade: 'Transaction Records',
    asset: 'Asset Management',
    evidence: 'Evidence List',
    software: 'Software Registration List',
  },
  product: {
    all: 'All Products',
    popular: 'Popular Products',
    latest: 'Latest Products',
    free: 'Free Products',
    detail: 'Product Details',
    price: 'Price',
    buy: 'Buy',
    trial: 'Free Trial',
    feature: 'Features',
    scene: 'Application Scenarios',
    api: 'API Documentation',
  },
  api: {
    list: 'API List',
    detail: 'API Details',
    name: 'API Name',
    desc: 'Description',
    method: 'Request Method',
    url: 'Request URL',
    params: 'Request Parameters',
    response: 'Response Parameters',
    example: 'Example Code',
    debug: 'Online Debug',
  },
};
