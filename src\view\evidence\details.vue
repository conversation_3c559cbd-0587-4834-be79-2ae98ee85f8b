<template>
  <div class="page-container">
    <div class="page-header">
      <div class="header-content">
        <a-button type="text" @click="goBack" class="back-button">
          <template #icon>
            <icon-arrow-left />
          </template>
          返回
        </a-button>
        <div class="header-info">
          <h1 class="page-title">区块链存证详情</h1>
          <p class="page-description">查看区块链存证详细信息和验证结果</p>
        </div>
      </div>
    </div>

    <div class="content-wrapper">
      <div v-if="loading" class="loading-container">
        <a-spin :size="24" />
        <p>正在加载存证详情...</p>
      </div>

      <div v-else-if="evidenceInfo" class="details-container">
        <!-- 状态卡片 -->
        <a-card class="status-card">
          <div class="status-header">
            <h2>{{ evidenceInfo.evidenceTitle }}</h2>
            <a-tag :color="getStatusColor(evidenceInfo.status)" size="large">
              {{ getStatusText(evidenceInfo.status) }}
            </a-tag>
          </div>
          <div class="blockchain-info">
            <a-row :gutter="24">
              <a-col :span="8">
                <div class="blockchain-item">
                  <div class="item-label">区块高度</div>
                  <div class="item-value">{{ evidenceInfo.blockHeight }}</div>
                </div>
              </a-col>
              <a-col :span="8">
                <div class="blockchain-item">
                  <div class="item-label">确认数</div>
                  <div class="item-value">{{ evidenceInfo.confirmations }}</div>
                </div>
              </a-col>
              <a-col :span="8">
                <div class="blockchain-item">
                  <div class="item-label">网络费用</div>
                  <div class="item-value">{{ evidenceInfo.gasFee }} ETH</div>
                </div>
              </a-col>
            </a-row>
          </div>
        </a-card>

        <!-- 基本信息 -->
        <a-card title="存证基本信息" class="info-card">
          <div class="info-grid">
            <div class="info-item">
              <span class="label">存证编号：</span>
              <span class="value">{{ evidenceInfo.evidenceNumber }}</span>
            </div>
            <div class="info-item">
              <span class="label">存证类型：</span>
              <span class="value">{{ getEvidenceTypeText(evidenceInfo.evidenceType) }}</span>
            </div>
            <div class="info-item">
              <span class="label">申请人类型：</span>
              <span class="value">{{ getApplicantTypeText(evidenceInfo.applicantType) }}</span>
            </div>
            <div class="info-item">
              <span class="label">申请人：</span>
              <span class="value">{{ evidenceInfo.applicantName }}</span>
            </div>
            <div class="info-item">
              <span class="label">联系电话：</span>
              <span class="value">{{ evidenceInfo.contactPhone }}</span>
            </div>
            <div class="info-item">
              <span class="label">电子邮箱：</span>
              <span class="value">{{ evidenceInfo.contactEmail }}</span>
            </div>
            <div class="info-item">
              <span class="label">存证时间：</span>
              <span class="value">{{ evidenceInfo.evidenceTime }}</span>
            </div>
            <div class="info-item">
              <span class="label">区块链网络：</span>
              <span class="value">{{ getNetworkText(evidenceInfo.blockchainNetwork) }}</span>
            </div>
            <div class="info-item full-width">
              <span class="label">存证描述：</span>
              <span class="value">{{ evidenceInfo.evidenceDescription }}</span>
            </div>
          </div>
        </a-card>

        <!-- 区块链信息 -->
        <a-card title="区块链凭证" class="info-card">
          <div class="blockchain-evidence">
            <div class="evidence-item">
              <div class="evidence-label">
                <icon-link />
                <span>交易哈希</span>
              </div>
              <div class="evidence-value">
                <span class="hash-text">{{ evidenceInfo.transactionHash }}</span>
                <a-button type="text" size="mini" @click="copyText(evidenceInfo.transactionHash)">
                  <icon-copy />
                </a-button>
                <a-button
                  type="text"
                  size="mini"
                  @click="openBlockchainExplorer(evidenceInfo.transactionHash)"
                >
                  <icon-export />
                </a-button>
              </div>
            </div>
            <div class="evidence-item">
              <div class="evidence-label">
                <icon-code />
                <span>文件哈希</span>
              </div>
              <div class="evidence-value">
                <span class="hash-text">{{ evidenceInfo.fileHash }}</span>
                <a-button type="text" size="mini" @click="copyText(evidenceInfo.fileHash)">
                  <icon-copy />
                </a-button>
              </div>
            </div>
            <div class="evidence-item">
              <div class="evidence-label">
                <icon-calendar />
                <span>上链时间</span>
              </div>
              <div class="evidence-value">
                <span>{{ evidenceInfo.blockTime }}</span>
              </div>
            </div>
            <div class="evidence-item">
              <div class="evidence-label">
                <icon-safe />
                <span>智能合约</span>
              </div>
              <div class="evidence-value">
                <span class="hash-text">{{ evidenceInfo.contractAddress }}</span>
                <a-button type="text" size="mini" @click="copyText(evidenceInfo.contractAddress)">
                  <icon-copy />
                </a-button>
                <a-button
                  type="text"
                  size="mini"
                  @click="openBlockchainExplorer(evidenceInfo.contractAddress, 'address')"
                >
                  <icon-export />
                </a-button>
              </div>
            </div>
          </div>
        </a-card>

        <!-- 存证文件 -->
        <a-card title="存证文件" class="info-card">
          <div class="file-list">
            <div v-for="file in evidenceInfo.files" :key="file.id" class="file-item">
              <div class="file-info">
                <icon-file />
                <div class="file-details">
                  <div class="file-name">{{ file.name }}</div>
                  <div class="file-meta">
                    {{ file.type }} · {{ formatFileSize(file.size) }} · SHA256: {{ file.hash }}
                  </div>
                </div>
              </div>
              <div class="file-actions">
                <a-button type="text" size="small" @click="copyText(file.hash)">
                  <icon-copy />
                  复制哈希
                </a-button>
                <a-button type="text" size="small" @click="verifyFile(file)">
                  <icon-check-circle />
                  验证文件
                </a-button>
              </div>
            </div>
          </div>
        </a-card>

        <!-- 验证记录 -->
        <a-card title="验证记录" class="info-card">
          <a-timeline>
            <a-timeline-item
              v-for="record in evidenceInfo.verificationRecords"
              :key="record.id"
              :dot-color="getTimelineDotColor(record.result)"
            >
              <template #dot>
                <icon-check-circle v-if="record.result === 'success'" />
                <icon-exclamation-circle v-else-if="record.result === 'warning'" />
                <icon-close-circle v-else />
              </template>
              <div class="timeline-content">
                <div class="timeline-title">{{ record.title }}</div>
                <div class="timeline-time">{{ record.time }}</div>
                <div v-if="record.details" class="timeline-details">{{ record.details }}</div>
              </div>
            </a-timeline-item>
          </a-timeline>
        </a-card>

        <!-- 操作按钮 -->
        <div class="action-buttons">
          <a-button type="primary" @click="downloadCertificate">
            <template #icon>
              <icon-download />
            </template>
            下载存证证书
          </a-button>
          <a-button @click="verifyEvidence">
            <template #icon>
              <icon-check-circle />
            </template>
            重新验证
          </a-button>
          <a-button @click="shareEvidence">
            <template #icon>
              <icon-share-alt />
            </template>
            分享存证
          </a-button>
          <a-button @click="printDetails">
            <template #icon>
              <icon-printer />
            </template>
            打印详情
          </a-button>
        </div>
      </div>

      <div v-else class="error-container">
        <a-result status="404" title="未找到存证信息" subtitle="请检查存证编号是否正确">
          <template #extra>
            <a-button type="primary" @click="goBack">返回</a-button>
          </template>
        </a-result>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref, onMounted } from 'vue';
  import { useRouter, useRoute } from 'vue-router';
  import { Message } from '@arco-design/web-vue';
  import {
    IconArrowLeft,
    IconLink,
    IconCode,
    IconCalendar,
    IconSafe,
    IconFile,
    IconCopy,
    IconExport,
    IconCheckCircle,
    IconExclamationCircle,
    IconCloseCircle,
    IconDownload,
    IconShareAlt,
    IconPrinter,
  } from '@arco-design/web-vue/es/icon';
  import { getEvidenceApplication, verifyBlockchainEvidence } from '@/http/api/evidence';

  const router = useRouter();
  const route = useRoute();

  // 数据状态
  const loading = ref(true);
  const evidenceInfo = ref<any>(null);

  // 获取详情数据
  const fetchEvidenceDetails = async () => {
    try {
      loading.value = true;
      const id = route.params.id as string;

      // 模拟数据，实际应该调用API
      // const response = await getEvidenceApplication(parseInt(id));

      // 模拟数据
      evidenceInfo.value = {
        id: id,
        evidenceNumber: `EV2024${id.padStart(6, '0')}`,
        evidenceTitle: '原创文章《区块链技术发展趋势》',
        evidenceType: 'document',
        applicantType: 'individual',
        applicantName: '张某某',
        contactPhone: '13800138000',
        contactEmail: '<EMAIL>',
        evidenceDescription:
          '这是一篇关于区块链技术发展趋势的原创文章，详细分析了当前区块链技术的发展现状和未来趋势。',
        evidenceTime: '2024-01-20 14:30:25',
        blockchainNetwork: 'ethereum',
        transactionHash: '0x1234567890abcdef1234567890abcdef12345678901234567890abcdef12345678',
        blockHeight: '2345678',
        blockTime: '2024-01-20 14:32:15',
        confirmations: 1024,
        gasFee: '0.0025',
        contractAddress: '******************************************',
        fileHash: 'sha256:abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890ab',
        status: 'confirmed',
        files: [
          {
            id: 1,
            name: '区块链技术发展趋势.pdf',
            type: 'PDF文档',
            size: 2048576,
            hash: 'sha256:abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890ab',
          },
        ],
        verificationRecords: [
          {
            id: 1,
            title: '存证创建',
            time: '2024-01-20 14:30:25',
            result: 'success',
            details: '存证申请已提交，文件哈希值已计算',
          },
          {
            id: 2,
            title: '区块链上链',
            time: '2024-01-20 14:32:15',
            result: 'success',
            details: '存证数据已成功写入区块链，交易已确认',
          },
          {
            id: 3,
            title: '自动验证',
            time: '2024-01-20 15:00:00',
            result: 'success',
            details: '系统自动验证存证完整性，验证通过',
          },
          {
            id: 4,
            title: '最近验证',
            time: '2024-01-21 10:30:00',
            result: 'success',
            details: '用户手动验证存证，数据完整性确认',
          },
        ],
      };
    } catch (error) {
      console.error('获取存证详情失败:', error);
      Message.error('获取存证详情失败');
    } finally {
      loading.value = false;
    }
  };

  // 工具方法
  const getStatusColor = (status: string) => {
    const colorMap: Record<string, string> = {
      pending: 'orange',
      confirmed: 'green',
      failed: 'red',
    };
    return colorMap[status] || 'gray';
  };

  const getStatusText = (status: string) => {
    const textMap: Record<string, string> = {
      pending: '待确认',
      confirmed: '已确认',
      failed: '失败',
    };
    return textMap[status] || '未知状态';
  };

  const getEvidenceTypeText = (type: string) => {
    const typeMap: Record<string, string> = {
      document: '文档存证',
      image: '图片存证',
      audio: '音频存证',
      video: '视频存证',
      code: '代码存证',
      contract: '合同存证',
    };
    return typeMap[type] || type;
  };

  const getApplicantTypeText = (type: string) => {
    const typeMap: Record<string, string> = {
      individual: '个人',
      enterprise: '企业',
    };
    return typeMap[type] || type;
  };

  const getNetworkText = (network: string) => {
    const networkMap: Record<string, string> = {
      ethereum: '以太坊主网',
      polygon: 'Polygon',
      bsc: 'BSC',
      private: '私有链',
    };
    return networkMap[network] || network;
  };

  const getTimelineDotColor = (result: string) => {
    const colorMap: Record<string, string> = {
      success: '#00b42a',
      warning: '#ff7d00',
      error: '#f53f3f',
    };
    return colorMap[result] || '#86909c';
  };

  const formatFileSize = (size: number) => {
    if (size < 1024) return size + ' B';
    if (size < 1024 * 1024) return (size / 1024).toFixed(1) + ' KB';
    return (size / (1024 * 1024)).toFixed(1) + ' MB';
  };

  const copyText = (text: string) => {
    navigator.clipboard.writeText(text);
    Message.success('已复制到剪贴板');
  };

  // 操作方法
  const goBack = () => {
    router.go(-1);
  };

  const openBlockchainExplorer = (hash: string, type: string = 'tx') => {
    const baseUrl = 'https://etherscan.io';
    const url = type === 'address' ? `${baseUrl}/address/${hash}` : `${baseUrl}/tx/${hash}`;
    window.open(url, '_blank');
  };

  const verifyFile = async (file: any) => {
    try {
      // 这里应该实现文件验证逻辑
      Message.success(`文件 ${file.name} 验证通过`);
    } catch (error) {
      Message.error('文件验证失败');
    }
  };

  const downloadCertificate = () => {
    // 这里应该实现证书下载逻辑
    Message.info('存证证书下载功能开发中...');
  };

  const verifyEvidence = async () => {
    try {
      // 调用验证API
      Message.loading('正在验证存证...');
      // const result = await verifyBlockchainEvidence({
      //   evidenceHash: evidenceInfo.value.fileHash,
      //   transactionHash: evidenceInfo.value.transactionHash,
      // });

      setTimeout(() => {
        Message.success('存证验证通过，数据完整性确认');
      }, 2000);
    } catch (error) {
      Message.error('验证失败，请重试');
    }
  };

  const shareEvidence = () => {
    const shareUrl = window.location.href;
    copyText(shareUrl);
    Message.success('存证链接已复制，可分享给他人查看');
  };

  const printDetails = () => {
    window.print();
  };

  // 组件挂载时获取数据
  onMounted(() => {
    fetchEvidenceDetails();
  });
</script>

<style lang="scss" scoped>
  .page-container {
    width: 100%;
    min-height: 100vh;
    background-color: #f7f8fa;
    padding-bottom: 40px;
  }

  .page-header {
    background: linear-gradient(135deg, #165dff 0%, #4080ff 100%);
    color: white;
    padding: 24px 0;

    .header-content {
      max-width: 1200px;
      margin: 0 auto;
      padding: 0 24px;
      display: flex;
      align-items: center;
      gap: 16px;

      .back-button {
        color: white;
        border-color: rgba(255, 255, 255, 0.3);

        &:hover {
          background-color: rgba(255, 255, 255, 0.1);
          border-color: rgba(255, 255, 255, 0.5);
        }
      }

      .header-info {
        .page-title {
          font-size: 24px;
          font-weight: 600;
          margin-bottom: 4px;
        }

        .page-description {
          font-size: 14px;
          opacity: 0.9;
          margin: 0;
        }
      }
    }
  }

  .content-wrapper {
    max-width: 1200px;
    margin: 0 auto;
    padding: 32px 24px;
  }

  .loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 80px 0;
    color: #86909c;

    p {
      margin-top: 16px;
      font-size: 14px;
    }
  }

  .details-container {
    display: flex;
    flex-direction: column;
    gap: 24px;
  }

  .status-card {
    .status-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 24px;

      h2 {
        font-size: 20px;
        font-weight: 600;
        color: #1d2129;
        margin: 0;
      }
    }

    .blockchain-info {
      .blockchain-item {
        text-align: center;
        padding: 16px;
        background: #f7f8fa;
        border-radius: 8px;

        .item-label {
          font-size: 12px;
          color: #86909c;
          margin-bottom: 8px;
        }

        .item-value {
          font-size: 18px;
          font-weight: 600;
          color: #165dff;
        }
      }
    }
  }

  .info-card {
    .info-grid {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 16px;

      .info-item {
        display: flex;
        align-items: flex-start;
        gap: 8px;

        &.full-width {
          grid-column: 1 / -1;
          flex-direction: column;
          gap: 8px;
        }

        .label {
          color: #86909c;
          width: 120px;
          flex-shrink: 0;
          font-size: 14px;
        }

        .value {
          color: #1d2129;
          font-weight: 500;
          flex: 1;
          word-break: break-word;
        }
      }
    }
  }

  .blockchain-evidence {
    .evidence-item {
      display: flex;
      align-items: center;
      padding: 16px;
      border: 1px solid #e5e6eb;
      border-radius: 8px;
      margin-bottom: 16px;

      &:last-child {
        margin-bottom: 0;
      }

      .evidence-label {
        display: flex;
        align-items: center;
        gap: 8px;
        width: 120px;
        flex-shrink: 0;
        color: #86909c;
        font-size: 14px;

        .arco-icon {
          font-size: 16px;
        }
      }

      .evidence-value {
        display: flex;
        align-items: center;
        gap: 8px;
        flex: 1;

        .hash-text {
          font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
          font-size: 12px;
          color: #1d2129;
          word-break: break-all;
          flex: 1;
        }
      }
    }
  }

  .file-list {
    .file-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 16px;
      background: #f7f8fa;
      border-radius: 8px;
      margin-bottom: 12px;

      &:last-child {
        margin-bottom: 0;
      }

      .file-info {
        display: flex;
        align-items: center;
        gap: 12px;
        flex: 1;

        .arco-icon {
          font-size: 20px;
          color: #165dff;
        }

        .file-details {
          .file-name {
            font-size: 14px;
            font-weight: 500;
            color: #1d2129;
            margin-bottom: 4px;
          }

          .file-meta {
            font-size: 11px;
            color: #86909c;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            word-break: break-all;
          }
        }
      }

      .file-actions {
        display: flex;
        gap: 8px;
      }
    }
  }

  .timeline-content {
    .timeline-title {
      font-size: 14px;
      font-weight: 500;
      color: #1d2129;
      margin-bottom: 4px;
    }

    .timeline-time {
      font-size: 12px;
      color: #86909c;
      margin-bottom: 4px;
    }

    .timeline-details {
      font-size: 12px;
      color: #4e5969;
      line-height: 1.5;
    }
  }

  .action-buttons {
    display: flex;
    justify-content: center;
    gap: 16px;
    margin-top: 32px;
    flex-wrap: wrap;

    .arco-btn {
      min-width: 140px;
    }
  }

  .error-container {
    padding: 40px 0;
  }

  :deep(.arco-card-header) {
    border-bottom: 1px solid #e5e6eb;
  }

  :deep(.arco-card-header-title) {
    font-size: 16px;
    font-weight: 600;
    color: #1d2129;
  }

  :deep(.arco-timeline-item-content) {
    padding-bottom: 20px;
  }

  @media (max-width: 768px) {
    .content-wrapper {
      padding: 24px 16px;
    }

    .page-header .header-content {
      padding: 0 16px;
      flex-direction: column;
      align-items: flex-start;
      gap: 12px;

      .back-button {
        align-self: flex-start;
      }
    }

    .status-card .status-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 16px;
    }

    .info-card .info-grid {
      grid-template-columns: 1fr;

      .info-item .label {
        width: auto;
      }
    }

    .action-buttons {
      flex-direction: column;

      .arco-btn {
        width: 100%;
      }
    }

    .file-item {
      flex-direction: column;
      align-items: flex-start;
      gap: 12px;

      .file-info {
        width: 100%;
      }

      .file-actions {
        width: 100%;
        justify-content: center;
      }
    }

    .evidence-item {
      flex-direction: column;
      align-items: flex-start;
      gap: 12px;

      .evidence-label {
        width: auto;
      }

      .evidence-value {
        width: 100%;
      }
    }

    .blockchain-info .arco-row {
      gap: 16px 0;
    }
  }

  @media print {
    .page-header,
    .action-buttons,
    .back-button {
      display: none !important;
    }

    .page-container {
      background: white !important;
    }

    .content-wrapper {
      max-width: none;
      padding: 0;
    }

    .details-container {
      gap: 16px;
    }

    :deep(.arco-card) {
      box-shadow: none !important;
      border: 1px solid #e5e6eb !important;
    }
  }
</style>
