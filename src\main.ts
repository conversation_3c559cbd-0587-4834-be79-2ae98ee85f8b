import { createApp } from 'vue';
import App from './App.vue';
import router from '@/router';
// import { createPinia } from 'pinia'
import piniaStore from './store/index';
import ArcoVue from '@arco-design/web-vue';
import '@arco-design/web-vue/dist/arco.css';
import '@/styles/app.css';
import '@/styles/style.css';
import '@/assets/fonts/fonts.css';
import '@/styles/responsive.css';
import '@/styles/mobile-adapter.css';
// 导入现代化SASS样式
import '@/styles/index.scss';
import { initResponsiveAdapter } from '@/responsive-adapter';
import { setupErrorHandle } from '@/utils/errorHandler';

// 在生产环境中禁用console
if (import.meta.env.PROD || import.meta.env.VITE_DROP_CONSOLE === 'true') {
  // 重写所有控制台方法
  window.console.log = () => {};
  window.console.info = () => {};
  window.console.warn = () => {};
  window.console.debug = () => {};
  // 保留error方法用于错误监控
  // window.console.error = () => {};
}

const app = createApp(App);

app.use(piniaStore).use(router).use(ArcoVue);

// 初始化错误处理
setupErrorHandle(app);

app.mount('#app');

// 初始化响应式适配器
initResponsiveAdapter();
