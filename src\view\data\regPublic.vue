<template>
  <div class="register-public-container">
    <div class="page-header">
      <h1 class="page-title">数据知识产权登记公示</h1>
      <p class="page-description">查看当前公示期内的数据知识产权登记项目</p>
    </div>

    <div class="content-wrapper">
      <div class="filter-section">
        <a-form :model="filterForm" layout="inline">
          <a-form-item field="keyword">
            <a-input v-model="filterForm.keyword" placeholder="请输入关键词" allow-clear />
          </a-form-item>
          <a-form-item field="category">
            <a-select
              v-model="filterForm.category"
              placeholder="请选择分类"
              style="width: 160px"
              allow-clear
            >
              <a-option value="industrial"> 工业数据 </a-option>
              <a-option value="agriculture"> 农业数据 </a-option>
              <a-option value="financial"> 金融数据 </a-option>
              <a-option value="medical"> 医疗数据 </a-option>
              <a-option value="other"> 其他 </a-option>
            </a-select>
          </a-form-item>
          <a-form-item field="status">
            <a-select
              v-model="filterForm.status"
              placeholder="公示状态"
              style="width: 120px"
              allow-clear
            >
              <a-option value="ongoing"> 公示中 </a-option>
              <a-option value="finished"> 已结束 </a-option>
            </a-select>
          </a-form-item>
          <a-form-item>
            <a-button type="primary" @click="handleSearch">
              <icon-search />
              搜索
            </a-button>
          </a-form-item>
        </a-form>
      </div>

      <div class="table-section">
        <a-table
          :data="tableData"
          :loading="loading"
          :pagination="pagination"
          @page-change="onPageChange"
        >
          <template #columns>
            <a-table-column title="申请编号" data-index="applicationNumber" />
            <a-table-column title="登记项目" data-index="title" />
            <a-table-column title="申请主体" data-index="applicant" />
            <a-table-column title="公示开始日期" data-index="startDate" />
            <a-table-column title="公示截止日期" data-index="endDate" />
            <a-table-column title="状态" data-index="status">
              <template #cell="{ record }">
                <a-tag :color="record.status === '公示中' ? 'blue' : 'green'">
                  {{ record.status }}
                </a-tag>
              </template>
            </a-table-column>
            <a-table-column title="操作">
              <template #cell="{ record }">
                <a-button type="text" size="small" @click="viewDetails(record.id)">
                  查看详情
                </a-button>
              </template>
            </a-table-column>
          </template>
        </a-table>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref, reactive, onMounted } from 'vue';
  import { useRouter } from 'vue-router';
  import { IconSearch } from '@arco-design/web-vue/es/icon';

  const router = useRouter();
  const loading = ref(false);

  const filterForm = reactive({
    keyword: '',
    category: '',
    status: '',
  });

  // 模拟表格数据
  const tableData = ref([
    {
      id: '1',
      applicationNumber: 'DIPR2025040700118',
      title: '有色金属工业-铝冶炼行业排污许可数字链组数据',
      applicant: '应辉环境科技服务（烟台）有限公司',
      startDate: '2025-04-10',
      endDate: '2025-04-24',
      status: '公示中',
    },
    {
      id: '2',
      applicationNumber: 'DIPR2025032700077',
      title: '牛类养殖周期与财务关联数据集',
      applicant: '山东动脉智能科技股份有限公司',
      startDate: '2025-03-27',
      endDate: '2025-04-24',
      status: '公示中',
    },
    {
      id: '3',
      applicationNumber: 'DIPR2025040700119',
      title: '铁合金、电解锰工业行业排污许可数字链组数据',
      applicant: '应辉环境科技服务（烟台）有限公司',
      startDate: '2025-04-07',
      endDate: '2025-04-24',
      status: '公示中',
    },
    {
      id: '4',
      applicationNumber: 'DIPR2025033000084',
      title: '石墨及其他非金属矿物制品制造行业排污许可数字链组数据',
      applicant: '应辉环境科技服务（烟台）有限公司',
      startDate: '2025-03-30',
      endDate: '2025-04-24',
      status: '公示中',
    },
  ]);

  const pagination = reactive({
    total: 100,
    current: 1,
    pageSize: 10,
  });

  const handleSearch = () => {
    loading.value = true;

    // 模拟API请求
    setTimeout(() => {
      loading.value = false;
      pagination.current = 1;
    }, 1000);
  };

  const onPageChange = (page: number) => {
    pagination.current = page;
    // 在实际应用中，这里会调用API获取对应页的数据
  };

  const viewDetails = (id: string) => {
    router.push(`/data/register/detail/${id}`);
  };

  onMounted(() => {
    // 页面加载时可以调用API获取实际数据
  });
</script>

<style lang="scss" scoped>
  .register-public-container {
    padding: 40px;
  }

  .page-header {
    margin-bottom: 40px;
    text-align: center;
  }

  .page-title {
    font-size: 32px;
    font-weight: bold;
    color: #1d2129;
    margin-bottom: 16px;
  }

  .page-description {
    font-size: 16px;
    color: #4e5969;
  }

  .content-wrapper {
    max-width: 1200px;
    margin: 0 auto;
  }

  .filter-section {
    margin-bottom: 24px;
    padding: 24px;
    background-color: #fff;
    border-radius: 4px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
  }

  .table-section {
    background-color: #fff;
    border-radius: 4px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
    padding: 24px;
  }
</style>
