<template>
  <div class="p-4 bg-grey">
    <a-page-header
      :show-back="false"
      :style="{ background: 'var(--color-bg-2)' }"
      title="产品申请"
      :subtitle="'共' + 4 + '款产品'"
    />
    <div class="py-4">
      <a-row :gutter="24">
        <a-col v-for="item in productList" :key="item.id" class="mb-4" :span="12">
          <a-card :title="item.description">
            <template #extra>
              <a-link v-if="item.flag" status="success"> 已申请 </a-link>
              <a-link v-else @click="showApplyModel(item.roleCode)"> 立即申请 </a-link>
            </template>
            <div>{{ item.introduce }}</div>
            <template #actions>
              <a-link @click="getApplyHistoryListFn(item.roleCode)"> 申请历史 </a-link>
              <a-link @click="getProductPermissionList(item.roleCode)"> 接口权限 </a-link>
            </template>
          </a-card>
        </a-col>
      </a-row>
    </div>
  </div>
  <!-- 申请理由弹出层 -->
  <a-modal
    v-model:visible="applyModel"
    title="申请理由"
    ok-text="提交"
    @cancel="applyModel = false"
    @ok="applyFn"
  >
    <a-textarea v-model="modelInput" placeholder="请输入.." allow-clear />
  </a-modal>
  <!-- 申请历史弹出层 -->
  <a-modal
    v-model:visible="applyHistoryModel"
    :width="1200"
    title="申请历史"
    ok-text="关闭"
    hide-cancel
    @ok="applyHistoryModel = false"
  >
    <a-table :columns="applyColumns" :data="applyHistoryList" />
  </a-modal>
  <!-- 接口权限弹出层 -->
  <a-modal
    v-model:visible="productPermissionModel"
    :width="600"
    title="接口权限"
    ok-text="关闭"
    hide-cancel
    @ok="applyHistoryModel = false"
  >
    <a-scrollbar style="height: 400px; overflow: auto">
      <a-list>
        <a-list-item v-for="(item, index) in productPermissionList" :key="index">
          {{ item.routeId }}
        </a-list-item>
      </a-list>
    </a-scrollbar>
  </a-modal>
</template>

<script lang="ts" setup>
  import { getAssetsFile } from '@/utils/tool';
  import { ref } from 'vue';
  import {
    getProductList,
    productApply,
    getApplyHistoryList,
    getProductById,
  } from '@/http/api/user';
  import { Message } from '@arco-design/web-vue';
  import { useRouter } from 'vue-router';

  // 定义产品项接口
  interface ProductItem {
    id: string | number;
    description: string;
    flag: boolean;
    roleCode: string;
    introduce: string;
  }

  // 定义申请历史项接口
  interface ApplyHistoryItem {
    applyTime: string;
    auditContent: string;
    auditTime: string;
    remark: string;
    status: string;
    statusText?: string;
  }

  // 定义接口权限项接口
  interface PermissionItem {
    routeId: string;
  }

  const router = useRouter();

  const priceIcon = getAssetsFile('icons/price-icon.png');

  const productList = ref<ProductItem[]>([]);
  const getProductListFn = () => {
    getProductList().then(res => {
      productList.value = res as ProductItem[];
    });
  };
  getProductListFn();

  // 产品申请
  const applyModel = ref(false);
  const productsId = ref('');
  const showApplyModel = (id: string) => {
    productsId.value = id;
    applyModel.value = true;
  };
  const modelInput = ref('');
  const applyFn = () => {
    if (!modelInput.value) {
      Message.warning('请输入申请理由');
      return;
    }
    let params = {
      remack: modelInput.value,
      roleCode: productsId.value,
    };
    productApply(params)
      .then(res => {
        Message.success('申请成功！');
        getProductListFn();
        modelInput.value = '';
      })
      .catch(() => {
        modelInput.value = '';
      });
  };
  // 申请历史
  const applyHistoryModel = ref(false);
  const applyHistoryList = ref<ApplyHistoryItem[]>([]);
  const applyColumns = [
    {
      title: '申请时间',
      dataIndex: 'applyTime',
    },
    {
      title: '审核内容',
      dataIndex: 'auditContent',
    },
    {
      title: '审核时间',
      dataIndex: 'auditTime',
    },
    {
      title: '申请原因',
      dataIndex: 'remark',
    },
    {
      title: '申请状态',
      dataIndex: 'statusText',
    },
  ];
  const getApplyHistoryListFn = (type: string) => {
    getApplyHistoryList(type).then(res => {
      applyHistoryList.value = (res as ApplyHistoryItem[]).map((ele: ApplyHistoryItem) => {
        switch (ele.status) {
          case '0':
            ele.statusText = '申请中';
            break;
          case '1':
            ele.statusText = '通过';
            break;
          case '2':
            ele.statusText = '不通过';
            break;
          case '3':
            ele.statusText = '取消';
            break;
        }
        return ele;
      });
      applyHistoryModel.value = true;
    });
  };

  // 接口权限查询
  const productPermissionModel = ref(false);
  const productPermissionList = ref<PermissionItem[]>([]);
  const getProductPermissionList = (type: string) => {
    getProductById(type).then(res => {
      productPermissionList.value = res as PermissionItem[];
      productPermissionModel.value = true;
    });
  };
  // 跳转 /shop/search
  const handleLink = (id: number) => {
    router.push({ path: `/shop/detail`, query: { id: id } });
  };
</script>
<style lang="scss" scoped>
  .icon-hover {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    transition: all 0.1s;
  }

  .icon-hover:hover {
    background-color: rgb(var(--gray-2));
  }
</style>
