<template>
  <div class="page-container">
    <div class="detail-header">
      <div class="back-button">
        <a-button type="text" @click="goBack"> <icon-left />返回 </a-button>
      </div>
      <h1 class="detail-title">存证详情</h1>
    </div>

    <div v-if="evidenceData" class="detail-content">
      <a-card :bordered="false" class="evidence-card">
        <template #title>
          <div class="card-header">
            <span class="card-title">{{ evidenceData.title }}</span>
            <a-tag color="blue">
              {{ evidenceData.status }}
            </a-tag>
          </div>
        </template>

        <a-descriptions :column="1" title="基本信息" bordered>
          <a-descriptions-item label="存证编号">
            {{ evidenceData.evidenceNumber }}
          </a-descriptions-item>
          <a-descriptions-item label="申请人">
            {{ evidenceData.applicant }}
          </a-descriptions-item>
          <a-descriptions-item label="申请日期">
            {{ evidenceData.applicationDate }}
          </a-descriptions-item>
          <a-descriptions-item label="存证状态">
            {{ evidenceData.status }}
          </a-descriptions-item>
          <a-descriptions-item label="数据类型">
            {{ evidenceData.dataType }}
          </a-descriptions-item>
          <a-descriptions-item label="存证哈希">
            {{ evidenceData.evidenceHash }}
          </a-descriptions-item>
        </a-descriptions>

        <div class="section-title">数据描述</div>
        <div class="description-content">
          <p>{{ evidenceData.description }}</p>
        </div>

        <div class="section-title">存证凭证</div>
        <div class="certificate-info">
          <a-descriptions :column="1" bordered>
            <a-descriptions-item label="区块高度">
              {{ evidenceData.blockHeight }}
            </a-descriptions-item>
            <a-descriptions-item label="存证时间">
              {{ evidenceData.evidenceTime }}
            </a-descriptions-item>
            <a-descriptions-item label="交易哈希">
              {{ evidenceData.txHash }}
            </a-descriptions-item>
          </a-descriptions>
        </div>

        <div class="section-title">存证文件</div>
        <div class="files-list">
          <a-table :columns="fileColumns" :data="evidenceData.files" :pagination="false">
            <template #fileName="{ record }">
              <div class="file-name">
                <icon-file />
                <span>{{ record.fileName }}</span>
              </div>
            </template>
            <template #fileHash="{ record }">
              <div class="file-hash">
                <span>{{ record.fileHash }}</span>
                <a-button type="text" @click="copyText(record.fileHash)">
                  <icon-copy />
                </a-button>
              </div>
            </template>
            <template #operation="{ record }">
              <a-button type="text" @click="downloadFile(record)"> <icon-download />下载 </a-button>
            </template>
          </a-table>
        </div>

        <div class="action-buttons">
          <a-button type="primary" @click="verifyEvidence"> 验证存证 </a-button>
          <a-button @click="printCertificate"> 打印凭证 </a-button>
        </div>
      </a-card>

      <a-card class="blockchain-info" :bordered="false" title="区块链信息">
        <a-timeline>
          <a-timeline-item>
            <div class="timeline-content">
              <div class="timeline-title">存证上链</div>
              <div class="timeline-time">
                {{ evidenceData.evidenceTime }}
              </div>
              <div class="timeline-desc">
                数据已成功存证，区块高度: {{ evidenceData.blockHeight }}
              </div>
            </div>
          </a-timeline-item>
          <a-timeline-item>
            <div class="timeline-content">
              <div class="timeline-title">存证申请</div>
              <div class="timeline-time">
                {{ evidenceData.applicationDate }}
              </div>
              <div class="timeline-desc">申请人: {{ evidenceData.applicant }}</div>
            </div>
          </a-timeline-item>
        </a-timeline>
      </a-card>
    </div>

    <div v-else class="loading-container">
      <a-spin tip="加载中..." />
    </div>

    <!-- 验证存证结果弹窗 -->
    <a-modal
      v-model:visible="verifyModalVisible"
      title="存证验证结果"
      :footer="false"
      width="500px"
    >
      <div class="verify-result">
        <div v-if="verifyResult.success" class="result-icon success">
          <icon-check-circle-fill />
        </div>
        <div v-else class="result-icon error">
          <icon-close-circle-fill />
        </div>
        <div class="result-message">
          {{ verifyResult.message }}
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
  import { ref, onMounted } from 'vue';
  import { useRouter, useRoute } from 'vue-router';
  import { Message } from '@arco-design/web-vue';
  import {
    IconLeft,
    IconFile,
    IconCopy,
    IconDownload,
    IconCheckCircleFill,
    IconCloseCircleFill,
  } from '@arco-design/web-vue/es/icon';

  const router = useRouter();
  const route = useRoute();
  const evidenceId = ref(route.params.id as string);
  const evidenceData = ref<any>(null);
  const verifyModalVisible = ref(false);
  const verifyResult = ref({
    success: true,
    message: '存证验证成功，数据未被篡改',
  });

  // 文件列表表格列定义
  const fileColumns = [
    {
      title: '文件名称',
      slotName: 'fileName',
    },
    {
      title: '文件哈希',
      slotName: 'fileHash',
    },
    {
      title: '文件大小',
      dataIndex: 'fileSize',
    },
    {
      title: '操作',
      slotName: 'operation',
    },
  ];

  // 获取存证详情
  const getEvidenceDetail = async () => {
    try {
      // 实际应用中应该调用API获取存证详情
      // 这里使用示例数据
      setTimeout(() => {
        evidenceData.value = {
          id: evidenceId.value,
          title: '铝冶炼行业排污许可数字链组数据',
          evidenceNumber: 'EVID2025040700118',
          applicant: '应辉环境科技服务（烟台）有限公司',
          applicationDate: '2025-04-07',
          status: '已完成',
          dataType: '结构化数据',
          evidenceHash: '0x8a35acfbc15c4c5db64c2f55a16a61c8795366bfc3c3a5326d89b19d18b05e3b',
          description:
            '本数据集包含有色金属工业-铝冶炼行业排污许可相关的监测数据和分析结果，用于环境监管和污染物排放控制。数据经过预处理和结构化整理，可用于行业分析和环境评估。',
          blockHeight: '18436782',
          evidenceTime: '2025-04-07 14:32:56',
          txHash: '0x9b7418d4a21e9124a27259bd7e7a89695fb31c5a8c6c2f9771794cc30afcbca1',
          files: [
            {
              fileName: '铝冶炼行业排污许可数据.xlsx',
              fileHash: '0x5a35acfbc15c4c5db64c2f55a16a61c8795366bfc3c3a5326d89b19d18b05e3c',
              fileSize: '2.4MB',
            },
            {
              fileName: '数据说明文档.pdf',
              fileHash: '0x7c35acfbc15c4c5db64c2f55a16a61c8795366bfc3c3a5326d89b19d18b05e3d',
              fileSize: '568KB',
            },
          ],
        };
      }, 1000);
    } catch (error) {
      Message.error('获取存证详情失败');
      console.error(error);
    }
  };

  // 复制文本到剪贴板
  const copyText = (text: string) => {
    navigator.clipboard
      .writeText(text)
      .then(() => {
        Message.success('复制成功');
      })
      .catch(() => {
        Message.error('复制失败');
      });
  };

  // 下载文件
  const downloadFile = (file: any) => {
    Message.info(`正在下载文件: ${file.fileName}`);
    // 实际应用中应该调用下载API
  };

  // 验证存证
  const verifyEvidence = () => {
    // 实际应用中应该调用验证API
    verifyModalVisible.value = true;
  };

  // 打印凭证
  const printCertificate = () => {
    Message.info('正在生成打印凭证');
    // 实际应用中应该生成PDF或打印页面
  };

  // 返回
  const goBack = () => {
    router.back();
  };

  onMounted(() => {
    getEvidenceDetail();
  });
</script>

<style lang="scss" scoped>
  .page-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
  }

  .detail-header {
    display: flex;
    align-items: center;
    margin-bottom: 24px;
    position: relative;
  }

  .back-button {
    position: absolute;
    left: 0;
  }

  .detail-title {
    font-size: 24px;
    font-weight: 600;
    color: #1d2129;
    flex: 1;
    text-align: center;
  }

  .detail-content {
    display: flex;
    flex-direction: column;
    gap: 24px;
  }

  .evidence-card {
    border-radius: 8px;
  }

  .card-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .card-title {
    font-size: 18px;
    font-weight: 600;
    color: #1d2129;
  }

  .section-title {
    font-size: 16px;
    font-weight: 600;
    color: #1d2129;
    margin: 24px 0 16px;
    padding-bottom: 8px;
    border-bottom: 1px solid #e5e6eb;
  }

  .description-content {
    color: #4e5969;
    line-height: 1.8;
  }

  .file-name {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .file-hash {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #86909c;
    font-family: monospace;
  }

  .action-buttons {
    display: flex;
    justify-content: center;
    gap: 16px;
    margin-top: 24px;
  }

  .blockchain-info {
    border-radius: 8px;
  }

  .timeline-content {
    padding: 4px 0;
  }

  .timeline-title {
    font-weight: 500;
    color: #1d2129;
    margin-bottom: 4px;
  }

  .timeline-time {
    color: #86909c;
    font-size: 12px;
    margin-bottom: 4px;
  }

  .timeline-desc {
    color: #4e5969;
  }

  .loading-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 400px;
  }

  .verify-result {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 24px 0;
  }

  .result-icon {
    font-size: 56px;
    margin-bottom: 16px;

    &.success {
      color: #00b42a;
    }

    &.error {
      color: #f53f3f;
    }
  }

  .result-message {
    font-size: 16px;
    color: #1d2129;
  }
</style>
