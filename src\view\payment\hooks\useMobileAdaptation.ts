/** @format */

import { ref, computed, onMounted, onUnmounted } from 'vue';

/**
 * 移动端适配Hook
 */
export function useMobileAdaptation() {
  // 响应式状态
  const windowWidth = ref(window.innerWidth);
  const windowHeight = ref(window.innerHeight);

  // 计算属性
  const isMobile = computed(() => windowWidth.value <= 768);
  const isTablet = computed(() => windowWidth.value > 768 && windowWidth.value <= 1024);
  const isDesktop = computed(() => windowWidth.value > 1024);

  // 设备类型
  const deviceType = computed(() => {
    if (isMobile.value) return 'mobile';
    if (isTablet.value) return 'tablet';
    return 'desktop';
  });

  // 屏幕方向
  const isLandscape = computed(() => windowWidth.value > windowHeight.value);
  const isPortrait = computed(() => windowWidth.value <= windowHeight.value);

  // 是否为触摸设备
  const isTouchDevice = computed(() => {
    return 'ontouchstart' in window || navigator.maxTouchPoints > 0;
  });

  // 是否为iOS设备
  const isIOS = computed(() => {
    return /iPad|iPhone|iPod/.test(navigator.userAgent);
  });

  // 是否为Android设备
  const isAndroid = computed(() => {
    return /Android/.test(navigator.userAgent);
  });

  // 是否在微信浏览器中
  const isWechat = computed(() => {
    return /MicroMessenger/i.test(navigator.userAgent);
  });

  // 是否在支付宝浏览器中
  const isAlipay = computed(() => {
    return /AlipayClient/i.test(navigator.userAgent);
  });

  // 安全区域适配（针对iPhone X等有刘海的设备）
  const safeAreaInsets = computed(() => {
    const style = getComputedStyle(document.documentElement);
    return {
      top: style.getPropertyValue('--safe-area-inset-top') || '0px',
      right: style.getPropertyValue('--safe-area-inset-right') || '0px',
      bottom: style.getPropertyValue('--safe-area-inset-bottom') || '0px',
      left: style.getPropertyValue('--safe-area-inset-left') || '0px'
    };
  });

  // 视口高度（考虑移动端地址栏影响）
  const viewportHeight = computed(() => {
    if (isMobile.value) {
      // 移动端使用 window.innerHeight 可能受地址栏影响
      return Math.max(document.documentElement.clientHeight, window.innerHeight);
    }
    return window.innerHeight;
  });

  // 更新窗口尺寸
  function updateWindowSize() {
    windowWidth.value = window.innerWidth;
    windowHeight.value = window.innerHeight;
  }

  // 防抖处理
  let resizeTimer: number | null = null;
  function handleResize() {
    if (resizeTimer) {
      clearTimeout(resizeTimer);
    }
    resizeTimer = window.setTimeout(updateWindowSize, 100);
  }

  // 设置视口meta标签
  function setViewportMeta() {
    let viewport = document.querySelector('meta[name="viewport"]');
    if (!viewport) {
      viewport = document.createElement('meta');
      viewport.setAttribute('name', 'viewport');
      document.head.appendChild(viewport);
    }
    
    // 移动端优化的viewport设置
    const content = [
      'width=device-width',
      'initial-scale=1.0',
      'maximum-scale=1.0',
      'minimum-scale=1.0',
      'user-scalable=no',
      'viewport-fit=cover' // 支持iPhone X等设备的安全区域
    ].join(', ');
    
    viewport.setAttribute('content', content);
  }

  // 禁用双击缩放（移动端）
  function disableDoubleTapZoom() {
    if (isMobile.value) {
      let lastTouchEnd = 0;
      document.addEventListener('touchend', (event) => {
        const now = new Date().getTime();
        if (now - lastTouchEnd <= 300) {
          event.preventDefault();
        }
        lastTouchEnd = now;
      }, { passive: false });
    }
  }

  // 处理iOS Safari底部安全区域
  function handleIOSSafeArea() {
    if (isIOS.value) {
      document.documentElement.style.setProperty(
        '--ios-safe-area-bottom', 
        'env(safe-area-inset-bottom, 0px)'
      );
    }
  }

  // 处理移动端键盘弹起
  function handleMobileKeyboard() {
    if (isMobile.value) {
      const originalHeight = window.innerHeight;
      
      window.addEventListener('resize', () => {
        const currentHeight = window.innerHeight;
        const heightDiff = originalHeight - currentHeight;
        
        // 如果高度差超过150px，认为是键盘弹起
        if (heightDiff > 150) {
          document.body.classList.add('keyboard-open');
          document.documentElement.style.setProperty('--keyboard-height', `${heightDiff}px`);
        } else {
          document.body.classList.remove('keyboard-open');
          document.documentElement.style.removeProperty('--keyboard-height');
        }
      });
    }
  }

  // 获取适合的字体大小
  function getAdaptiveFontSize(baseSize: number): string {
    if (isMobile.value) {
      return `${Math.max(12, baseSize * 0.9)}px`;
    }
    return `${baseSize}px`;
  }

  // 获取适合的间距
  function getAdaptiveSpacing(baseSpacing: number): string {
    if (isMobile.value) {
      return `${Math.max(4, baseSpacing * 0.8)}px`;
    }
    return `${baseSpacing}px`;
  }

  // 获取适合的按钮高度
  function getAdaptiveButtonHeight(): string {
    if (isMobile.value) {
      return '44px'; // iOS推荐的最小触摸目标尺寸
    }
    return '32px';
  }

  // 获取适合的容器宽度
  function getAdaptiveContainerWidth(): string {
    if (isMobile.value) {
      return '100%';
    }
    if (isTablet.value) {
      return '90%';
    }
    return '1200px';
  }

  // 滚动到顶部（考虑移动端优化）
  function scrollToTop(smooth = true) {
    if (smooth && 'scrollBehavior' in document.documentElement.style) {
      window.scrollTo({
        top: 0,
        behavior: 'smooth'
      });
    } else {
      window.scrollTo(0, 0);
    }
  }

  // 生命周期
  onMounted(() => {
    updateWindowSize();
    setViewportMeta();
    disableDoubleTapZoom();
    handleIOSSafeArea();
    handleMobileKeyboard();
    
    window.addEventListener('resize', handleResize);
    window.addEventListener('orientationchange', handleResize);
  });

  onUnmounted(() => {
    if (resizeTimer) {
      clearTimeout(resizeTimer);
    }
    window.removeEventListener('resize', handleResize);
    window.removeEventListener('orientationchange', handleResize);
  });

  return {
    // 响应式状态
    windowWidth,
    windowHeight,
    
    // 计算属性
    isMobile,
    isTablet,
    isDesktop,
    deviceType,
    isLandscape,
    isPortrait,
    isTouchDevice,
    isIOS,
    isAndroid,
    isWechat,
    isAlipay,
    safeAreaInsets,
    viewportHeight,
    
    // 方法
    updateWindowSize,
    setViewportMeta,
    disableDoubleTapZoom,
    handleIOSSafeArea,
    handleMobileKeyboard,
    getAdaptiveFontSize,
    getAdaptiveSpacing,
    getAdaptiveButtonHeight,
    getAdaptiveContainerWidth,
    scrollToTop
  };
}
