import { useUserStore } from '@/store/index';

/**
 * Token管理工具类
 * 提供token的获取、设置、删除等功能
 */
export class TokenManager {
  private static userStore = useUserStore();

  /**
   * 获取访问令牌
   */
  static getAccessToken(): string | null {
    return this.userStore.getAccessToken;
  }

  /**
   * 获取刷新令牌
   */
  static getRefreshToken(): string | null {
    return this.userStore.getRefreshToken;
  }

  /**
   * 获取过期时间
   */
  static getExpiresTime(): string | null {
    return this.userStore.getExpiresTime;
  }

  /**
   * 设置完整的token信息
   */
  static setTokenInfo(tokenInfo: {
    accessToken?: string;
    access_token?: string;
    refreshToken?: string;
    refresh_token?: string;
    expiresTime?: string;
    expires_time?: string;
  }): void {
    this.userStore.setTokenInfo(tokenInfo);
  }

  /**
   * 设置访问令牌
   */
  static setAccessToken(accessToken: string): void {
    this.userStore.setAccessToken(accessToken);
  }

  /**
   * 设置刷新令牌
   */
  static setRefreshToken(refreshToken: string): void {
    this.userStore.setRefreshToken(refreshToken);
  }

  /**
   * 设置过期时间
   */
  static setExpiresTime(expiresTime: string): void {
    this.userStore.setExpiresTime(expiresTime);
  }

  /**
   * 删除所有token信息
   */
  static removeToken(): void {
    this.userStore.userOutLogin();
  }

  /**
   * 检查token是否存在
   */
  static hasToken(): boolean {
    return !!this.getAccessToken();
  }

  /**
   * 检查token是否即将过期（提前5分钟）
   */
  static isTokenExpiringSoon(): boolean {
    return this.userStore.isTokenExpiringSoon;
  }

  /**
   * 检查token是否已过期
   */
  static isTokenExpired(): boolean {
    return this.userStore.isTokenExpired;
  }

  /**
   * 格式化token为Bearer格式
   */
  static formatToken(token?: string): string | null {
    const accessToken = token || this.getAccessToken();
    return accessToken ? `Bearer ${accessToken}` : null;
  }

  /**
   * 检查是否需要刷新token
   */
  static needsRefresh(): boolean {
    return this.hasToken() && this.isTokenExpiringSoon();
  }

  /**
   * 检查token是否有效（存在且未过期）
   */
  static isTokenValid(): boolean {
    return this.hasToken() && !this.isTokenExpired();
  }
}

/**
 * 兼容旧版本的函数导出
 */
export const getAccessToken = () => TokenManager.getAccessToken();
export const getRefreshToken = () => TokenManager.getRefreshToken();
export const setToken = (tokenInfo: any) => TokenManager.setTokenInfo(tokenInfo);
export const removeToken = () => TokenManager.removeToken();
export const formatToken = (token?: string) => TokenManager.formatToken(token);
