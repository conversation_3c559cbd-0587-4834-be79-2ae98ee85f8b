/** @format */

import http from "@/http/sopHttp";

// 获取基本信息
export function getIsvPortal() {
  return http.get("/portal/isv/getIsvPortal");
}
// 获取产品申请列表
export function getProductList() {
  return http.get("/portal/product/getProductList");
}
// 获取产品申请列表
export function productApply(params: any) {
  return http.post("/portal/product/productApply", params);
}
// 获取产品申请历史
export function getApplyHistoryList(params: any) {
  return http.get("/portal/product/getProductRecord/" + params);
}
// 获取产品权限
export function getProductById(params: any) {
  return http.get("/portal/product/getProductById/" + params);
}
// 查询接口调用次数
export function getBlockchainList(params: any) {
  return http.get("/portal/blockchain/log/pageInfo", params);
}
// 修改密码
export function updatePassword(params: any) {
  return http.post("/portal/isv/updatePassword", params);
}

// 原有接口
// 获取用户信息
export async function getUserInfo() {
  return http.get("/user/info");
}
// 修改用户信息
export async function editUserInfo(params: any) {
  return http.post("/portal/isv/updatePassword", params);
}

export async function getAttentionRecordRecord(params: any) {
  return http.get("/attentionRecord/record", params);
}
// 获取浏览列表
export async function browseRecordRecord(params: any) {
  return http.get("/browseRecord/record", params);
}
// 删除单个历史浏览
export async function deleteBrowseRecord(params: number | string) {
  return http.delete("/browseRecord/" + params);
}
// 清空所有历史浏览
export async function deleteAllBrowseRecord() {
  return http.delete("/browseRecord/remove");
}
// 添加关注
interface AddAttention {
  worksPutId: string | number;
}
export async function addAttentionRecord(params: any) {
  return http.get("/attentionRecord/put", params);
}
// 删除关注
export async function deleteAttentionRecord(params: number) {
  return http.delete("/attentionRecord/" + params);
}
