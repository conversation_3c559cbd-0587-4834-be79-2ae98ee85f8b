# 支付API接口文档

## 概述

本文档描述了文创链支付系统的API接口，包括订单管理、支付处理、状态查询等功能。

## 基础信息

- **Base URL**: `https://api.wenchuangchain.com`
- **API版本**: `v1`
- **认证方式**: <PERSON><PERSON>
- **数据格式**: JSON
- **字符编码**: UTF-8

## 通用响应格式

```json
{
  "code": 200,
  "success": true,
  "message": "操作成功",
  "data": {},
  "timestamp": 1640995200000
}
```

### 响应状态码

| 状态码 | 说明 |
|--------|------|
| 200 | 请求成功 |
| 400 | 请求参数错误 |
| 401 | 未授权访问 |
| 403 | 权限不足 |
| 404 | 资源不存在 |
| 500 | 服务器内部错误 |

## 订单管理接口

### 获取订单信息

**接口地址**: `GET /payment/order/{orderId}`

**请求参数**:
- `orderId` (string, required): 订单ID

**响应示例**:
```json
{
  "code": 200,
  "success": true,
  "data": {
    "orderId": "ORDER_20240101_001",
    "orderType": "package",
    "productId": "PROD_001",
    "productName": "专业版套餐",
    "productDescription": "包含1000次存证服务",
    "productImage": "https://example.com/image.jpg",
    "originalAmount": 99.00,
    "discountAmount": 9.00,
    "finalAmount": 90.00,
    "createTime": "2024-01-01T10:00:00Z",
    "expireTime": "2024-01-01T11:00:00Z",
    "userId": "USER_001",
    "status": "created"
  }
}
```

## 支付方式接口

### 获取支付方式列表

**接口地址**: `GET /payment/methods`

**请求参数**:
- `orderId` (string, required): 订单ID
- `amount` (number, required): 订单金额

**响应示例**:
```json
{
  "code": 200,
  "success": true,
  "data": {
    "wechatEnabled": true,
    "alipayEnabled": true,
    "userAssets": {
      "balance": 50.00,
      "points": 1000,
      "pointsRate": 100,
      "availableCoupons": [
        {
          "couponId": "COUPON_001",
          "name": "新用户优惠券",
          "type": "amount",
          "value": 10.00,
          "minOrderAmount": 50.00,
          "maxDiscountAmount": 10.00,
          "expireTime": "2024-12-31T23:59:59Z",
          "description": "新用户专享10元优惠券",
          "applicable": true
        }
      ]
    },
    "paymentMethods": [
      {
        "type": "wechat",
        "name": "微信支付",
        "icon": "/icons/wechat.png",
        "enabled": true,
        "description": "使用微信扫码支付",
        "minAmount": 0.01,
        "maxAmount": 50000.00
      },
      {
        "type": "alipay",
        "name": "支付宝",
        "icon": "/icons/alipay.png",
        "enabled": true,
        "description": "使用支付宝扫码支付",
        "minAmount": 0.01,
        "maxAmount": 50000.00
      },
      {
        "type": "balance",
        "name": "余额支付",
        "icon": "/icons/balance.png",
        "enabled": true,
        "amount": 50.00,
        "description": "使用账户余额支付"
      },
      {
        "type": "points",
        "name": "积分支付",
        "icon": "/icons/points.png",
        "enabled": true,
        "amount": 1000,
        "description": "使用积分抵扣现金"
      }
    ]
  }
}
```

### 获取用户资产信息

**接口地址**: `GET /payment/user/assets`

**请求参数**:
- `userId` (string, optional): 用户ID，默认当前用户

**响应示例**:
```json
{
  "code": 200,
  "success": true,
  "data": {
    "balance": 150.50,
    "points": 2500,
    "pointsRate": 100,
    "availableCoupons": [...]
  }
}
```

### 获取可用优惠券

**接口地址**: `GET /payment/coupons/available`

**请求参数**:
- `orderId` (string, required): 订单ID
- `amount` (number, required): 订单金额
- `productType` (string, optional): 产品类型

**响应示例**:
```json
{
  "code": 200,
  "success": true,
  "data": [
    {
      "couponId": "COUPON_001",
      "name": "新用户优惠券",
      "type": "amount",
      "value": 10.00,
      "minOrderAmount": 50.00,
      "maxDiscountAmount": 10.00,
      "expireTime": "2024-12-31T23:59:59Z",
      "description": "新用户专享10元优惠券",
      "applicable": true
    }
  ]
}
```

## 支付处理接口

### 计算支付金额

**接口地址**: `POST /payment/calculate`

**请求参数**:
```json
{
  "orderId": "ORDER_20240101_001",
  "originalAmount": 99.00,
  "couponIds": ["COUPON_001"],
  "pointsUsed": 500,
  "balanceUsed": 20.00
}
```

**响应示例**:
```json
{
  "code": 200,
  "success": true,
  "data": {
    "originalAmount": 99.00,
    "couponDiscount": 10.00,
    "pointsDiscount": 5.00,
    "balanceUsed": 20.00,
    "finalAmount": 64.00,
    "savings": 35.00
  }
}
```

### 创建支付订单

**接口地址**: `POST /payment/create`

**请求参数**:
```json
{
  "orderId": "ORDER_20240101_001",
  "paymentMethods": [
    {
      "type": "wechat",
      "amount": 44.00
    },
    {
      "type": "balance",
      "amount": 20.00
    },
    {
      "type": "points",
      "amount": 500,
      "couponId": "COUPON_001"
    }
  ],
  "totalAmount": 64.00,
  "clientInfo": {
    "userAgent": "Mozilla/5.0...",
    "ip": "***********",
    "platform": "web"
  }
}
```

**响应示例**:
```json
{
  "code": 200,
  "success": true,
  "data": {
    "success": true,
    "paymentId": "PAY_20240101_001",
    "orderId": "ORDER_20240101_001",
    "qrCode": "https://qr.example.com/pay/xxx",
    "redirectUrl": "https://pay.example.com/redirect/xxx",
    "message": "支付订单创建成功",
    "paymentStatus": "processing",
    "transactionId": "TXN_001",
    "paidAmount": 64.00,
    "paymentTime": "2024-01-01T10:30:00Z"
  }
}
```

### 查询支付状态

**接口地址**: `GET /payment/status/{paymentId}`

**请求参数**:
- `paymentId` (string, required): 支付ID
- `orderId` (string, optional): 订单ID

**响应示例**:
```json
{
  "code": 200,
  "success": true,
  "data": {
    "paymentId": "PAY_20240101_001",
    "orderId": "ORDER_20240101_001",
    "status": "success",
    "paidAmount": 64.00,
    "paymentTime": "2024-01-01T10:35:00Z",
    "transactionId": "TXN_001",
    "refundAmount": 0.00
  }
}
```

### 取消支付

**接口地址**: `POST /payment/cancel`

**请求参数**:
```json
{
  "paymentId": "PAY_20240101_001",
  "orderId": "ORDER_20240101_001",
  "reason": "用户主动取消"
}
```

**响应示例**:
```json
{
  "code": 200,
  "success": true,
  "data": {
    "cancelled": true,
    "refundAmount": 0.00,
    "message": "支付已取消"
  }
}
```

## 第三方支付接口

### 微信支付

#### 创建微信支付订单

**接口地址**: `POST /payment/wechat/create`

**请求参数**:
```json
{
  "orderId": "ORDER_20240101_001",
  "amount": 44.00,
  "description": "专业版套餐",
  "notifyUrl": "https://api.example.com/payment/wechat/notify"
}
```

#### 查询微信支付订单

**接口地址**: `GET /payment/wechat/query`

**请求参数**:
- `orderId` (string, required): 订单ID
- `transactionId` (string, optional): 微信交易ID

### 支付宝

#### 创建支付宝订单

**接口地址**: `POST /payment/alipay/create`

**请求参数**:
```json
{
  "orderId": "ORDER_20240101_001",
  "amount": 44.00,
  "subject": "专业版套餐",
  "body": "包含1000次存证服务",
  "notifyUrl": "https://api.example.com/payment/alipay/notify"
}
```

## 账户支付接口

### 积分支付

#### 积分支付

**接口地址**: `POST /payment/points/pay`

**请求参数**:
```json
{
  "orderId": "ORDER_20240101_001",
  "points": 500,
  "amount": 5.00
}
```

#### 获取积分兑换比例

**接口地址**: `GET /payment/points/rate`

**响应示例**:
```json
{
  "code": 200,
  "success": true,
  "data": {
    "rate": 100,
    "description": "100积分 = 1元"
  }
}
```

### 余额支付

#### 余额支付

**接口地址**: `POST /payment/balance/pay`

**请求参数**:
```json
{
  "orderId": "ORDER_20240101_001",
  "amount": 20.00,
  "password": "encrypted_password"
}
```

#### 余额充值

**接口地址**: `POST /payment/balance/recharge`

**请求参数**:
```json
{
  "amount": 100.00,
  "paymentMethod": "wechat"
}
```

### 优惠券

#### 使用优惠券

**接口地址**: `POST /payment/coupon/use`

**请求参数**:
```json
{
  "orderId": "ORDER_20240101_001",
  "couponId": "COUPON_001",
  "amount": 10.00
}
```

#### 验证优惠券

**接口地址**: `POST /payment/coupon/validate`

**请求参数**:
```json
{
  "couponId": "COUPON_001",
  "orderId": "ORDER_20240101_001",
  "amount": 99.00
}
```

## 记录查询接口

### 支付记录

**接口地址**: `GET /payment/records`

**请求参数**:
- `pageNum` (number, optional): 页码，默认1
- `pageSize` (number, optional): 页大小，默认10
- `startTime` (string, optional): 开始时间
- `endTime` (string, optional): 结束时间
- `status` (string, optional): 支付状态
- `orderType` (string, optional): 订单类型

### 积分记录

**接口地址**: `GET /payment/points/records`

### 余额记录

**接口地址**: `GET /payment/balance/records`

### 优惠券记录

**接口地址**: `GET /payment/coupons/records`

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| 40001 | 订单不存在 |
| 40002 | 订单已过期 |
| 40003 | 订单状态异常 |
| 40004 | 支付方式不可用 |
| 40005 | 余额不足 |
| 40006 | 积分不足 |
| 40007 | 优惠券不可用 |
| 40008 | 支付金额异常 |
| 40009 | 支付已存在 |
| 40010 | 支付不存在 |
| 50001 | 第三方支付服务异常 |
| 50002 | 支付处理超时 |

## 安全说明

### 签名验证

所有支付相关接口都需要进行签名验证，签名算法：

```
signature = MD5(params + timestamp + secret_key)
```

### 回调通知

支付成功后，系统会向指定的回调地址发送通知：

```json
{
  "paymentId": "PAY_20240101_001",
  "orderId": "ORDER_20240101_001",
  "status": "success",
  "amount": 64.00,
  "transactionId": "TXN_001",
  "paymentTime": "2024-01-01T10:35:00Z",
  "signature": "abc123..."
}
```

### 数据加密

敏感数据（如支付密码）需要使用RSA公钥加密传输。

## 测试环境

### 测试地址

- **测试环境**: `https://test-api.wenchuangchain.com`
- **沙箱环境**: `https://sandbox-api.wenchuangchain.com`

### 测试数据

测试环境提供以下测试数据：

- 测试订单ID: `TEST_ORDER_001`
- 测试用户ID: `TEST_USER_001`
- 测试优惠券: `TEST_COUPON_001`

### 模拟支付

测试环境支持模拟支付，无需真实的第三方支付：

- 微信支付: 使用测试二维码
- 支付宝: 使用测试跳转链接
- 余额支付: 使用测试余额
- 积分支付: 使用测试积分
