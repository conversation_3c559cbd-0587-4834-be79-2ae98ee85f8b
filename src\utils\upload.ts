/** @format */

import * as FileApi from '@/http/api/global/proIndex';
import CryptoJS from 'crypto-js';
import axios from 'axios';
import { Message } from '@arco-design/web-vue';

/**
 * 上传文件类型
 */
export interface UploadFile {
  uid: string;
  name: string;
  file?: File;
  url?: string;
  status?: 'init' | 'uploading' | 'done' | 'error';
  percent?: number;
  response?: any;
}

/**
 * 上传请求选项
 */
export interface UploadRequestOptions {
  file: File;
  filename?: string;
  onProgress?: (percent: number) => void;
  onSuccess?: (response: any) => void;
  onError?: (error: any) => void;
}

/**
 * 上传类型枚举
 */
enum UPLOAD_TYPE {
  // 客户端直接上传（只支持S3服务）
  CLIENT = 'client',
  // 客户端发送到后端上传
  SERVER = 'server',
}

/**
 * 获得上传 URL
 */
export const getUploadUrl = (): string => {
  return import.meta.env.VITE_PRO_API_TARGET + '/app-api/infra/file/upload';
};

/**
 * 生成文件名称（使用算法SHA256）
 * @param file 要上传的文件
 */
async function generateFileName(file: File): Promise<string> {
  try {
    // 读取文件内容
    const data = await file.arrayBuffer();
    const wordArray = CryptoJS.lib.WordArray.create(data);
    // 计算SHA256
    const sha256 = CryptoJS.SHA256(wordArray).toString();
    // 拼接后缀
    const ext = file.name.substring(file.name.lastIndexOf('.'));
    return `${sha256}${ext}`;
  } catch (error) {
    console.error('生成文件名失败:', error);
    // 如果生成失败，使用时间戳 + 随机数作为备用方案
    const timestamp = Date.now();
    const random = Math.floor(Math.random() * 1000);
    const ext = file.name.substring(file.name.lastIndexOf('.'));
    return `${timestamp}_${random}${ext}`;
  }
}

/**
 * 创建文件信息
 * @param vo 文件预签名信息
 * @param name 文件名称
 * @param file 文件
 */
function createFile(vo: FileApi.FilePresignedUrlRespVO, name: string, file: File) {
  const fileVo: FileApi.FileCreateReqVO = {
    path: name,
    name: file.name,
    type: file.type,
    size: file.size,
  };
  FileApi.createFile(fileVo);
  return fileVo;
}

/**
 * 文件上传 Hook
 */
export const useUpload = () => {
  // 后端上传地址
  const uploadUrl = getUploadUrl();
  // 是否使用前端直连上传
  const isClientUpload = UPLOAD_TYPE.CLIENT === import.meta.env.VITE_UPLOAD_TYPE;

  /**
   * 重写上传方法
   */
  const httpRequest = async (options: UploadRequestOptions) => {
    console.log('开始文件上传，模式:', isClientUpload ? 'client' : 'server');
    console.log('上传文件信息:', {
      name: options.file.name,
      size: options.file.size,
      type: options.file.type,
    });

    try {
      // 模式一：前端直连上传
      if (isClientUpload) {
        console.log('使用前端直连上传模式');
        // 1.1 生成文件名称
        const fileName = await generateFileName(options.file);
        console.log('生成的文件名:', fileName);

        // 1.2 获取文件预签名地址
        const presignedInfo = await FileApi.getFilePresignedUrl(fileName);
        console.log('获取预签名URL成功:', presignedInfo);

        // 1.3 上传文件（不能使用 FormData 上传，Minio 不支持）
        return axios
          .put(presignedInfo.data.uploadUrl, options.file, {
            headers: {
              'Content-Type': options.file.type,
            },
            onUploadProgress: progressEvent => {
              if (options.onProgress && progressEvent.total) {
                const percent = Math.round((progressEvent.loaded * 100) / progressEvent.total);
                console.log('上传进度:', percent + '%');
                options.onProgress(percent);
              }
            },
          })
          .then(() => {
            console.log('文件上传成功');
            // 1.4. 记录文件信息到后端（异步）
            createFile(presignedInfo.data, fileName, options.file);

            // 通知成功，数据格式保持与后端上传的返回结果一致
            const response = { data: presignedInfo.data.url };
            options.onSuccess?.(response);
            return response;
          })
          .catch(error => {
            console.error('文件上传失败:', error);
            options.onError?.(error);
            throw error;
          });
      } else {
        // 模式二：后端上传
        console.log('使用后端代理上传模式');
        return new Promise((resolve, reject) => {
          FileApi.uploadFile(options.file)
            .then(res => {
              console.log('后端上传响应:', res);
              if (res.code === 0) {
                console.log('文件上传成功');
                options.onSuccess?.(res);
                resolve(res);
              } else {
                console.error('后端上传失败:', res.msg);
                const error = new Error(res.msg || '上传失败');
                options.onError?.(error);
                reject(error);
              }
            })
            .catch(error => {
              console.error('文件上传失败:', error);
              options.onError?.(error);
              reject(error);
            });
        });
      }
    } catch (error) {
      console.error('文件上传异常:', error);
      options.onError?.(error);
      throw error;
    }
  };

  /**
   * Arco Design Upload 组件的自定义上传方法
   */
  const customRequest = (option: any) => {
    const { file, onProgress, onSuccess, onError } = option;

    return httpRequest({
      file,
      onProgress: percent => {
        onProgress({ percent });
      },
      onSuccess: response => {
        onSuccess(response);
      },
      onError: error => {
        Message.error(`文件上传失败: ${error.message || '未知错误'}`);
        onError(error);
      },
    });
  };

  return {
    uploadUrl,
    httpRequest,
    customRequest,
  };
};

/**
 * 文件大小格式化
 * @param size 文件大小（字节）
 */
export const formatFileSize = (size: number): string => {
  if (size === 0) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(size) / Math.log(k));
  return parseFloat((size / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

/**
 * 验证文件类型
 * @param file 文件对象
 * @param acceptTypes 允许的文件类型数组
 */
export const validateFileType = (file: File, acceptTypes: string[]): boolean => {
  if (!acceptTypes || acceptTypes.length === 0) return true;

  const fileExtension = file.name.substring(file.name.lastIndexOf('.')).toLowerCase();
  const mimeType = file.type.toLowerCase();

  return acceptTypes.some(type => {
    if (type.startsWith('.')) {
      return fileExtension === type.toLowerCase();
    } else if (type.includes('/')) {
      return mimeType === type.toLowerCase();
    }
    return false;
  });
};

/**
 * 验证文件大小
 * @param file 文件对象
 * @param maxSize 最大文件大小（字节）
 */
export const validateFileSize = (file: File, maxSize: number): boolean => {
  return file.size <= maxSize;
};
