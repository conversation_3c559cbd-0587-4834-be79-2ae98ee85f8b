# 401错误处理机制

## 概述

本项目已实现完整的401未授权错误处理机制，当前端访问proHttp或sopHttp中的接口时，如果后端返回401错误码，系统会自动：

1. 显示用户友好的提示消息
2. 清除用户登录状态
3. 自动跳转到登录页面

## 实现细节

### 1. HTTP状态码401处理

在 `src/http/axios.ts` 的 `handleError` 函数中实现：

```typescript
case 401:
  // 401未授权错误：清除登录状态并跳转到登录页
  error.message = '登录已过期，请重新登录';
  Message.error(error.message);
  userStore.userOutLogin();
  router.replace('/login');
  return; // 直接返回，避免重复显示错误消息
```

### 2. 业务状态码9处理

在响应拦截器中处理后端自定义的登录过期状态码：

```typescript
if (response.data.code === 9) {
  // 业务状态码9：登录过期，与401错误保持一致的处理
  Message.error('登录已过期，请重新登录');
  userStore.userOutLogin();
  router.replace('/login');
  return Promise.reject(response.data);
}
```

## 适用范围

此错误处理机制适用于：

- **proHttp**: PRO后端API调用
- **sopHttp**: SOP后端API调用
- 所有使用 `createAxiosInstance` 创建的HTTP实例

## 用户体验

当发生401错误时，用户会看到：

1. **错误提示**: "登录已过期，请重新登录"
2. **自动清理**: 用户token和登录信息被清除
3. **自动跳转**: 页面自动跳转到 `/login` 登录页面

## 技术特点

- **避免重复提示**: 优化了错误处理逻辑，避免显示多个错误消息
- **统一处理**: HTTP状态码401和业务状态码9使用相同的处理逻辑
- **自动化**: 无需手动处理，所有401错误都会被自动拦截和处理

## 测试

可以使用测试页面 `src/test/401-error-test.vue` 来验证401错误处理功能：

1. 访问测试页面
2. 点击相应的测试按钮
3. 观察错误提示和页面跳转行为

## 相关文件

- `src/http/axios.ts` - 核心错误处理逻辑
- `src/http/proHttp.ts` - PRO API HTTP服务
- `src/http/sopHttp.ts` - SOP API HTTP服务
- `src/store/modules/user/index.js` - 用户状态管理
- `src/router/index.ts` - 路由配置

## 注意事项

1. 确保登录页面路由路径为 `/login`
2. 用户store的 `userOutLogin()` 方法会清除所有用户相关数据
3. 401错误处理会立即中断当前请求，不会继续执行后续逻辑
