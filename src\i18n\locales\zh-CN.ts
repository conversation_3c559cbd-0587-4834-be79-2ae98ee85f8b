export default {
  common: {
    confirm: '确认',
    cancel: '取消',
    submit: '提交',
    reset: '重置',
    search: '搜索',
    loading: '加载中...',
    success: '操作成功',
    failed: '操作失败',
    noData: '暂无数据',
    more: '更多',
    back: '返回',
    home: '首页',
  },
  header: {
    home: '首页',
    products: '产品大全',
    api: 'API开放文档',
    login: '登录',
    register: '注册',
    userCenter: '用户中心',
    logout: '退出登录',
  },
  footer: {
    copyright: '© 2023 成都九天星空科技有限公司 版权所有',
    icp: '蜀ICP备xxxxxxxx号',
  },
  login: {
    title: '用户登录',
    username: '用户名',
    password: '密码',
    remember: '记住密码',
    forget: '忘记密码',
    register: '注册账号',
    loginBtn: '登录',
  },
  register: {
    title: '用户注册',
    username: '用户名',
    password: '密码',
    confirmPassword: '确认密码',
    email: '邮箱',
    phone: '手机号',
    code: '验证码',
    agreement: '我已阅读并同意',
    terms: '服务条款',
    privacy: '隐私政策',
    registerBtn: '注册',
  },
  user: {
    userInfo: '用户信息',
    account: '账号信息',
    products: '我的产品',
    package: '套餐购买',
    usage: '用量查询',
    follow: '关注记录',
    browse: '浏览记录',
    trade: '交易记录',
    asset: '资产管理',
    evidence: '存证列表',
    software: '软件登记列表',
  },
  product: {
    all: '全部产品',
    popular: '热门产品',
    latest: '最新产品',
    free: '免费产品',
    detail: '产品详情',
    price: '价格',
    buy: '购买',
    trial: '免费试用',
    feature: '功能特点',
    scene: '应用场景',
    api: '接口文档',
  },
  api: {
    list: 'API列表',
    detail: 'API详情',
    name: '接口名称',
    desc: '接口描述',
    method: '请求方式',
    url: '请求地址',
    params: '请求参数',
    response: '返回参数',
    example: '示例代码',
    debug: '在线调试',
  },
};
