/** @format */

import { createAxiosInstance, HttpService, ResType } from './axios';
import { useUserStore } from '@/store/index';
import { Message } from '@arco-design/web-vue';

// 获取环境变量中的SOP API地址
const API_URL = import.meta.env.VITE_SOP_API_TARGET || '/api';

// 创建SOP API的HTTP实例
const sopInstance = createAxiosInstance({
  baseURL: API_URL,
  timeout: 10000,
  customHeaders: {
    'Content-Type': 'application/json',
    // SOP API不需要tenant-id
  },
});

// 创建SOP HTTP服务
const sopHttpService = new HttpService(sopInstance);

// 导出HTTP服务实例
export default sopHttpService;
