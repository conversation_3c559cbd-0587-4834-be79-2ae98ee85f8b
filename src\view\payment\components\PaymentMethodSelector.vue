<template>
  <div class="payment-method-selector-container">
    <a-card class="payment-method-card" :bordered="false">
      <template #title>
        <div class="payment-method-header">
          <icon-user class="payment-method-icon" />
          <span class="payment-method-title">选择支付方式</span>
        </div>
      </template>

      <div v-if="loading" class="loading-container">
        <a-spin :size="24" />
        <p class="loading-text">正在加载支付方式...</p>
      </div>

      <div v-else class="payment-method-content">
        <!-- 在线支付方式 -->
        <div class="payment-section">
          <h4 class="section-title">在线支付</h4>
          <div class="payment-options">
            <div
              v-for="method in onlinePaymentMethods"
              :key="method.type"
              class="payment-option"
              :class="{
                selected: selectedOnlineMethod === method.type,
                disabled: !method.enabled,
              }"
              @click="handleOnlinePaymentSelect(method.type)"
            >
              <div class="payment-option-inner">
                <div class="payment-icon-wrapper">
                  <img
                    v-if="method.type === 'wechat'"
                    src="/icons/wechat-pay.svg"
                    alt="微信支付"
                    class="payment-logo"
                  />
                  <img
                    v-else-if="method.type === 'alipay'"
                    src="/icons/alipay.svg"
                    alt="支付宝"
                    class="payment-logo"
                  />
                </div>
                <div class="payment-info">
                  <div class="payment-name">{{ method.name }}</div>
                  <div class="payment-description">{{ method.description }}</div>
                </div>
                <div class="payment-radio">
                  <a-radio
                    :model-value="selectedOnlineMethod === method.type"
                    :disabled="!method.enabled"
                    @click.stop
                  />
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 账户支付方式 -->
        <div class="payment-section">
          <h4 class="section-title">账户支付</h4>

          <!-- 余额支付 -->
          <div v-if="userAssets && userAssets.balance > 0" class="payment-option account-payment">
            <div class="payment-option-inner">
              <div class="payment-icon-wrapper">
                <icon-user class="account-icon" />
              </div>
              <div class="payment-info">
                <div class="payment-name">钱包余额</div>
                <div class="payment-description">
                  可用余额：¥{{ formatAmount(userAssets.balance) }}
                </div>
              </div>
              <div class="payment-amount-input">
                <a-input-number
                  v-model="balanceAmount"
                  :min="0"
                  :max="Math.min(userAssets.balance, orderAmount)"
                  :precision="2"
                  placeholder="使用金额"
                  size="small"
                  style="width: 120px"
                  @change="handleBalanceAmountChange"
                />
              </div>
            </div>
          </div>

          <!-- 积分支付 -->
          <div v-if="userAssets && userAssets.points > 0" class="payment-option account-payment">
            <div class="payment-option-inner">
              <div class="payment-icon-wrapper">
                <icon-star class="account-icon" />
              </div>
              <div class="payment-info">
                <div class="payment-name">积分支付</div>
                <div class="payment-description">
                  可用积分：{{ userAssets.points }}分 ({{ userAssets.pointsRate }}积分=1元)
                </div>
              </div>
              <div class="payment-amount-input">
                <a-input-number
                  v-model="pointsAmount"
                  :min="0"
                  :max="Math.min(userAssets.points, orderAmount * userAssets.pointsRate)"
                  :precision="0"
                  placeholder="使用积分"
                  size="small"
                  style="width: 120px"
                  @change="handlePointsAmountChange"
                />
              </div>
            </div>
          </div>
        </div>

        <!-- 支付方式为空提示 -->
        <div v-if="!hasAnyPaymentMethod" class="empty-payment">
          <a-empty description="暂无可用的支付方式">
            <template #image>
              <icon-user style="font-size: 64px; color: #c9cdd4" />
            </template>
          </a-empty>
        </div>
      </div>
    </a-card>
  </div>
</template>

<script setup lang="ts">
  import { computed, ref, watch } from 'vue';
  import { IconUser, IconStar } from '@arco-design/web-vue/es/icon';
  import type { PaymentMethod, UserAssets } from '@/http/api/payment/types';

  // Props
  interface Props {
    paymentMethods: PaymentMethod[];
    userAssets?: UserAssets;
    orderAmount: number;
    loading?: boolean;
  }

  const props = withDefaults(defineProps<Props>(), {
    loading: false,
  });

  // Emits
  const emit = defineEmits(['payment-change', 'amount-change']);

  // 本地状态
  const selectedOnlineMethod = ref<string>('');
  const balanceAmount = ref(0);
  const pointsAmount = ref(0);

  // 计算属性
  const onlinePaymentMethods = computed(() =>
    props.paymentMethods.filter(m => m.type === 'wechat' || m.type === 'alipay')
  );

  const hasOnlinePayment = computed(() => onlinePaymentMethods.value.length > 0);
  const hasAccountPayment = computed(
    () => (props.userAssets?.balance || 0) > 0 || (props.userAssets?.points || 0) > 0
  );
  const hasAnyPaymentMethod = computed(() => hasOnlinePayment.value || hasAccountPayment.value);

  // 方法
  function handleOnlinePaymentSelect(methodType: string) {
    if (selectedOnlineMethod.value === methodType) {
      selectedOnlineMethod.value = '';
    } else {
      selectedOnlineMethod.value = methodType;
    }
    emitPaymentChange();
  }

  function handleBalanceAmountChange(amount: number | undefined) {
    balanceAmount.value = amount || 0;
    emitPaymentChange();
  }

  function handlePointsAmountChange(points: number | undefined) {
    pointsAmount.value = points || 0;
    emitPaymentChange();
  }

  function emitPaymentChange() {
    const selectedMethods: PaymentMethod[] = [];

    // 添加在线支付方式
    if (selectedOnlineMethod.value) {
      const method = onlinePaymentMethods.value.find(m => m.type === selectedOnlineMethod.value);
      if (method) {
        selectedMethods.push({
          ...method,
          amount: calculateOnlinePaymentAmount(),
        });
      }
    }

    // 添加余额支付
    if (balanceAmount.value > 0) {
      selectedMethods.push({
        type: 'balance',
        name: '钱包余额',
        icon: 'wallet',
        enabled: true,
        amount: balanceAmount.value,
      });
    }

    // 添加积分支付
    if (pointsAmount.value > 0) {
      selectedMethods.push({
        type: 'points',
        name: '积分支付',
        icon: 'star',
        enabled: true,
        amount: pointsAmount.value,
      });
    }

    emit('payment-change', selectedMethods);
  }

  function calculateOnlinePaymentAmount(): number {
    const totalAccountPayment =
      balanceAmount.value + pointsAmount.value / (props.userAssets?.pointsRate || 100);
    return Math.max(0, props.orderAmount - totalAccountPayment);
  }

  function formatAmount(amount: number): string {
    return amount.toFixed(2);
  }

  // 监听订单金额变化，自动调整支付金额
  watch(
    () => props.orderAmount,
    () => {
      // 重新计算支付金额分配
      emitPaymentChange();
    }
  );
</script>

<style scoped lang="less">
  .payment-method-selector-container {
    margin-bottom: 24px;

    .payment-method-card {
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

      :deep(.arco-card-header) {
        border-bottom: 1px solid #f0f0f0;
        padding: 16px 20px;
      }

      :deep(.arco-card-body) {
        padding: 20px;
      }
    }

    .payment-method-header {
      display: flex;
      align-items: center;
      gap: 8px;

      .payment-method-icon {
        font-size: 18px;
        color: #1890ff;
      }

      .payment-method-title {
        font-size: 16px;
        font-weight: 600;
        color: #262626;
      }
    }

    .loading-container {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 40px 20px;

      .loading-text {
        margin-top: 12px;
        color: #8c8c8c;
        font-size: 14px;
      }
    }

    .payment-method-content {
      .payment-section {
        margin-bottom: 24px;

        &:last-child {
          margin-bottom: 0;
        }

        .section-title {
          font-size: 14px;
          font-weight: 600;
          color: #262626;
          margin: 0 0 16px 0;
        }

        .payment-options {
          display: flex;
          flex-direction: column;
          gap: 12px;
        }

        .payment-option {
          border: 1px solid #f0f0f0;
          border-radius: 8px;
          cursor: pointer;
          transition: all 0.3s ease;

          &:hover:not(.disabled) {
            border-color: #1890ff;
            box-shadow: 0 2px 8px rgba(24, 144, 255, 0.15);
          }

          &.selected {
            border-color: #1890ff;
            background: #f6ffed;
          }

          &.disabled {
            cursor: not-allowed;
            opacity: 0.6;
            background: #fafafa;
          }

          &.account-payment {
            cursor: default;

            &:hover {
              border-color: #f0f0f0;
              box-shadow: none;
            }
          }

          .payment-option-inner {
            display: flex;
            align-items: center;
            padding: 16px;
            gap: 16px;

            .payment-icon-wrapper {
              width: 40px;
              height: 40px;
              display: flex;
              align-items: center;
              justify-content: center;

              .payment-logo {
                width: 32px;
                height: 32px;
                object-fit: contain;
              }

              .account-icon {
                font-size: 24px;
                color: #1890ff;
              }
            }

            .payment-info {
              flex: 1;

              .payment-name {
                font-size: 16px;
                font-weight: 600;
                color: #262626;
                margin-bottom: 4px;
              }

              .payment-description {
                font-size: 14px;
                color: #8c8c8c;
              }
            }

            .payment-radio {
              margin-left: auto;
            }

            .payment-amount-input {
              margin-left: auto;
            }
          }
        }
      }
    }

    .empty-payment {
      display: flex;
      justify-content: center;
      align-items: center;
      padding: 60px 20px;
    }
  }

  // 响应式设计
  @media (max-width: 768px) {
    .payment-method-selector-container {
      .payment-method-content {
        .payment-section {
          .payment-option {
            .payment-option-inner {
              flex-direction: column;
              align-items: flex-start;
              gap: 12px;

              .payment-radio,
              .payment-amount-input {
                margin-left: 0;
                align-self: flex-end;
              }
            }
          }
        }
      }
    }
  }
</style>
