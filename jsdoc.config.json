{"tags": {"allowUnknownTags": true, "dictionaries": ["jsdoc", "closure"]}, "source": {"include": ["src"], "includePattern": "\\.(js|ts|vue)$", "excludePattern": "(node_modules/|dist/|__tests__/)"}, "plugins": ["plugins/markdown", "node_modules/jsdoc-vuejs"], "templates": {"cleverLinks": false, "monospaceLinks": false, "default": {"outputSourceFiles": true}}, "opts": {"destination": "./docs", "recurse": true, "readme": "README.md"}}