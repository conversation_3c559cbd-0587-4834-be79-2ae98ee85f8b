<template>
  <div class="page-container">
    <!-- Hero Banner Section -->
    <div class="hero-section">
      <div class="content-wrapper">
        <h1 class="hero-title">区块链存证服务</h1>
        <p class="hero-description">
          基于区块链技术的数字资产存证服务，为您的数字内容提供不可篡改的时间戳证明和版权保护。通过分布式账本技术，确保存证数据的真实性、完整性和可追溯性，为数字版权保护提供强有力的技术支撑。
        </p>
        <div class="hero-buttons">
          <a-button type="primary" size="large" class="hero-button" @click="goToSubmit">
            <span>立即存证</span>
            <icon-arrow-right />
          </a-button>
          <a-button type="outline" size="large" class="hero-button" @click="goToQuery">
            <span>查询验证</span>
            <icon-search />
          </a-button>
        </div>
      </div>
    </div>

    <!-- Statistics Section -->
    <div class="section statistics">
      <div class="section-title">
        <h2>平台数据</h2>
      </div>
      <div class="stats-container">
        <a-row :gutter="24">
          <a-col :span="6">
            <div class="stat-card">
              <div class="stat-number">{{ formatNumber(statistics.totalEvidence) }}</div>
              <div class="stat-label">累计存证</div>
            </div>
          </a-col>
          <a-col :span="6">
            <div class="stat-card">
              <div class="stat-number">{{ formatNumber(statistics.todayEvidence) }}</div>
              <div class="stat-label">今日存证</div>
            </div>
          </a-col>
          <a-col :span="6">
            <div class="stat-card">
              <div class="stat-number">{{ formatNumber(statistics.totalUsers) }}</div>
              <div class="stat-label">服务用户</div>
            </div>
          </a-col>
          <a-col :span="6">
            <div class="stat-card">
              <div class="stat-number">{{ statistics.blockHeight }}</div>
              <div class="stat-label">当前区块高度</div>
            </div>
          </a-col>
        </a-row>
      </div>
    </div>

    <!-- Service Features Section -->
    <div class="section service-features">
      <div class="section-title">
        <h2>服务特色</h2>
      </div>
      <div class="features-container">
        <a-row :gutter="24">
          <a-col :span="8">
            <div class="feature-card">
              <div class="feature-icon">
                <icon-safe />
              </div>
              <h3>不可篡改</h3>
              <p>基于区块链分布式账本技术，存证数据一旦上链即不可篡改，确保证据的真实性和完整性</p>
            </div>
          </a-col>
          <a-col :span="8">
            <div class="feature-card">
              <div class="feature-icon">
                <icon-clock-circle />
              </div>
              <h3>时间戳证明</h3>
              <p>提供精确的时间戳证明，记录数据产生的确切时间，为版权保护提供有力支撑</p>
            </div>
          </a-col>
          <a-col :span="8">
            <div class="feature-card">
              <div class="feature-icon">
                <icon-check-circle />
              </div>
              <h3>法律效力</h3>
              <p>符合国家相关法律法规，存证记录具有法律效力，可作为司法诉讼的有效证据</p>
            </div>
          </a-col>
        </a-row>
      </div>
    </div>

    <!-- Application Scenarios Section -->
    <div class="section application-scenarios">
      <div class="section-title">
        <h2>应用场景</h2>
      </div>
      <div class="scenarios-container">
        <a-row :gutter="24">
          <a-col :span="12">
            <div class="scenario-card">
              <div class="scenario-icon">
                <icon-file />
              </div>
              <div class="scenario-content">
                <h3>数字版权保护</h3>
                <p>
                  为原创文章、图片、音频、视频等数字作品提供版权存证，确保创作时间和内容的真实性，有效防止盗版和侵权行为。
                </p>
                <a-button type="primary" @click="goToSubmit"> 立即使用 </a-button>
              </div>
            </div>
          </a-col>
          <a-col :span="12">
            <div class="scenario-card">
              <div class="scenario-icon">
                <icon-edit />
              </div>
              <div class="scenario-content">
                <h3>电子合同存证</h3>
                <p>
                  为电子合同、协议、证书等重要文档提供区块链存证，确保文档的完整性和签署时间的真实性，提高合同的法律效力。
                </p>
                <a-button type="primary" @click="goToSubmit"> 立即使用 </a-button>
              </div>
            </div>
          </a-col>
        </a-row>
        <a-row :gutter="24" class="mt-6">
          <a-col :span="12">
            <div class="scenario-card">
              <div class="scenario-icon">
                <icon-code />
              </div>
              <div class="scenario-content">
                <h3>软件代码保护</h3>
                <p>
                  为软件源代码、算法、技术文档等提供存证保护，证明代码的原创性和开发时间，保护软件知识产权。
                </p>
                <a-button type="primary" @click="goToSubmit"> 立即使用 </a-button>
              </div>
            </div>
          </a-col>
          <a-col :span="12">
            <div class="scenario-card">
              <div class="scenario-icon">
                <icon-trophy />
              </div>
              <div class="scenario-content">
                <h3>学术成果保护</h3>
                <p>
                  为学术论文、研究报告、专利申请等学术成果提供存证，证明研究成果的原创性和完成时间，保护学术权益。
                </p>
                <a-button type="primary" @click="goToSubmit"> 立即使用 </a-button>
              </div>
            </div>
          </a-col>
        </a-row>
      </div>
    </div>

    <!-- Process Section -->
    <div class="section process">
      <div class="section-title">
        <h2>存证流程</h2>
      </div>
      <div class="process-container">
        <a-steps direction="horizontal" :current="3">
          <a-step title="上传文件">
            <template #description>
              <div class="step-desc">
                <p>上传需要存证的数字文件</p>
                <p>支持多种格式，最大100MB</p>
              </div>
            </template>
          </a-step>
          <a-step title="生成哈希">
            <template #description>
              <div class="step-desc">
                <p>系统自动计算文件哈希值</p>
                <p>确保文件唯一性标识</p>
              </div>
            </template>
          </a-step>
          <a-step title="上链存证">
            <template #description>
              <div class="step-desc">
                <p>将哈希值写入区块链</p>
                <p>获得不可篡改的时间戳</p>
              </div>
            </template>
          </a-step>
          <a-step title="获得证书">
            <template #description>
              <div class="step-desc">
                <p>生成存证证书</p>
                <p>可随时查询和验证</p>
              </div>
            </template>
          </a-step>
        </a-steps>
      </div>
    </div>

    <!-- Recent Evidence Section -->
    <div class="section recent-evidence">
      <div class="section-title">
        <h2>最新存证</h2>
      </div>
      <div class="evidence-list">
        <a-card v-for="item in recentEvidenceList" :key="item.id" class="evidence-card">
          <template #title>
            <div class="card-title">
              {{ item.title }}
            </div>
          </template>
          <template #extra>
            <a-button type="text" @click="viewDetails(item.id)"> 详情 </a-button>
          </template>
          <div class="card-content">
            <div class="card-item">
              <span class="label">存证编号：</span>
              <span class="value">{{ item.evidenceNumber }}</span>
            </div>
            <div class="card-item">
              <span class="label">存证时间：</span>
              <span class="value">{{ item.evidenceTime }}</span>
            </div>
            <div class="card-item">
              <span class="label">区块高度：</span>
              <span class="value">{{ item.blockHeight }}</span>
            </div>
            <div class="card-item">
              <span class="label">存证类型：</span>
              <span class="value">{{ item.evidenceType }}</span>
            </div>
          </div>
        </a-card>
      </div>
      <div class="evidence-pagination">
        <a-pagination
          v-model:current="currentPage"
          :total="totalEvidence"
          :page-size="pageSize"
          show-total
          @change="handlePageChange"
        />
      </div>
    </div>

    <!-- FAQ Section -->
    <div class="section faq">
      <div class="section-title">
        <h2>常见问题</h2>
      </div>
      <div class="faq-content">
        <a-collapse accordion>
          <a-collapse-item key="1" header="什么是区块链存证？">
            <p>
              区块链存证是利用区块链技术的不可篡改特性，将数字文件的哈希值和时间戳记录在区块链上，形成可信的数字证据。这种方式可以证明文件在特定时间点的存在性和完整性。
            </p>
          </a-collapse-item>
          <a-collapse-item key="2" header="区块链存证有什么法律效力？">
            <p>
              根据《电子签名法》、《民事诉讼法》等相关法律法规，区块链存证具有法律效力。最高人民法院也明确认可了区块链存证的证据效力，可以作为司法诉讼中的有效证据。
            </p>
          </a-collapse-item>
          <a-collapse-item key="3" header="支持哪些类型的文件存证？">
            <p>
              支持文档、图片、音频、视频、代码等各种类型的数字文件存证。单个文件大小限制为100MB，支持批量存证功能。
            </p>
          </a-collapse-item>
          <a-collapse-item key="4" header="存证费用如何计算？">
            <p>
              存证费用根据文件大小、存证类型和区块链网络费用计算。一般文档存证费用为10-50元，具体费用在提交前会显示详细计算结果。
            </p>
          </a-collapse-item>
          <a-collapse-item key="5" header="如何验证存证的真实性？">
            <p>
              可以通过存证编号、交易哈希或文件哈希在查询页面进行验证。系统会显示区块链上的原始记录，确保存证的真实性和完整性。
            </p>
          </a-collapse-item>
        </a-collapse>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref, onMounted } from 'vue';
  import { useRouter } from 'vue-router';
  import {
    IconArrowRight,
    IconSearch,
    IconSafe,
    IconClockCircle,
    IconCheckCircle,
    IconFile,
    IconEdit,
    IconCode,
    IconTrophy,
  } from '@arco-design/web-vue/es/icon';
  import { getEvidenceStatistics, queryBlockchainEvidence } from '@/http/api/evidence';

  const router = useRouter();

  // 统计数据
  const statistics = ref({
    totalEvidence: 1234567,
    todayEvidence: 1234,
    totalUsers: 98765,
    blockHeight: 2345678,
  });

  // 最新存证数据
  const recentEvidenceList = ref([
    {
      id: 1,
      title: '原创文章《区块链技术发展趋势》',
      evidenceNumber: 'EV2024001001',
      evidenceTime: '2024-01-20 14:30:25',
      blockHeight: '2345678',
      evidenceType: '文档存证',
    },
    {
      id: 2,
      title: '摄影作品《城市夜景》',
      evidenceNumber: 'EV2024001002',
      evidenceTime: '2024-01-20 14:25:18',
      blockHeight: '2345677',
      evidenceType: '图片存证',
    },
    {
      id: 3,
      title: '软件源代码 v1.2.0',
      evidenceNumber: 'EV2024001003',
      evidenceTime: '2024-01-20 14:20:12',
      blockHeight: '2345676',
      evidenceType: '代码存证',
    },
  ]);

  // 分页数据
  const currentPage = ref(1);
  const pageSize = ref(10);
  const totalEvidence = ref(150);

  // 页面跳转方法
  const goToSubmit = () => {
    router.push('/evidence/submit');
  };

  const goToQuery = () => {
    router.push('/evidence/query');
  };

  const viewDetails = (id: number) => {
    router.push(`/evidence/details/${id}`);
  };

  const handlePageChange = (page: number) => {
    currentPage.value = page;
    // 这里可以调用API获取对应页面的数据
  };

  // 工具方法
  const formatNumber = (num: number) => {
    return num.toLocaleString();
  };

  // 组件挂载时获取数据
  onMounted(() => {
    // 可以在这里调用API获取实际的统计数据和最新存证
  });
</script>

<style lang="scss" scoped>
  .page-container {
    width: 100%;
    min-height: 100vh;
    background-color: #f7f8fa;
  }

  .hero-section {
    background: linear-gradient(135deg, #165dff 0%, #4080ff 100%);
    color: white;
    padding: 80px 0;
    text-align: center;

    .content-wrapper {
      max-width: 1200px;
      margin: 0 auto;
      padding: 0 24px;
    }

    .hero-title {
      font-size: 48px;
      font-weight: 700;
      margin-bottom: 24px;
      line-height: 1.2;
    }

    .hero-description {
      font-size: 18px;
      line-height: 1.6;
      margin-bottom: 40px;
      max-width: 800px;
      margin-left: auto;
      margin-right: auto;
      opacity: 0.9;
    }

    .hero-buttons {
      display: flex;
      gap: 16px;
      justify-content: center;
      flex-wrap: wrap;

      .hero-button {
        height: 48px;
        padding: 0 32px;
        font-size: 16px;
        border-radius: 24px;
        display: flex;
        align-items: center;
        gap: 8px;
      }
    }
  }

  .section {
    padding: 80px 0;

    &:nth-child(even) {
      background-color: white;
    }

    .section-title {
      text-align: center;
      margin-bottom: 48px;

      h2 {
        font-size: 36px;
        font-weight: 600;
        color: #1d2129;
        margin: 0;
      }
    }

    .content-wrapper {
      max-width: 1200px;
      margin: 0 auto;
      padding: 0 24px;
    }
  }

  .statistics {
    .stats-container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 0 24px;

      .stat-card {
        text-align: center;
        padding: 32px 24px;
        background: white;
        border-radius: 12px;
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
        height: 100%;

        .stat-number {
          font-size: 32px;
          font-weight: 700;
          color: #165dff;
          margin-bottom: 8px;
        }

        .stat-label {
          font-size: 16px;
          color: #4e5969;
        }
      }
    }
  }

  .service-features {
    .features-container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 0 24px;

      .feature-card {
        text-align: center;
        padding: 32px 24px;
        background: white;
        border-radius: 12px;
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
        height: 100%;

        .feature-icon {
          font-size: 48px;
          color: #165dff;
          margin-bottom: 24px;
        }

        h3 {
          font-size: 20px;
          font-weight: 600;
          color: #1d2129;
          margin-bottom: 16px;
        }

        p {
          color: #4e5969;
          line-height: 1.6;
          margin: 0;
        }
      }
    }
  }

  .application-scenarios {
    .scenarios-container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 0 24px;

      .scenario-card {
        display: flex;
        align-items: flex-start;
        gap: 24px;
        padding: 32px;
        background: white;
        border-radius: 12px;
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
        height: 100%;

        .scenario-icon {
          font-size: 48px;
          color: #165dff;
          flex-shrink: 0;
        }

        .scenario-content {
          flex: 1;

          h3 {
            font-size: 20px;
            font-weight: 600;
            color: #1d2129;
            margin-bottom: 16px;
          }

          p {
            color: #4e5969;
            line-height: 1.6;
            margin-bottom: 24px;
          }
        }
      }
    }
  }

  .process {
    .process-container {
      max-width: 1000px;
      margin: 0 auto;
      padding: 0 24px;

      .step-desc {
        margin-top: 8px;

        p {
          margin: 4px 0;
          font-size: 12px;
          color: #86909c;
        }
      }
    }
  }

  .recent-evidence {
    .evidence-list {
      max-width: 1200px;
      margin: 0 auto;
      padding: 0 24px;

      .evidence-card {
        margin-bottom: 16px;
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

        .card-title {
          font-weight: 600;
          color: #1d2129;
        }

        .card-content {
          .card-item {
            display: flex;
            margin-bottom: 8px;

            .label {
              color: #86909c;
              width: 100px;
              flex-shrink: 0;
            }

            .value {
              color: #1d2129;
              font-weight: 500;
              font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            }
          }
        }
      }
    }

    .evidence-pagination {
      display: flex;
      justify-content: center;
      margin-top: 32px;
    }
  }

  .faq {
    .faq-content {
      max-width: 800px;
      margin: 0 auto;
      padding: 0 24px;

      :deep(.arco-collapse-item-header) {
        font-size: 16px;
        font-weight: 500;
        color: #1d2129;
      }

      :deep(.arco-collapse-item-content) {
        color: #4e5969;
        line-height: 1.6;
      }
    }
  }

  @media (max-width: 768px) {
    .hero-section {
      padding: 60px 0;

      .hero-title {
        font-size: 32px;
      }

      .hero-description {
        font-size: 16px;
      }

      .hero-buttons {
        flex-direction: column;
        align-items: center;

        .hero-button {
          width: 200px;
        }
      }
    }

    .section {
      padding: 60px 0;

      .section-title h2 {
        font-size: 28px;
      }
    }

    .scenario-card {
      flex-direction: column;
      text-align: center;

      .scenario-icon {
        align-self: center;
      }
    }

    .stats-container .arco-row {
      gap: 16px 0;
    }
  }
</style>
