# 文创链开放平台 - 色彩设计指南

## 概述

本项目采用了完整的主色调设计体系，确保整个应用的视觉一致性和品牌识别度。色彩系统包括品牌色、功能色、中性色等多个层次。

## 品牌主色系

### 主品牌色 - 文创链蓝
- **主色**: `#165dff` - 用于主要按钮、链接、重要信息标识
- **浅色变体**: `#4080ff` - 用于悬停状态、次要强调
- **深色变体**: `#0f58d3` - 用于激活状态、深色主题

### 使用示例
```scss
// SCSS 中使用
.primary-button {
  background-color: $primary-color;
  &:hover {
    background-color: $primary-light;
  }
}
```

```html
<!-- Tailwind CSS 中使用 -->
<button class="bg-primary hover:bg-primary-400 text-white">
  主要按钮
</button>
```

## 功能色系

### 成功色 - 绿色系
- **主色**: `#00b42a` - 成功状态、确认操作
- **浅色**: `#23c343` - 成功提示背景
- **深色**: `#009a29` - 成功状态的强调

### 警告色 - 橙色系
- **主色**: `#ff7d00` - 警告状态、需要注意的信息
- **浅色**: `#ff9a33` - 警告提示背景
- **深色**: `#d25f00` - 警告状态的强调

### 错误色 - 红色系
- **主色**: `#f53f3f` - 错误状态、危险操作
- **浅色**: `#f76560` - 错误提示背景
- **深色**: `#cb2634` - 错误状态的强调

### 信息色 - 蓝色系
- **主色**: `#165dff` - 信息提示（使用主品牌色）
- **浅色**: `#4080ff` - 信息提示背景
- **深色**: `#0f58d3` - 信息状态的强调

## 中性色系

### 文本色
- **主文本**: `#1d2129` - 标题、重要文本
- **次要文本**: `#4e5969` - 正文、描述文本
- **第三级文本**: `#86909c` - 辅助信息、标签
- **禁用文本**: `#c9cdd4` - 禁用状态文本
- **反色文本**: `#ffffff` - 深色背景上的文本

### 背景色
- **主背景**: `#ffffff` - 页面主背景
- **次要背景**: `#f7f8fa` - 卡片、面板背景
- **第三级背景**: `#f2f3f5` - 分割区域、输入框背景
- **深色背景**: `#17171a` - 深色主题背景

### 边框色
- **主边框**: `#e5e6eb` - 默认边框
- **浅边框**: `#f2f3f5` - 分割线、浅色边框
- **深边框**: `#c9cdd4` - 强调边框
- **聚焦边框**: `#165dff` - 输入框聚焦状态

## 使用建议

### 1. 层次结构
- 使用主品牌色突出最重要的操作和信息
- 使用功能色明确表达状态和反馈
- 使用中性色构建信息层次

### 2. 对比度
- 确保文本与背景有足够的对比度（至少4.5:1）
- 重要信息使用高对比度色彩组合

### 3. 一致性
- 相同功能使用相同颜色
- 保持整个应用的色彩使用一致性

### 4. 可访问性
- 不仅依靠颜色传达信息，结合图标、文字等
- 考虑色盲用户的使用体验

## Tailwind CSS 类名参考

### 背景色
```html
<!-- 品牌色 -->
<div class="bg-primary">主品牌色背景</div>
<div class="bg-primary-100">浅品牌色背景</div>

<!-- 功能色 -->
<div class="bg-success">成功色背景</div>
<div class="bg-warning">警告色背景</div>
<div class="bg-error">错误色背景</div>

<!-- 中性色 -->
<div class="bg-gray-50">浅灰背景</div>
<div class="bg-gray-900">深灰背景</div>
```

### 文本色
```html
<!-- 文本色 -->
<p class="text-text-primary">主要文本</p>
<p class="text-text-secondary">次要文本</p>
<p class="text-primary">品牌色文本</p>
```

### 边框色
```html
<!-- 边框色 -->
<div class="border border-border">默认边框</div>
<div class="border border-primary">品牌色边框</div>
```

## 更新日志

- **2024-01**: 建立完整的主色调设计体系
- 统一品牌色为文创链蓝 (#165dff)
- 完善功能色和中性色系统
- 集成 Tailwind CSS 配置