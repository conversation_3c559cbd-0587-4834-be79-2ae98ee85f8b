module.exports = {
  extends: ['@commitlint/config-conventional'],
  rules: {
    'type-enum': [
      2,
      'always',
      [
        'feat', // 新功能
        'add', // 添加功能
        'fix', // 修复bug
        'docs', // 文档变更
        'update', // 更新文件
        'style', // 代码格式（不影响功能，例如空格、分号等格式修正）
        'refactor', // 代码重构（不包括bug修复、功能新增）
        'perf', // 性能优化
        'test', // 添加、修改测试用例
        'chore', // 对构建过程或辅助工具和库的更改
        'revert', // 回滚到上一个版本
        'build', // 构建流程、外部依赖变更
        'ci', // 修改CI配置、脚本
        'release', // 发布新版本
      ],
    ],
    'subject-full-stop': [0, 'never'],
    'subject-case': [0, 'never'],
  },
};
