/** @format */

import http from '@/http/sopHttp';
import {
  ApiResponse,
  BannerItem,
  MerchantCase,
  Product,
  ProductCapabilityItem,
  UploadParams,
} from './interface';

// 获取公钥
export function getPublicKey() {
  const data = { ifNeedToken: false };
  return http.get('/token/getPublicKey', data);
}

// 获取轮播图
export function getBannerList(): Promise<ApiResponse<BannerItem[]>> {
  return http.get('/banner') as Promise<ApiResponse<BannerItem[]>>;
}
// 获取商家案例
export function getMerchantCasesList(): Promise<ApiResponse<MerchantCase[]>> {
  return http.get('/merchantCases') as Promise<ApiResponse<MerchantCase[]>>;
}

// 查询产品列表（树形）
export function getProductList(): Promise<ApiResponse<Product[]>> {
  return http.get('/product/list') as Promise<ApiResponse<Product[]>>;
}
// 查询产品列表
export function getAllProductList(): Promise<ApiResponse<Product[]>> {
  return http.get('/product') as Promise<ApiResponse<Product[]>>;
}
// 查询首页产品能力列表
export function getProductCapability(): Promise<ApiResponse<ProductCapabilityItem[]>> {
  return http.get('/ProductCapability') as Promise<ApiResponse<ProductCapabilityItem[]>>;
}
// 查询产品富文本
export function getProductInfoRichTextParam(id: string | number) {
  return http.get('/productInfoRichTextParam/info?id=' + id);
}

// 查询能力动态
export function getApiDynamicInfo() {
  return http.get('/portal/api/dynamicInfo');
}
// 查询API列表
export function getApiList(value: string) {
  return http.get('/portal/api/apiInfo', { apiName: value });
}
// 查询API介绍
export function getApiInfo(id: string | number) {
  return http.get('/portal/api/apiIntroduce/' + id);
}
// 查询API列表
export function getApiInfoList(id: string | number) {
  return http.get('/portal/api/apiList?apiInfoId=' + id);
}

// 上传图片至oss（第一步）
export async function fileUpload(file: File) {
  return http.post('/attachment-info/presigned-upload-url', file);
}
// 上传图片至服务器（第二步）
export async function uploadToServer(params: UploadParams) {
  const config = {
    headers: { 'Content-Type': params.contentType, 'Content-Md5': params.md5Base64 },
  };
  return http.uploadFile(params.url, params.file, config);
}
