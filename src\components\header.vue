<template>
  <div class="header-com">
    <div
      style="width: 1400px; margin: auto; z-index: 3; position: relative"
      class="flex items-center"
    >
      <router-link to="/home" class="flex items-center" title="文创链开放平台首页">
        <img class="logo-image" :src="imgUrl" alt="文创链开放平台" />
      </router-link>
      <div class="flex justify-between items-center flex-grow">
        <a-menu mode="horizontal" :selected-keys="[selectedKey]">
          <a-menu-item key="home">
            <router-link to="/home" title="文创链开放平台首页">首页</router-link>
          </a-menu-item>
          <a-menu-item key="data-asset">
            <router-link to="/data/page" title="数据知识产权存证服务">数据知识产权存证</router-link>
          </a-menu-item>
          <a-menu-item key="software-copyright">
            <router-link to="/software/page">计算机软件著作权登记</router-link>
          </a-menu-item>
          <a-menu-item key="works-copyright">
            <router-link to="/works/page" title="作品著作权登记服务">作品著作权登记</router-link>
          </a-menu-item>
          <a-menu-item key="blockchain-evidence">
            <router-link to="/evidence/page" title="区块链存证服务">区块链存证</router-link>
          </a-menu-item>
          <!-- <router-link to="/open-platform">
                        <a-menu-item key="open-platform">能力开放平台</a-menu-item>
                    </router-link> -->
          <!-- <router-link to="/products">
                        <a-menu-item key="products">产品大全</a-menu-item>
                    </router-link> -->
          <!-- <router-link to="/products">
                        <a-menu-item key="products">能力开放平台</a-menu-item>
                    </router-link> -->
          <!-- <router-link to="/api">
                        <a-menu-item key="api">开放文档</a-menu-item>
                    </router-link> -->
          <!-- <a href="https://blockChain.bmark.cn" target="_blank"> <a-menu-item key="">链浏览器</a-menu-item></a> -->
          <!-- <a href="https://scbsi.cn/screen" target="_blank" title="区块链浏览器" rel="noopener"> -->
          <a-menu-item key="">
            <a href="https://btsi.org.cn/" target="_blank" title="区块链浏览器" rel="noopener">区块链浏览器</a>
          </a-menu-item>
          <!-- <router-link to="">
                        <a-menu-item key="" disabled>管理后台</a-menu-item>
                    </router-link> -->
        </a-menu>
        <div v-if="!ifLogin" class="flex items-center">
          <a-link style="flex-shrink: 0; font-size: 15px" class="ml-10">
            <router-link to="/login" title="用户登录"> 登录 </router-link>
          </a-link>
          <a-link style="flex-shrink: 0; font-size: 15px" class="ml-4">
            <router-link to="/register" title="用户注册"> 注册 </router-link>
          </a-link>
        </div>
        <a-dropdown v-else trigger="hover">
          <a-avatar :size="32" class="ml-10" style="flex-shrink: 0; background-color: #165dff">
            <icon-user />
          </a-avatar>
          <template #content>
            <router-link to="/user" title="个人中心">
              <a-doption>
                <template #icon>
                  <icon-user />
                </template>
                <!-- <template #default>账号信息</template> -->
                <template #default> 个人中心 </template>
              </a-doption>
            </router-link>
            <router-link to="/login" title="退出登录">
              <a-doption @click="userOut">
                <template #icon>
                  <IconPoweroff />
                </template>
                <template #default> 退出登录 </template>
              </a-doption>
            </router-link>
          </template>
        </a-dropdown>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { ref, watch, computed } from 'vue';
  import { getAssetsFile } from '@/utils/tool';
  import { IconUser, IconPoweroff } from '@arco-design/web-vue/es/icon';
  import { useRouter } from 'vue-router';
  import { useUserStore } from '@/store/index';
  import { storeToRefs } from 'pinia';
  const router = useRouter();
  const userStore = useUserStore();
  const { user_info } = storeToRefs(userStore);
  const ifLogin = computed(() => (user_info.value ? true : false));
  const selectedKey = ref('home');

  const userOut = () => {
    userStore.userOutLogin();
  };

  watch(
    () => router.currentRoute.value,
    (newValue: any) => {
      selectedKey.value = newValue.meta.header;
    },
    { immediate: true }
  );
  // let imgUrl = getAssetsFile("wenChuang-logo5.png");
  let imgUrl = getAssetsFile('logo/wenChuang-logo6-mini.png');
</script>

<style lang="scss" scoped>
  .header-com {
    position: fixed;
    top: 0;
    left: 0;
    z-index: 9;
    width: 100%;

    .logo-image {
      height: 44px;
      // width: 253px;

      :deep(.arco-image) {
        height: 44px;
        width: 253px;
      }

      :deep(.arco-image-img) {
        height: 44px;
        width: 253px;
        object-fit: contain;
      }
    }

    .arco-menu-light {
      background-color: transparent;
      position: relative;
      z-index: 4;
    }

    .arco-menu-overflow-wrap {
      position: relative;
      z-index: 4;
    }

    .arco-menu-item {
      color: black;
      font-size: 15px;
      background-color: transparent;
    }

    :deep(.arco-menu-overflow-wrap) {
      position: relative;
      z-index: 4;
    }

    :deep(.arco-menu-selected-label) {
      display: none !important;
    }
  }

  .header-com::after {
    display: block;
    content: '';
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.8);
    position: absolute;
    top: 0;
    left: 0;
    backdrop-filter: blur(6px);
  }
</style>
