import { describe, it, expect, vi, beforeEach } from 'vitest';
import { mount } from '@vue/test-utils';
import { nextTick } from 'vue';
import LoginPage from '../index.vue';

// Mock dependencies
vi.mock('@/utils/tool', () => ({
  getAssetsFile: vi.fn(() => 'mock-image.png'),
}));

vi.mock('@/store/index', () => ({
  useUserStore: vi.fn(() => ({
    setToken: vi.fn(),
    setUserInfo: vi.fn(),
  })),
}));

vi.mock('vue-router', () => ({
  useRouter: vi.fn(() => ({
    replace: vi.fn(),
  })),
}));

vi.mock('@/http/api/login', () => ({
  memberLogin: vi.fn(),
  memberSmsLogin: vi.fn(),
  getIsvPortal: vi.fn(),
  memberSendSmsCode: vi.fn(),
}));

vi.mock('@arco-design/web-vue', () => ({
  Message: {
    success: vi.fn(),
    error: vi.fn(),
  },
}));

describe('LoginPage', () => {
  let wrapper: any;

  beforeEach(() => {
    wrapper = mount(LoginPage, {
      global: {
        stubs: {
          'a-tabs': true,
          'a-tab-pane': true,
          'a-form': true,
          'a-form-item': true,
          'a-input': true,
          'a-input-password': true,
          'a-button': true,
          'a-link': true,
        },
      },
    });
  });

  it('should render login page correctly', () => {
    expect(wrapper.find('.login-wrapper').exists()).toBe(true);
    expect(wrapper.find('.login-container').exists()).toBe(true);
    expect(wrapper.text()).toContain('登录到');
    expect(wrapper.text()).toContain('文创链开放平台');
  });

  it('should switch between account and mobile login modes', async () => {
    // 默认应该是账号登录模式
    expect(wrapper.vm.loginType).toBe('account');

    // 切换到手机登录模式
    wrapper.vm.loginType = 'mobile';
    await nextTick();

    expect(wrapper.vm.loginType).toBe('mobile');
  });

  it('should clear form data when switching login type', async () => {
    // 设置一些表单数据
    wrapper.vm.form.username = '<EMAIL>';
    wrapper.vm.form.password = 'password123';
    wrapper.vm.form.mobile = '***********';
    wrapper.vm.form.smsCode = '123456';

    // 调用切换方法
    wrapper.vm.handleLoginTypeChange();

    // 验证表单数据被清空
    expect(wrapper.vm.form.username).toBe('');
    expect(wrapper.vm.form.password).toBe('');
    expect(wrapper.vm.form.mobile).toBe('');
    expect(wrapper.vm.form.smsCode).toBe('');
    expect(wrapper.vm.countdown).toBe(60);
  });

  it('should validate mobile number format', () => {
    const validMobile = '***********';
    const invalidMobile = '***********';

    expect(/^1[3-9]\d{9}$/.test(validMobile)).toBe(true);
    expect(/^1[3-9]\d{9}$/.test(invalidMobile)).toBe(false);
  });

  it('should validate SMS code format', () => {
    const validCode = '123456';
    const invalidCode = 'abc123';

    expect(/^\d{4,6}$/.test(validCode)).toBe(true);
    expect(/^\d{4,6}$/.test(invalidCode)).toBe(false);
  });

  it('should handle SMS code input correctly', () => {
    // 测试只允许数字输入
    wrapper.vm.handleSmsCodeInput('abc123def456');
    expect(wrapper.vm.form.smsCode).toBe('123456');

    wrapper.vm.handleSmsCodeInput('123');
    expect(wrapper.vm.form.smsCode).toBe('123');
  });
});
