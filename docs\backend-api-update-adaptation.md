# 后端创建存证接口更新适配总结

## 接口变化概述

根据最新的接口文档 `http://127.0.0.1:4523/export/openapi/13?version=3.0`，后端创建存证接口发生了以下重要变化：

### 1. 请求方式变化

**之前**: POST 请求体
```javascript
proHttp.post('/app-api/bisness/certificate-info/create', params);
```

**现在**: POST 请求 + query 参数
```javascript
proHttp.post('/app-api/bisness/certificate-info/create', null, { params });
```

### 2. 必填字段变化

**新增必填字段**: `dataResourceOwnership`（数据资源权属申明）
- 之前：可选字段 `dataResourceOwnership?: string`
- 现在：必填字段 `dataResourceOwnership: string`

### 3. 字段描述更新

- `personalInfoCollectionEvidenceMaterial` 的描述更新为"个人信息采集的合法佐证材料路径"

## 前端适配工作

### 1. 更新 API 接口定义

**文件**: `src/http/api/data/index.ts`

```typescript
// 修改前
export function createCertificateInfo(params: {
  // ...
  dataResourceOwnership?: string; // 可选
  // ...
}) {
  return proHttp.post('/app-api/bisness/certificate-info/create', params);
}

// 修改后
export function createCertificateInfo(params: {
  // ...
  dataResourceOwnership: string; // 必填字段
  // ...
}) {
  // 使用 query 参数而不是请求体
  return proHttp.post('/app-api/bisness/certificate-info/create', null, { params });
}
```

### 2. 更新表单验证

**文件**: `src/view/data/eviApply.vue`

```typescript
const validateForm = () => {
  const errors: string[] = [];

  // 原有验证...
  
  // 新增：验证数据资源权属申明（必填字段）
  if (!formData.value.dataResourceOwnership || 
      (Array.isArray(formData.value.dataResourceOwnership) && 
       formData.value.dataResourceOwnership.length === 0)) {
    errors.push('请选择数据资源权属申明');
  }

  // ...
};
```

### 3. 更新表单UI

**文件**: `src/view/data/eviApply.vue`

```vue
<!-- 添加必填标识 -->
<a-form-item 
  field="dataResourceOwnership" 
  label="数据资源权属申明" 
  required
>
  <!-- 表单控件 -->
</a-form-item>
```

### 4. 更新数据提交处理

确保 `dataResourceOwnership` 字段不为空：

```typescript
const submitData = {
  // ...
  dataResourceOwnership: Array.isArray(formData.value.dataResourceOwnership)
    ? formData.value.dataResourceOwnership.join(',')
    : (formData.value.dataResourceOwnership || ''), // 确保不为空
  // ...
};
```

## 兼容性说明

### 向后兼容性

1. **字段映射保持不变**: 所有现有字段的映射关系保持不变
2. **数据格式保持不变**: 文件路径、时间格式等处理逻辑保持不变
3. **用户体验保持一致**: 表单操作流程保持不变

### 破坏性变化

1. **请求方式变化**: 从 POST 请求体改为 query 参数
2. **必填字段增加**: `dataResourceOwnership` 现在是必填的
3. **验证规则更严格**: 必须选择至少一个数据资源权属申明选项

## 测试要点

### 1. 接口调用测试

- [ ] 验证请求使用 query 参数而不是请求体
- [ ] 确认所有参数正确传递到后端
- [ ] 测试接口响应处理

### 2. 表单验证测试

- [ ] 测试不选择数据资源权属申明时的验证提示
- [ ] 验证其他必填字段的验证逻辑
- [ ] 测试表单提交成功的情况

### 3. 用户体验测试

- [ ] 确认必填字段有明显的视觉标识
- [ ] 验证错误提示信息清晰明确
- [ ] 测试表单重置和暂存功能

### 4. 数据完整性测试

- [ ] 验证数据资源权属申明的多选处理
- [ ] 确认文件路径正确收集和传递
- [ ] 测试特殊字符和边界情况

## 注意事项

### 1. 数据资源权属申明

- 现在是必填字段，用户必须至少选择一个选项
- 多选项会用逗号连接成字符串传递给后端
- 需要确保数据字典中有相应的选项数据

### 2. 请求格式

- 后端现在期望 query 参数而不是请求体
- 需要确保所有参数都正确编码
- 注意 URL 长度限制（虽然通常不会超过）

### 3. 错误处理

- 需要处理新的验证错误情况
- 确保错误提示对用户友好
- 考虑网络超时等异常情况

## 部署建议

### 1. 渐进式部署

1. 先部署后端接口更新
2. 验证后端接口正常工作
3. 再部署前端适配代码

### 2. 回滚准备

- 保留旧版本的接口调用代码作为备份
- 准备快速回滚方案
- 监控部署后的错误率

### 3. 用户通知

- 如果界面有明显变化，考虑通知用户
- 更新用户手册或帮助文档
- 准备客服支持应对用户问题

## 相关文档

- [文件上传调试指南](./file-upload-debug-guide.md)
- [文件上传修复总结](./file-upload-fix-summary.md)
- [存证提交功能实现文档](./evidence-submit-implementation.md)

## 总结

此次后端接口更新主要涉及请求方式的变化和必填字段的增加。前端适配工作已完成，包括：

1. ✅ 更新 API 接口调用方式（POST + query 参数）
2. ✅ 添加数据资源权属申明必填验证
3. ✅ 更新表单UI显示必填标识
4. ✅ 确保数据提交时字段不为空

所有修改都保持了向后兼容性，用户体验基本保持不变，只是增加了一个必填字段的验证要求。
