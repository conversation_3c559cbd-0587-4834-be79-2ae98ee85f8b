import http from "@/http/sopHttp";

export function chinaumsMerchat() {
  return http.get("/portal/chinaumsMerchat/complexUpload");
}

// 获取用户信息
export function portalUserInfo() {
  return http.get("/portal/user/info");
}

// 获取银行支行
export function getBranchBankList(data: any) {
  return http.get("/portal/user/getBranchBankList", data);
}

// 重新发送余额
export function getValidationFirst() {
    return http.get("/portal/chinaumsMerchat/validationFirst");
  }

// 发送第一次
export function getValidationSecond(data: any) {
    return http.get("/portal/chinaumsMerchat/validationSecond", data);
}

// 查询步骤
export function chinaumsMerchatProcess() {
    return http.get("/portal/chinaumsMerchat/process")
}

// 再次获取URL
export function chinaumsMerchatAgreementSign() {
    return http.get("/portal/chinaumsMerchat/agreementSign")
}