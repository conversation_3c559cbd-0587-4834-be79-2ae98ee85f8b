// ========== 会员模块接口，老版本SOP1.0，sopHttp ==========
import sopHttp from '@/http/sopHttp';

export async function getPublicKey() {
  return sopHttp.get(`/token/getPublicKey`);
}

export async function loginFun(data: any) {
  return sopHttp.post(`/portal/common/login`, data);
}

// 发送验证码(注册用)
export async function sendSmsCodeFun(data: any) {
  const data2 = { ...data };
  delete data2.mobile;
  return sopHttp.get(`/mobile/sendSmsCode/register/` + data.mobile, data2);
}

// 获取商户信息
export async function getIsvPortal() {
  return sopHttp.get(`/portal/isv/getIsvPortal`);
}

// 注册
export async function registerFun(data: any) {
  return sopHttp.post(`/portal/common/regIsv`, data);
}

// 发送验证码(找回密码用)
export async function forgetSendSmsCodeFun(data: any) {
  const data1 = { ...data };
  delete data1.mobile;
  return sopHttp.get(`/mobile/sendSmsCode/` + data.mobile, data1);
}

// 找回密码
export async function forgetFun(data: any) {
  return sopHttp.put(`/user/find-password`, data);
}

// 获取用户信息
export async function getUserInfoFun() {
  return sopHttp.get(`/user/info`);
}

// ========== 会员模块接口，新版文创链2.0，proHttp ==========
import proHttp from '@/http/proHttp';

// 会员注册相关接口类型定义（基于接口文档）
export interface MemberRegisterParams {
  registerType: '1' | '2'; // 注册类型：1-手机号注册，2-账号注册
  mobile?: string; // 手机号（手机号注册时必填）
  username?: string; // 账号（账号注册时必填）
  password?: string; // 密码（账号注册时必填）
  code: string; // 手机验证码
  nickname?: string; // 用户昵称
  socialType?: string; // 社交平台类型
  socialCode?: string; // 授权码
  socialState?: string; // state
}

export interface MemberSendSmsCodeParams {
  mobile: string;
  scene: string; // 场景：1-注册，2-登录，3-重置密码
}

export interface ApiResponse<T = any> {
  code: number;
  data: T;
  msg: string;
}

// 登录响应数据结构
export interface AppAuthLoginRespVO {
  userId: number;
  accessToken: string;
  refreshToken: string;
  expiresTime: string;
  openid?: string;
}

// 会员注册（统一接口，基于接口文档）
export async function memberRegister(
  data: MemberRegisterParams
): Promise<ApiResponse<AppAuthLoginRespVO>> {
  // 参数验证
  if (!data.registerType || !['1', '2'].includes(data.registerType)) {
    throw new Error('注册类型参数不正确');
  }

  if (data.registerType === '1') {
    // 手机号注册验证
    if (!data.mobile || !/^1[3-9]\d{9}$/.test(data.mobile)) {
      throw new Error('手机号格式不正确');
    }
  } else {
    // 账号注册验证
    if (!data.username) {
      throw new Error('账号不能为空');
    }
    if (!data.password || data.password.length < 8) {
      throw new Error('密码长度不能少于8位');
    }
    if (!data.mobile || !/^1[3-9]\d{9}$/.test(data.mobile)) {
      throw new Error('手机号格式不正确');
    }
  }

  if (!data.code || !/^\d{4,6}$/.test(data.code)) {
    throw new Error('验证码格式不正确');
  }

  return proHttp.post(`/app-api/member/auth/register`, data) as Promise<
    ApiResponse<AppAuthLoginRespVO>
  >;
}

// 保留旧接口以兼容现有代码
export async function memberRegisterByMobile(data: any) {
  return memberRegister({
    registerType: '1',
    mobile: data.mobile,
    code: data.smsCode || data.code,
    nickname: data.nickname,
  });
}

export async function memberRegisterByAccount(data: any) {
  return memberRegister({
    registerType: '2',
    username: data.username,
    password: data.password,
    mobile: data.mobile,
    code: data.smsCode || data.code,
    nickname: data.nickname,
  });
}

// 会员登录-手机号+密码
export async function memberLogin(data: any) {
  return proHttp.post(`/app-api/member/auth/login`, data);
}

// 会员登录-手机号+短信验证码
export async function memberSmsLogin(data: { mobile: string; code: string }) {
  return proHttp.post(`/app-api/member/auth/sms-login`, data);
}

// 会员登录-账号+密码
export async function memberAccountLogin(data: { username: string; password: string }) {
  return proHttp.post(`/app-api/member/auth/account-login`, data);
}

// 会员登出
export async function memberLogout() {
  return proHttp.post(`/app-api/member/auth/logout`);
}

// 会员发送短信验证码（注册用）
export async function memberSendSmsCode(data: any) {
  return proHttp.post(`/app-api/member/auth/send-sms-code`, data);
}

// 会员发送重置密码短信验证码
export async function memberSendResetPasswordSmsCode(data: any) {
  return proHttp.post(`/app-api/member/auth/send-sms-code`, data);
}

// 会员刷新令牌
export async function memberRefreshToken(data: any) {
  return proHttp.post(`/app-api/member/auth/refresh-token`, data);
}

// 获得会员基本信息
export async function getMemberUserInfo() {
  return proHttp.get(`/app-api/member/user/get`);
}

// 修改会员基本信息
export async function updateMemberUser(data: any) {
  return proHttp.put(`/app-api/member/user/update`, data);
}

// 修改会员用户密码
export async function updateMemberPassword(data: { password: string; code: string }) {
  return proHttp.put(`/app-api/member/user/update-password`, data);
}

// 修改会员用户手机号
export async function updateMemberMobile(data: any) {
  return proHttp.put(`/app-api/member/user/update-mobile`, data);
}

// 基于微信小程序的授权码，修改会员用户手机号
export async function updateMemberMobileByWeixin(data: any) {
  return proHttp.put(`/app-api/member/user/update-mobile-by-weixin`, data);
}

// 重置会员密码
export async function resetMemberPassword(data: any) {
  return proHttp.put(`/app-api/member/user/reset-password`, data);
}

// ========== 会员收件地址管理 ==========

// 创建用户收件地址
export async function createMemberAddress(data: any) {
  return proHttp.post(`/app-api/member/address/create`, data);
}

// 更新用户收件地址
export async function updateMemberAddress(data: any) {
  return proHttp.put(`/app-api/member/address/update`, data);
}

// 获得用户收件地址列表
export async function getMemberAddressList() {
  return proHttp.get(`/app-api/member/address/list`);
}

// 获得用户收件地址
export async function getMemberAddress(id: number) {
  return proHttp.get(`/app-api/member/address/get?id=${id}`);
}

// 获得默认的用户收件地址
export async function getDefaultMemberAddress() {
  return proHttp.get(`/app-api/member/address/get-default`);
}

// 删除用户收件地址
export async function deleteMemberAddress(id: number) {
  return proHttp.delete(`/app-api/member/address/delete?id=${id}`);
}
