<template>
  <div style="width: 50%">
    <a-form ref="formRef" size="large" :model="form" @submit-success="handleSubmit">
      <a-form-item
        field="nickName"
        label="用户昵称"
        :rules="[
          { required: true, message: '必填' },
          { minLength: 4, message: '最少4个字符' },
        ]"
        :validate-trigger="['change', 'input']"
      >
        <a-input v-model="form.nickName" placeholder="请输入用户昵称.." />
      </a-form-item>
      <a-form-item field="phone" label="手机号">
        <a-input v-model="form.phone" disabled />
        <template #extra>
          <div>用户手机号暂不支持修改。</div>
        </template>
      </a-form-item>
      <a-form-item>
        <a-space>
          <a-button type="primary" html-type="submit" :loading="loading"> 保存 </a-button>
          <a-button type="secondary" @click="resetForm"> 重置 </a-button>
        </a-space>
      </a-form-item>
    </a-form>
  </div>
</template>

<script lang="ts" setup>
  import { getAssetsFile } from '@/utils/tool';
  import { ref, reactive, onMounted } from 'vue';
  import { Message } from '@arco-design/web-vue';
  import { editUserInfo } from '@/http/api/user';
  import { useUserStore } from '@/store/index';
  const userStore = useUserStore();
  const loading = ref(false);

  type Props = {
    formData: {
      nickName: string;
      username: string;
      phone: string;
    };
  };
  const props = defineProps<Props>();
  // 表单绑定
  const form = ref({
    nickName: props.formData.nickName,
    phone: props.formData.phone,
    username: props.formData.username,
  });
  let detailNickName = props.formData.nickName; // 用户名称 用来重置表单
  // 重置昵称
  const resetForm = () => {
    form.value.nickName = detailNickName;
  };
  // 提交
  const handleSubmit = (val: any) => {
    loading.value = true;
    let params = { nickName: val.nickName, username: form.value.username };
    editUserInfo(params).then(res => {
      loading.value = false;
      if (res) {
        Message.success('更新成功！');
        let userInfo = { ...userStore.getUserInfo, nickName: val.nickName };
        console.log(userInfo);

        userStore.setUserInfo(userInfo);
        console.log(userStore.getUserInfo);
      }
    });

    // 1.获取密匙
    // getPublicKey().then((res: any) => {
    //     console.log(res)
    //     let rsa = new JsEncrypt()
    //     rsa.setPrivateKey(res);
    //     params.password = rsa.encrypt(params.password);
    //     params.publicKey = res;
    //     refreshCode()
    //     loginFun(params).then((data: any) => {
    //         // sessionStorage.setItem('token', data.access_token)
    //         userStore.setToken(data.access_token)
    //         getUserInfo()
    //     })
    // })
  };
</script>
