<template>
  <div class="login-wrapper bg-white" :style="{ backgroundImage: `url(${loginBg})` }">
    <div class="login-container">
      <div class="text-4xl font-semibold">登录到</div>
      <div class="text-4xl font-semibold mt-1">文创链开放平台</div>

      <!-- 登录方式切换标签 -->
      <a-tabs
        v-model:active-key="loginType"
        class="mt-6"
        size="large"
        @change="handleLoginTypeChange"
      >
        <a-tab-pane key="account" title="账号登录" />
        <a-tab-pane key="mobile" title="手机登录" />
      </a-tabs>

      <a-form ref="formRef" size="large" :model="form" class="mt-4" @submit-success="handleSubmit">
        <!-- 账号登录表单 -->
        <template v-if="loginType === 'account'">
          <a-form-item
            field="username"
            :hide-label="true"
            :hide-asterisk="true"
            :rules="[{ required: true, message: '请输入账号' }]"
            :validate-trigger="['change', 'input']"
          >
            <a-input
              v-model="form.username"
              :style="{ width: '320px' }"
              placeholder="请输入账号"
              allow-clear
            />
          </a-form-item>
          <a-form-item
            field="password"
            :hide-label="true"
            :hide-asterisk="true"
            :rules="[{ required: true, message: '请输入密码' }]"
            :validate-trigger="['change', 'input']"
          >
            <a-input-password
              v-model="form.password"
              :style="{ width: '320px' }"
              placeholder="请输入密码"
              allow-clear
            />
          </a-form-item>
        </template>

        <!-- 手机登录表单 -->
        <template v-if="loginType === 'mobile'">
          <a-form-item
            field="mobile"
            :hide-label="true"
            :hide-asterisk="true"
            :rules="[
              { required: true, message: '请输入手机号' },
              { match: /^1[3-9]\d{9}$/, message: '请输入正确的手机号' },
            ]"
            :validate-trigger="['change', 'input']"
          >
            <a-input
              v-model="form.mobile"
              :style="{ width: '320px' }"
              placeholder="请输入手机号"
              allow-clear
              maxlength="11"
            />
          </a-form-item>
          <a-form-item
            field="smsCode"
            :hide-label="true"
            :hide-asterisk="true"
            :rules="[
              { required: true, message: '请输入短信验证码' },
              { match: /^\d{4,6}$/, message: '验证码应为4-6位数字' },
            ]"
            :validate-trigger="['change', 'input']"
          >
            <div class="flex items-center gap-2">
              <a-input
                v-model="form.smsCode"
                :style="{ width: '220px' }"
                placeholder="请输入短信验证码"
                allow-clear
                maxlength="6"
                @input="handleSmsCodeInput"
              />
              <a-button
                v-if="countdown === 60"
                type="primary"
                :disabled="!form.mobile || !/^1[3-9]\d{9}$/.test(form.mobile)"
                @click="sendSmsCode"
              >
                发送验证码
              </a-button>
              <a-button v-else type="primary" disabled> {{ countdown }}s </a-button>
            </div>
          </a-form-item>
        </template>

        <a-form-item :hide-label="true" :hide-asterisk="true">
          <div class="flex w-full justify-between items-center">
            <div class="flex items-center">
              <span class="text-gray-500">没有账号吗?</span>
              <span class="cursor-pointer ml-1" @click="pageJump('register')">注册新账号</span>
            </div>
            <div class="flex-1 text-right">
              <a-link @click="pageJump('forget')"> 忘记密码? </a-link>
            </div>
          </div>
        </a-form-item>
        <a-form-item class="mt-4" :hide-label="true" :hide-asterisk="true">
          <a-button type="primary" long html-type="submit"> 登 录 </a-button>
        </a-form-item>
      </a-form>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { getAssetsFile } from '@/utils/tool';
  import { useUserStore } from '@/store/index';
  import { ref, reactive } from 'vue';
  import md5 from 'js-md5';
  import {
    memberLogin,
    memberSmsLogin,
    memberAccountLogin,
    getIsvPortal,
    memberSendSmsCode,
  } from '@/http/api/login';
  import { useRouter } from 'vue-router';
  import { Message } from '@arco-design/web-vue';
  import { userInfo } from 'os';
  let loginBg = getAssetsFile('login-bg.png');
  const userStore = useUserStore();
  const router = useRouter();

  // 登录方式：account-账号登录，mobile-手机登录
  const loginType = ref('account');

  // 登录框表单绑定
  const form = reactive({
    username: '',
    password: '',
    mobile: '',
    smsCode: '',
  });

  // 短信验证码倒计时
  const countdown = ref(60);

  // 登录方式切换处理
  const handleLoginTypeChange = () => {
    // 清空表单数据
    form.username = '';
    form.password = '';
    form.mobile = '';
    form.smsCode = '';
    // 重置倒计时
    countdown.value = 60;
  };

  // 处理短信验证码输入，只允许数字
  const handleSmsCodeInput = (value: string) => {
    const numericValue = value.replace(/\D/g, '');
    form.smsCode = numericValue;
  };

  // 发送短信验证码
  const sendSmsCode = () => {
    if (!form.mobile || !/^1[3-9]\d{9}$/.test(form.mobile)) {
      Message.error('请输入正确的手机号');
      return;
    }

    const params = {
      mobile: form.mobile,
      scene: '1', // 会员用户-手机号登录场景
    };

    memberSendSmsCode(params)
      .then((res: any) => {
        if (res.code === 0 || res.data === true) {
          Message.success('验证码发送成功！请注意查收');
          setTime();
        } else {
          const errorMsg = res.msg || res.message || '发送失败，请重试';
          Message.error(errorMsg);
        }
      })
      .catch((error: any) => {
        Message.error(error.msg || '发送失败，请重试');
      });
  };

  // 倒计时
  const setTime = () => {
    if (countdown.value === 1) {
      countdown.value = 60;
      return;
    } else {
      countdown.value--;
    }
    setTimeout(() => {
      setTime();
    }, 1000);
  };

  // 提交方法
  const handleSubmit = () => {
    if (loginType.value === 'account') {
      // 账号登录
      const params = {
        username: form.username,
        // password: md5(form.password),
        password: form.password,
      };

      memberAccountLogin(params)
        .then((r: any) => {
          handleLoginSuccess(r, form.username);
        })
        .catch((error: any) => {
          Message.error(error.msg || '登录失败，请重试');
        });
    } else {
      // 手机号登录
      const params = {
        mobile: form.mobile,
        code: form.smsCode,
      };

      memberSmsLogin(params)
        .then((r: any) => {
          handleLoginSuccess(r, form.mobile);
        })
        .catch((error: any) => {
          Message.error(error.msg || '登录失败，请重试');
        });
    }
  };

  // 处理登录成功的逻辑
  const handleLoginSuccess = (r: any, identifier: string) => {
    //这里r需要从data中取
    r = r.data;
    console.log(r);
    if (r.accessToken) {
      userStore.setToken(r.accessToken);
      // getIsvPortal().then((res: any) => {
      //   console.log(res);
      //   let userInfo = {
      //     ...res,
      //     email: identifier,
      //   };
      //   userStore.setUserInfo(userInfo);
      //   router.replace('/home');
      // });
      let userInfo = {
        ...r,
        email: identifier,
      };
      userStore.setUserInfo(userInfo);
      router.replace('/home');
    } else {
      Message.error(r.msg || '网络异常，请稍后再试');
    }
  };
  const pageJump = (d: string) => {
    router.replace('/' + d);
  };
</script>

<style lang="scss" scoped>
  .login-wrapper {
    height: 100vh;
    display: flex;
    flex-direction: column;
    background-size: cover;
    background-position: 100%;
    position: relative;
  }

  .login-container {
    position: absolute;
    top: 22%;
    left: 6%;
    min-height: 500px;
  }

  // 优化标签页样式
  :deep(.arco-tabs-nav) {
    margin-bottom: 0;
  }

  :deep(.arco-tabs-content) {
    padding-top: 0;
  }

  // 优化表单间距
  :deep(.arco-form-item) {
    margin-bottom: 16px;
  }
</style>
