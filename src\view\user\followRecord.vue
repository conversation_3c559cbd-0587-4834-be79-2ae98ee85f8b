<template>
  <div class="p-4 bg-grey">
    <a-page-header
      :show-back="false"
      :style="{ background: 'var(--color-bg-2)' }"
      title="我的关注"
      :subtitle="'共' + searchParams.total + '条'"
    />
    <div v-if="list.length > 0" class="bg-white px-4">
      <a-row>
        <a-col v-for="(item, index) in list" :key="index" :span="8">
          <!-- <router-link :to="'/shop/detail?id=' + item.worksPutId"> -->
          <a-card hoverable class="mb-6 mx-2" @click="handleLink(item.worksPutId)">
            <template #actions>
              <span
                v-if="item.attentionRecordId"
                class="icon-hover"
                @click.stop="deleteAttentionRecordFn(item)"
              >
                <Icon-Star-Fill />
              </span>
              <span v-else class="icon-hover" @click.stop="addAttentionRecordFun(item)">
                <Icon-Star />
              </span>
              <span class="icon-hover" @click.stop="shareInternal(item)">
                <IconShareInternal />
              </span>
            </template>
            <template #cover>
              <div
                :style="{
                  height: '204px',
                  overflow: 'hidden',
                }"
              >
                <img
                  :style="{ width: '100%', transform: 'translateY(-20px)' }"
                  alt="dessert"
                  :src="item.cover"
                />
              </div>
            </template>
            <a-card-meta :title="item.worksName">
              <template #avatar>
                <div :style="{ display: 'flex', alignItems: 'center', color: '#1D2129' }">
                  <a-avatar
                    class="mr-2"
                    :style="{ backgroundColor: '#ffffff', width: '22px', height: '22px' }"
                  >
                    <img alt="avatar" :src="priceIcon" />
                  </a-avatar>
                  <a-typography-text class="text-xl">
                    {{ item.price }}
                  </a-typography-text>
                </div>
              </template>
              <template #description>
                <div>{{ item.worksName }}</div>
                <div class="text-sm text-gray-500">关注日期：{{ item.attentionDate }}</div>
              </template>
            </a-card-meta>
          </a-card>
          <!-- </router-link> -->
        </a-col>
      </a-row>
      <div class="py-6 flex justify-center">
        <a-pagination
          size="large"
          :total="searchParams.total"
          :current="searchParams.current"
          :page-size="searchParams.size"
          simple
          @change="getIndexData"
        />
      </div>
    </div>
    <template v-else>
      <div class="bg-white py-10 flex flex-col">
        <a-empty />
        <a-button type="text" @click="handShopleLink"> 去逛逛 </a-button>
      </div>
    </template>
  </div>
  <!-- 分享弹窗 -->
  <a-modal :visible="modalVisible" ok-text="复制链接" @ok="modalOk" @cancel="modalVisible = false">
    <template #title> 分享链接 </template>
    <div>{{ shareData.title }}</div>
    <a-link>{{ shareData.url }}</a-link>
  </a-modal>
</template>

<script lang="ts" setup>
  import { getAssetsFile, IntegerToFloat, copyToClip } from '@/utils/tool';
  import { ref, reactive, onMounted } from 'vue';
  import { useRouter } from 'vue-router';
  import { IconStar, IconShareInternal, IconStarFill } from '@arco-design/web-vue/es/icon';
  import {
    getAttentionRecordRecord,
    deleteAttentionRecord,
    addAttentionRecord,
  } from '@/http/api/user';
  import { Message } from '@arco-design/web-vue';

  // 定义关注项接口
  interface FollowItem {
    id: string | number;
    worksPutId: number;
    attentionRecordId: number;
    cover: string;
    worksName: string;
    attentionDate: string;
    price: string | number;
    priceStart?: string;
    priceEnd?: string;
  }

  // 分享数据接口
  interface ShareData {
    title: string;
    url: string;
  }

  const priceIcon = getAssetsFile('icons/price-icon.png');
  const router = useRouter();

  // 关注列表
  const list = ref<FollowItem[]>([]);
  const searchParams = ref({
    total: 0,
    current: 1,
    size: 9,
  });
  // 跳转 /shop/search
  const handleLink = (id: number) => {
    router.push({ path: `/shop/detail`, query: { id: id } });
  };
  // 跳转
  const handShopleLink = () => {
    router.push({ path: `/shop/search` });
  };
  // 查询关注列表
  const getIndexData = (current?: number) => {
    let params = {
      current: current || searchParams.value.current,
      size: searchParams.value.size,
    };
    getAttentionRecordRecord(params).then((res: any) => {
      let arr = res.records.map(el => {
        el.price = IntegerToFloat(el.price);
        el.priceStart = el.price.toString().split('.')[0];
        el.priceEnd = el.price.toString().split('.')[1];
        return el;
      });
      list.value = arr;
      if (current !== undefined) {
        searchParams.value.current = current;
      }
      searchParams.value.total = res.total;
    });
  };
  getIndexData();

  // 添加关注
  const addAttentionRecordFun = (item: FollowItem) => {
    let params = {
      worksPutId: item.worksPutId,
    };
    addAttentionRecord(params).then((res: any) => {
      if (res.code == 0) {
        Message.success('关注成功');
        getIndexData();
      }
    });
  };
  // 取消关注
  const deleteAttentionRecordFn = (item: FollowItem) => {
    deleteAttentionRecord(item.attentionRecordId).then((res: any) => {
      if (res.code == 0) {
        Message.success('取消成功');
        getIndexData();
      }
    });
  };

  // 分享
  const modalVisible = ref(false); // 显示分享弹窗
  const shareData = ref<ShareData>({
    title: '',
    url: '',
  });
  // 分享地址
  const shareInternal = (item: FollowItem) => {
    shareData.value.url = `${window.location.origin}/#/shop/detail?id=${item.worksPutId}`;
    shareData.value.title = `【国家文化大数据体系·巴蜀文化专业中心】${item.worksName}`;
    modalVisible.value = true;
  };
  // 复制文本
  const modalOk = () => {
    copyToClip(`${shareData.value.title} ${shareData.value.url}`);
    modalVisible.value = false;
    Message.success('复制成功，快去分享吧！');
  };
</script>
<style lang="scss" scoped>
  .icon-hover {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    transition: all 0.1s;
  }

  .icon-hover:hover {
    background-color: rgb(var(--gray-2));
  }
</style>
