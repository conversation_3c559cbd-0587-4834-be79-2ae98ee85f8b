# Token自动刷新机制实现文档

## 概述

本文档描述了在文创链用户端项目中实现的Token自动刷新机制，确保用户在使用过程中不会因为Token过期而被强制退出登录，提升用户体验。

## 实现方案

### 1. 用户Store改造 (`src/store/modules/user/index.js`)

扩展了用户Store以支持完整的Token信息管理：

**新增状态字段：**
- `access_token`: 访问令牌
- `refresh_token`: 刷新令牌  
- `expires_time`: 过期时间

**新增Getters：**
- `getAccessToken()`: 获取访问令牌
- `getRefreshToken()`: 获取刷新令牌
- `getExpiresTime()`: 获取过期时间
- `isTokenExpiringSoon()`: 检查Token是否即将过期（提前5分钟）
- `isTokenExpired()`: 检查Token是否已过期

**新增Actions：**
- `setTokenInfo()`: 设置完整的Token信息
- `setAccessToken()`: 设置访问令牌
- `setRefreshToken()`: 设置刷新令牌
- `setExpiresTime()`: 设置过期时间

### 2. Token管理工具类 (`src/utils/token.ts`)

创建了专门的Token管理工具类 `TokenManager`，提供统一的Token操作接口：

**主要功能：**
- Token的获取、设置、删除
- Token状态检查（是否存在、是否过期、是否即将过期）
- Token格式化（Bearer格式）
- 兼容旧版本API

### 3. Axios拦截器改造 (`src/http/axios.ts`)

实现了完整的Token自动刷新机制：

**核心特性：**
- **无感知刷新**: 用户无需感知Token刷新过程
- **请求队列管理**: 刷新期间的请求会被暂存，刷新成功后自动重发
- **防重复刷新**: 确保同时只有一个刷新请求
- **错误处理**: 刷新失败时自动跳转登录页

**工作流程：**
1. 检测到401错误或业务状态码9（登录过期）
2. 检查是否有refreshToken
3. 如果正在刷新，将请求加入队列等待
4. 如果未在刷新，发起刷新请求
5. 刷新成功后更新Token并重发所有队列中的请求
6. 刷新失败则清除Token并跳转登录页

### 4. 登录逻辑更新 (`src/view/login/index.vue`)

修改登录成功处理逻辑，保存完整的Token信息：

```javascript
// 保存完整的token信息
userStore.setTokenInfo({
  accessToken: r.accessToken,
  refreshToken: r.refreshToken,
  expiresTime: r.expiresTime
});
```

### 5. 测试页面 (`src/view/test/token-refresh-test.vue`)

创建了专门的测试页面用于验证Token刷新机制：

**测试功能：**
- 显示当前Token状态
- 测试正常请求和受保护请求
- 手动刷新Token
- 模拟Token过期
- 清除Token

**访问路径：** `/test/token-refresh`

## 技术特点

### 1. 无感知刷新
- 用户在使用过程中不会感知到Token刷新过程
- 请求会自动重试，用户体验流畅

### 2. 请求队列管理
- 刷新期间的所有请求会被暂存在队列中
- 刷新成功后自动重发所有队列中的请求
- 避免了请求丢失和重复发送

### 3. 防重复刷新
- 使用标志位确保同时只有一个刷新请求
- 避免了并发刷新导致的问题

### 4. 智能错误处理
- 区分不同类型的错误消息
- 对于特定错误（如"无效的刷新令牌"）进行静默处理
- 刷新失败时自动跳转登录页

### 5. 兼容性保证
- 保持了与旧版本API的兼容性
- 渐进式升级，不影响现有功能

## 使用方法

### 1. 在组件中使用

```javascript
import { TokenManager } from '@/utils/token';

// 检查Token状态
if (TokenManager.isTokenValid()) {
  // Token有效，执行相关操作
}

// 检查是否需要刷新
if (TokenManager.needsRefresh()) {
  // Token即将过期，可以主动刷新
}
```

### 2. 在API调用中

Token刷新机制会自动工作，无需额外处理：

```javascript
import proHttp from '@/http/proHttp';

// 这个调用会自动处理Token刷新
const response = await proHttp.get('/app-api/some-protected-endpoint');
```

### 3. 登录时保存Token

```javascript
// 登录成功后保存完整Token信息
userStore.setTokenInfo({
  accessToken: response.accessToken,
  refreshToken: response.refreshToken,
  expiresTime: response.expiresTime
});
```

## 配置说明

### 1. Token过期提前时间

默认提前5分钟判断Token即将过期，可在Store中修改：

```javascript
const fiveMinutes = 5 * 60 * 1000; // 5分钟
```

### 2. 忽略的错误消息

可在 `src/http/axios.ts` 中配置需要忽略的错误消息：

```javascript
const ignoreMsgs = [
  '无效的刷新令牌',
  '刷新令牌已过期'
];
```

## 测试验证

1. 访问测试页面：`/test/token-refresh`
2. 查看当前Token状态
3. 测试各种场景：
   - 正常请求
   - 受保护请求
   - 手动刷新Token
   - 模拟Token过期
   - 清除Token

## 注意事项

1. **刷新Token的有效期**: 确保refreshToken的有效期足够长，建议至少7天
2. **网络异常处理**: 网络异常时刷新可能失败，会自动跳转登录页
3. **并发请求**: 多个并发请求在Token过期时会被正确处理，不会重复刷新
4. **安全性**: refreshToken应该安全存储，避免XSS攻击

## 后续优化

1. **主动刷新**: 可以在Token即将过期时主动刷新，而不是等到401错误
2. **刷新策略**: 可以根据用户活跃度调整刷新策略
3. **监控统计**: 添加Token刷新的监控和统计功能
4. **离线处理**: 考虑离线状态下的Token处理策略
