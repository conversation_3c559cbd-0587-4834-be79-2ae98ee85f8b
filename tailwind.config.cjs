// tailwind.config.js
module.exports = {
  content: ['./index.html', './src/**/*.{vue,js,ts,jsx,tsx}'],
  darkMode: 'media', // or 'class'
  theme: {
    extend: {
      colors: {
        // 品牌主色系
        primary: {
          DEFAULT: '#165dff',
          50: '#e8f3ff',
          100: '#d1e7ff',
          200: '#a3cfff',
          300: '#7aa3ff',
          400: '#4080ff',
          500: '#165dff',
          600: '#0f58d3',
          700: '#0a4bb8',
          800: '#083e9d',
          900: '#063182',
        },
        // 辅助色系
        secondary: {
          DEFAULT: '#385087',
          light: '#5a7aa8',
          dark: '#2a3f66',
        },
        // 功能色系
        success: {
          DEFAULT: '#00b42a',
          light: '#23c343',
          dark: '#009a29',
        },
        warning: {
          DEFAULT: '#ff7d00',
          light: '#ff9a33',
          dark: '#d25f00',
        },
        error: {
          DEFAULT: '#f53f3f',
          light: '#f76560',
          dark: '#cb2634',
        },
        info: {
          DEFAULT: '#165dff',
          light: '#4080ff',
          dark: '#0f58d3',
        },
        // 中性色系
        gray: {
          50: '#f7f8fa',
          100: '#f2f3f5',
          200: '#e5e6eb',
          300: '#c9cdd4',
          400: '#a9aeb8',
          500: '#86909c',
          600: '#6b7785',
          700: '#4e5969',
          800: '#272e3b',
          900: '#1d2129',
        },
        // 文本色
        text: {
          primary: '#1d2129',
          secondary: '#4e5969',
          tertiary: '#86909c',
          disabled: '#c9cdd4',
          inverse: '#ffffff',
        },
        // 背景色
        bg: {
          primary: '#ffffff',
          secondary: '#f7f8fa',
          tertiary: '#f2f3f5',
          dark: '#17171a',
        },
        // 边框色
        border: {
          DEFAULT: '#e5e6eb',
          light: '#f2f3f5',
          dark: '#c9cdd4',
        },
      },
    },
  },
  variants: {
    extend: {},
  },
  plugins: [],
};
