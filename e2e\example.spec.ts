import { test, expect } from '@playwright/test';

test.describe('基础页面测试', () => {
  test('应该加载首页并验证标题', async ({ page }) => {
    // 导航到首页
    await page.goto('/');

    // 等待页面加载完成
    await page.waitForLoadState('networkidle');

    // 验证页面标题
    const title = await page.title();
    expect(title).toBeTruthy();

    // 验证页面内容
    await expect(page).toHaveScreenshot({ fullPage: true, maxDiffPixelRatio: 0.1 });
  });

  test('导航应该正确响应', async ({ page }) => {
    // 导航到首页
    await page.goto('/');

    // 等待页面加载
    await page.waitForLoadState('networkidle');

    // 点击导航菜单（如果存在）
    const navExists = await page.$$('nav a, .menu a, .navigation a');
    if (navExists.length > 0) {
      // 点击第一个导航链接
      await navExists[0].click();

      // 等待导航完成
      await page.waitForLoadState('networkidle');

      // 验证URL已经改变
      expect(page.url()).not.toBe(new URL('/', page.url()).toString());
    }
  });
});
