/** @format */

// 支付相关类型定义

// 支付方式类型
export type PaymentMethodType = 'wechat' | 'alipay' | 'points' | 'coupon' | 'balance';

// 订单类型
export type OrderType = 'package' | 'evidence' | 'software' | 'api';

// 支付状态
export type PaymentStatus = 'pending' | 'processing' | 'success' | 'failed' | 'cancelled';

// 订单信息
export interface OrderInfo {
  orderId: string;                    // 订单ID
  orderType: OrderType;               // 订单类型
  productId: string;                  // 产品ID
  productName: string;                // 产品名称
  productDescription: string;         // 产品描述
  productImage?: string;              // 产品图片
  originalAmount: number;             // 原价
  discountAmount: number;             // 优惠金额
  finalAmount: number;                // 最终金额
  createTime: string;                 // 创建时间
  expireTime: string;                 // 过期时间
  userId: string;                     // 用户ID
  status: 'created' | 'paid' | 'cancelled' | 'expired'; // 订单状态
}

// 支付方式信息
export interface PaymentMethod {
  type: PaymentMethodType;            // 支付方式类型
  name: string;                       // 支付方式名称
  icon: string;                       // 图标
  enabled: boolean;                   // 是否可用
  amount?: number;                    // 可用金额/积分
  description?: string;               // 描述信息
  minAmount?: number;                 // 最小支付金额
  maxAmount?: number;                 // 最大支付金额
}

// 优惠券信息
export interface CouponInfo {
  couponId: string;                   // 优惠券ID
  name: string;                       // 优惠券名称
  type: 'discount' | 'amount';        // 优惠券类型：折扣/金额
  value: number;                      // 优惠值（折扣率或金额）
  minOrderAmount: number;             // 最小订单金额
  maxDiscountAmount?: number;         // 最大优惠金额
  expireTime: string;                 // 过期时间
  description: string;                // 描述
  applicable: boolean;                // 是否适用于当前订单
}

// 支付方式选择
export interface PaymentSelection {
  primaryMethod: PaymentMethod;       // 主要支付方式
  secondaryMethods: PaymentMethod[];  // 组合支付方式
  totalAmount: number;                // 总金额
  usedCoupons: CouponInfo[];         // 使用的优惠券
}

// 支付计算结果
export interface PaymentCalculation {
  originalAmount: number;             // 原始金额
  couponDiscount: number;             // 优惠券折扣
  pointsUsed: number;                 // 使用积分
  pointsValue: number;                // 积分价值
  balanceUsed: number;                // 使用余额
  onlinePayAmount: number;            // 在线支付金额
  finalAmount: number;                // 最终需支付金额
  savings: number;                    // 节省金额
}

// 用户资产信息
export interface UserAssets {
  balance: number;                    // 余额
  points: number;                     // 积分
  pointsRate: number;                 // 积分兑换比例（多少积分=1元）
  availableCoupons: CouponInfo[];     // 可用优惠券
}

// 支付方式响应
export interface PaymentMethodsResponse {
  wechatEnabled: boolean;             // 微信支付是否启用
  alipayEnabled: boolean;             // 支付宝支付是否启用
  userAssets: UserAssets;             // 用户资产信息
  paymentMethods: PaymentMethod[];    // 可用支付方式列表
}

// 创建支付请求
export interface CreatePaymentRequest {
  orderId: string;                    // 订单ID
  paymentMethods: {                   // 支付方式组合
    type: PaymentMethodType;
    amount: number;
    couponId?: string;                // 优惠券ID（如果使用）
  }[];
  totalAmount: number;                // 总金额
  clientInfo?: {                      // 客户端信息
    userAgent: string;
    ip: string;
    platform: 'web' | 'mobile';
  };
}

// 支付结果
export interface PaymentResult {
  success: boolean;                   // 是否成功
  paymentId: string;                  // 支付ID
  orderId: string;                    // 订单ID
  qrCode?: string;                    // 二维码支付链接
  redirectUrl?: string;               // 跳转支付链接
  message: string;                    // 结果消息
  paymentStatus: PaymentStatus;       // 支付状态
  transactionId?: string;             // 第三方交易ID
  paidAmount: number;                 // 实际支付金额
  paymentTime?: string;               // 支付时间
}

// 支付状态查询响应
export interface PaymentStatusResponse {
  paymentId: string;                  // 支付ID
  orderId: string;                    // 订单ID
  status: PaymentStatus;              // 支付状态
  paidAmount: number;                 // 已支付金额
  paymentTime?: string;               // 支付时间
  failureReason?: string;             // 失败原因
  refundAmount?: number;              // 退款金额
}

// API响应基础结构
export interface ApiResponse<T = any> {
  code: number;                       // 响应码
  message: string;                    // 响应消息
  data: T;                           // 响应数据
  success: boolean;                   // 是否成功
  timestamp: number;                  // 时间戳
}

// 分页响应
export interface PageResponse<T = any> {
  list: T[];                         // 数据列表
  total: number;                     // 总数
  pageNum: number;                   // 当前页
  pageSize: number;                  // 页大小
  pages: number;                     // 总页数
}

// 错误响应
export interface ErrorResponse {
  code: number;                      // 错误码
  message: string;                   // 错误消息
  details?: string;                  // 错误详情
  timestamp: number;                 // 时间戳
}
