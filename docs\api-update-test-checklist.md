# 后端接口更新测试清单

## 测试前准备

- [ ] 确认后端接口已更新并正常运行
- [ ] 清除浏览器缓存和本地存储
- [ ] 打开浏览器开发者工具准备查看网络请求

## 功能测试

### 1. 页面加载测试

- [ ] 页面能正常加载
- [ ] 数据编号自动生成
- [ ] 所有数据字典选项正常加载
- [ ] 数据资源权属申明选项正常显示

### 2. 表单验证测试

#### 必填字段验证
- [ ] 数据名称为空时显示验证错误
- [ ] 存证单位为空时显示验证错误
- [ ] 数据分类未选择时显示验证错误
- [ ] 数据级别未选择时显示验证错误
- [ ] 数据生成时间未选择时显示验证错误
- [ ] **新增**: 数据资源权属申明未选择时显示验证错误

#### 验证错误信息
- [ ] 错误提示信息清晰明确
- [ ] 错误提示在表单顶部或相应字段附近显示
- [ ] 修复错误后验证提示消失

### 3. 数据资源权属申明测试

- [ ] 字段显示必填标识（红色星号）
- [ ] 可以选择单个选项
- [ ] 可以选择多个选项
- [ ] 取消选择后字段为空
- [ ] 选择后能正常提交

### 4. 文件上传测试

- [ ] 数据样本文件只能上传1个
- [ ] 数据来源佐证材料只能上传1个
- [ ] 有效控制措施佐证材料只能上传1个
- [ ] 个人信息采集佐证材料只能上传1个
- [ ] 文件上传成功后状态正确
- [ ] 文件路径正确收集

### 5. 表单提交测试

#### 成功提交测试
- [ ] 填写所有必填字段
- [ ] 选择数据资源权属申明
- [ ] 点击提交按钮
- [ ] 查看网络请求是否使用 query 参数
- [ ] 确认请求参数完整正确
- [ ] 提交成功后显示成功消息
- [ ] 表单自动重置

#### 失败提交测试
- [ ] 缺少必填字段时提交失败
- [ ] 网络错误时显示错误信息
- [ ] 后端返回错误时显示相应消息

## 网络请求验证

### 1. 请求格式检查

打开浏览器开发者工具 → Network 标签，提交表单后检查：

- [ ] 请求方法为 POST
- [ ] 请求URL为 `/app-api/bisness/certificate-info/create`
- [ ] 参数在 Query String 中而不是 Request Body
- [ ] 所有字段都正确传递

### 2. 关键参数检查

确认以下参数在请求中存在且不为空：

- [ ] `dataNumber` - 数据编号
- [ ] `dataName` - 数据名称
- [ ] `certificationUnit` - 存证单位
- [ ] `dataClassification` - 数据分类
- [ ] `dataLevel` - 数据级别
- [ ] `dataGenerationTime` - 数据生成时间（ISO格式）
- [ ] `dataResourceOwnership` - 数据资源权属申明（必填，不为空）

### 3. 文件路径参数检查

如果上传了文件，确认以下参数包含文件路径：

- [ ] `dataSampleFile` - 数据样本文件路径
- [ ] `dataSourceEvidenceMaterial` - 数据来源佐证材料路径
- [ ] `effectiveControlEvidenceMaterial` - 有效控制措施佐证材料路径
- [ ] `personalInfoCollectionEvidenceMaterial` - 个人信息采集佐证材料路径

## 调试工具测试

### 1. 控制台调试

在浏览器控制台执行：

```javascript
// 检查文件状态
window.debugFileStatus()
```

- [ ] 函数正常执行
- [ ] 返回当前文件状态信息
- [ ] 文件路径收集结果正确

### 2. 日志检查

查看控制台日志，确认：

- [ ] 文件上传过程有详细日志
- [ ] 文件状态变化有记录
- [ ] 提交过程有完整日志
- [ ] 没有错误或警告信息

## 边界情况测试

### 1. 特殊字符测试

- [ ] 数据名称包含特殊字符
- [ ] 描述字段包含换行符
- [ ] 中文字符正确处理

### 2. 大文件测试

- [ ] 上传接近大小限制的文件
- [ ] 上传超过大小限制的文件（应该失败）

### 3. 网络异常测试

- [ ] 断网情况下的错误处理
- [ ] 请求超时的处理
- [ ] 服务器错误的处理

## 用户体验测试

### 1. 界面响应

- [ ] 提交按钮显示加载状态
- [ ] 表单验证实时响应
- [ ] 错误信息及时显示

### 2. 操作流程

- [ ] 表单填写流程顺畅
- [ ] 文件上传操作简单
- [ ] 错误修复容易理解

### 3. 数据持久化

- [ ] 暂存功能正常工作
- [ ] 页面刷新后数据恢复
- [ ] 提交成功后暂存数据清除

## 回归测试

### 1. 原有功能

- [ ] 数据编号生成功能
- [ ] 数据字典加载功能
- [ ] 表单重置功能
- [ ] 暂存恢复功能

### 2. 文件上传功能

- [ ] 文件类型验证
- [ ] 文件大小验证
- [ ] 上传进度显示
- [ ] 上传错误处理

## 测试结果记录

### 通过的测试项

- [ ] 记录通过的测试项数量
- [ ] 记录测试时间和环境

### 发现的问题

- [ ] 记录发现的问题
- [ ] 记录问题的严重程度
- [ ] 记录修复方案

### 测试总结

- [ ] 整体功能是否正常
- [ ] 是否可以发布到生产环境
- [ ] 需要注意的事项

## 测试签名

- 测试人员：___________
- 测试日期：___________
- 测试环境：___________
- 测试结果：□ 通过 □ 不通过
- 备注：_______________
