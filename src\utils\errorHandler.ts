import { App } from 'vue';
import { Message } from '@arco-design/web-vue';

interface ErrorInfo {
  type: string;
  message: string;
  stack?: string;
  info?: string;
  url?: string;
  time?: string;
}

// 错误类型枚举
export enum ErrorTypeEnum {
  VUE = 'vue',
  SCRIPT = 'script',
  RESOURCE = 'resource',
  AJAX = 'ajax',
  PROMISE = 'promise',
}

// 错误日志收集配置
const errorLogConfig = {
  // 是否开启错误捕获
  enabled: import.meta.env.PROD, // 生产环境开启
  // 是否向服务器发送错误日志
  reportToServer: import.meta.env.PROD, // 生产环境发送
  // 错误日志上报地址
  reportUrl: '/api/error/report',
  // 要收集的错误类型
  errorTypes: [
    ErrorTypeEnum.VUE,
    ErrorTypeEnum.SCRIPT,
    ErrorTypeEnum.RESOURCE,
    ErrorTypeEnum.AJAX,
    ErrorTypeEnum.PROMISE,
  ],
};

// 上报错误信息到服务器
function reportErrorToServer(error: ErrorInfo) {
  if (errorLogConfig.reportToServer) {
    // 这里可以使用你的HTTP模块发送错误信息
    // Http.post(errorLogConfig.reportUrl, error).catch(() => {});
    console.log('Error reported to server:', error);
  }
}

// 收集错误信息
function collectErrorInfo(error: Error, type: ErrorTypeEnum, info?: string): ErrorInfo {
  const errorInfo: ErrorInfo = {
    type,
    message: error.message,
    stack: error.stack,
    info: info,
    url: window.location.href,
    time: new Date().toISOString(),
  };

  // 上报错误
  reportErrorToServer(errorInfo);

  // 控制台输出错误
  console.error(`[Error] ${type}:`, errorInfo);

  return errorInfo;
}

// 全局错误处理
export function setupErrorHandle(app: App) {
  if (!errorLogConfig.enabled) return;

  // Vue应用内部错误
  app.config.errorHandler = (error: unknown, instance, info) => {
    if (error instanceof Error) {
      collectErrorInfo(error, ErrorTypeEnum.VUE, info);
      // 对于Vue应用内部错误，可以在这里选择显示错误提示
      Message.error('应用发生错误，请刷新页面重试');
    }
  };

  // 脚本错误
  window.onerror = (message, source, lineno, colno, error) => {
    if (error instanceof Error) {
      collectErrorInfo(error, ErrorTypeEnum.SCRIPT);
    }
    return true;
  };

  // 未处理的Promise错误
  window.addEventListener('unhandledrejection', event => {
    const error = new Error(String(event.reason));
    collectErrorInfo(error, ErrorTypeEnum.PROMISE);
  });

  // 资源加载错误
  window.addEventListener(
    'error',
    event => {
      const target = event.target;
      let resourceUrl = '';

      // 根据不同类型的元素获取资源URL
      if (target instanceof HTMLImageElement) {
        resourceUrl = target.src;
      } else if (target instanceof HTMLScriptElement) {
        resourceUrl = target.src;
      } else if (target instanceof HTMLLinkElement) {
        resourceUrl = target.href;
      }

      if (resourceUrl) {
        const message = `资源加载失败: ${resourceUrl}`;
        const error = new Error(message);
        collectErrorInfo(error, ErrorTypeEnum.RESOURCE);
      }
    },
    true
  );
}

export default setupErrorHandle;
