<template>
  <!-- 关于我们 -->
  <div style="background-color: #1c1f24">
    <div class="flex items-start py-10 layout-content">
      <div class="flex flex-grow justify-center">
        <a-space :size="100" :align="'start'">
          <ul v-for="(item, index) in aboutUsList" :key="index" class="text-center">
            <li class="font-semibold cursor-pointer text-gray-300" style="padding: 4px">
              {{ item.title }}
            </li>
            <template v-for="(ele, inx) in item.content" :key="inx">
              <router-link v-if="ele.path" :to="ele.path" :title="ele.title">
                <li class="text-sm text-gray-400 cursor-pointer" style="padding: 4px">
                  <span>{{ ele.title }}</span>
                </li>
              </router-link>
              <a-tooltip
                v-else
                content="即将开放"
                background-color="#ffffff"
                :content-style="{ color: '#303133' }"
              >
                <li class="text-sm text-gray-400 cursor-pointer" style="padding: 4px">
                  <span>{{ ele.title }}</span>
                </li>
              </a-tooltip>
            </template>
          </ul>
        </a-space>
      </div>
      <div style="min-width: 530px" class="flex">
        <div style="border-left: 1px solid #6c727f" />
        <div class="flex ml-20">
          <div class="flex flex-col items-start mr-10">
            <div class="text-base text-gray-300">联系电话</div>
            <div class="text-sm text-gray-400">
              <a href="tel:028-64058443" title="联系我们" class="text-gray-400 hover:text-gray-300"
                >028-6405 8443</a
              >
            </div>
            <div class="text-base mt-2 text-gray-300">电子邮箱</div>
            <div class="text-sm text-gray-400">
              <a
                href="mailto:<EMAIL>"
                title="发送邮件"
                class="text-gray-400 hover:text-gray-300"
                ><EMAIL></a
              >
            </div>
            <div class="text-base mt-2 text-gray-300">工作时间</div>
            <div class="text-sm text-gray-400">
              9:00-18:00 <span class="text-sm">（工作日）</span>
            </div>
          </div>
          <img
            style="width: 120px; height: 120px"
            class="ml-8"
            src="@/assets/images/qrCode.jpg"
            alt="文创链开放平台二维码"
          />
        </div>
      </div>
    </div>
  </div>
  <a-layout-footer style="background-color: #1c1f24" class="text-center pt-6 pb-6 text-gray-400">
    <div itemscope itemtype="http://schema.org/Organization">
      <span itemprop="name">成都九天星空科技有限公司</span>
      <div itemprop="address" itemscope itemtype="http://schema.org/PostalAddress" class="hidden">
        <span itemprop="addressLocality">成都市</span>
        <span itemprop="streetAddress">高新区</span>
      </div>
      <span itemprop="telephone" class="hidden">028-6405 8443</span>
      <span itemprop="email" class="hidden"><EMAIL></span>
    </div>
    Copyright © 2023 成都九天星空科技有限公司 |
    <a
      href="https://beian.miit.gov.cn/"
      target="_blank"
      rel="noopener"
      title="工信部备案查询"
      class="text-gray-400 hover:text-gray-300"
    >
      网站备案号：蜀ICP备20000718号-9
    </a>
  </a-layout-footer>
</template>

<script lang="ts" setup>
  import { reactive } from 'vue';
  // 关于我们
  const aboutUsList = reactive([
    {
      title: '新手入门',
      content: [
        {
          title: '平台介绍',
          path: '/home',
        },
        {
          title: '接口文档',
          path: '/apiDoc',
        },
      ],
    },
    {
      title: '服务支持',
      content: [
        {
          title: '常见FAQ',
          path: '',
        },
        {
          title: '平台规则',
          path: '',
        },
      ],
    },
    {
      title: '客服帮助',
      content: [
        {
          title: '社区提问',
          path: '',
        },
        {
          title: '服务热线',
          path: '',
        },
      ],
    },
  ]);
</script>

<style lang="scss" scoped>
  .footer {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 40px;
    color: var(--color-text-2);
    text-align: center;
  }

  .hidden {
    display: none;
  }
</style>
