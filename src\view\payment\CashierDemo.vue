<template>
  <div class="cashier-demo">
    <h1>收银台页面演示</h1>
    <p>这是改造后的收银台页面，按照新的布局要求：</p>
    <ol>
      <li>最上面是选择服务类型</li>
      <li>中间是选择优惠券</li>
      <li>下面是选择支付方式（在线支付、钱包余额、积分支付）</li>
      <li>最后提交确认</li>
    </ol>
    
    <div class="demo-container">
      <!-- 服务类型选择 -->
      <ServiceTypeSelector
        :loading="false"
        :selected-type="selectedServiceType"
        @select="handleServiceTypeSelect"
        @change="handleServiceTypeChange"
      />

      <!-- 优惠券选择 -->
      <CouponSelector
        :available-coupons="mockCoupons"
        :selected-coupons="selectedCoupons"
        :order-amount="orderAmount"
        :loading="false"
        @select="handleCouponSelect"
        @remove="handleCouponRemove"
      />

      <!-- 支付方式选择 -->
      <PaymentMethodSelector
        :payment-methods="mockPaymentMethods"
        :user-assets="mockUserAssets"
        :order-amount="finalAmount"
        :loading="false"
        @payment-change="handlePaymentMethodChange"
        @amount-change="handleAmountChange"
      />

      <!-- 支付确认 -->
      <PaymentConfirmation
        :final-amount="finalAmount"
        :savings="savings"
        :submitting="false"
        :show-cancel-button="true"
        @submit="handleSubmitPayment"
        @cancel="handleCancelPayment"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref, computed } from 'vue';
  import { Message } from '@arco-design/web-vue';
  import ServiceTypeSelector from './components/ServiceTypeSelector.vue';
  import CouponSelector from './components/CouponSelector.vue';
  import PaymentMethodSelector from './components/PaymentMethodSelector.vue';
  import PaymentConfirmation from './components/PaymentConfirmation.vue';
  import type { CouponInfo, PaymentMethod, UserAssets } from '@/http/api/payment/types';

  // 状态
  const selectedServiceType = ref('');
  const selectedCoupons = ref<CouponInfo[]>([]);
  const selectedPaymentMethods = ref<PaymentMethod[]>([]);
  const orderAmount = ref(99.00);

  // 模拟数据
  const mockCoupons: CouponInfo[] = [
    {
      couponId: 'coupon_001',
      name: '新用户专享券',
      type: 'amount',
      value: 10,
      minOrderAmount: 50,
      expireTime: '2024-12-31T23:59:59Z',
      description: '新用户专享立减券',
      applicable: true,
    },
    {
      couponId: 'coupon_002',
      name: '九折优惠券',
      type: 'discount',
      value: 0.9,
      minOrderAmount: 100,
      maxDiscountAmount: 20,
      expireTime: '2024-12-31T23:59:59Z',
      description: '全场九折优惠券',
      applicable: false,
    },
  ];

  const mockPaymentMethods: PaymentMethod[] = [
    {
      type: 'wechat',
      name: '微信支付',
      icon: 'wechat',
      enabled: true,
      description: '使用微信扫码支付',
    },
    {
      type: 'alipay',
      name: '支付宝',
      icon: 'alipay',
      enabled: true,
      description: '使用支付宝扫码支付',
    },
  ];

  const mockUserAssets: UserAssets = {
    balance: 50.00,
    points: 1000,
    pointsRate: 100,
    availableCoupons: mockCoupons,
  };

  // 计算属性
  const couponDiscount = computed(() => {
    return selectedCoupons.value.reduce((total, coupon) => {
      if (coupon.type === 'amount') {
        return total + coupon.value;
      } else {
        const discount = orderAmount.value * (1 - coupon.value);
        return total + Math.min(discount, coupon.maxDiscountAmount || discount);
      }
    }, 0);
  });

  const finalAmount = computed(() => {
    return Math.max(0, orderAmount.value - couponDiscount.value);
  });

  const savings = computed(() => {
    return couponDiscount.value;
  });

  // 事件处理
  function handleServiceTypeSelect(serviceType: any) {
    console.log('选择服务类型:', serviceType);
    Message.success(`已选择服务类型: ${serviceType.name}`);
  }

  function handleServiceTypeChange(serviceTypeValue: string) {
    selectedServiceType.value = serviceTypeValue;
    console.log('服务类型变化:', serviceTypeValue);
  }

  function handleCouponSelect(coupon: CouponInfo) {
    const existingIndex = selectedCoupons.value.findIndex(c => c.couponId === coupon.couponId);
    
    if (existingIndex >= 0) {
      selectedCoupons.value.splice(existingIndex, 1);
      Message.info(`已取消选择优惠券: ${coupon.name}`);
    } else {
      selectedCoupons.value.push(coupon);
      Message.success(`已选择优惠券: ${coupon.name}`);
    }
  }

  function handleCouponRemove(couponId: string) {
    const coupon = selectedCoupons.value.find(c => c.couponId === couponId);
    selectedCoupons.value = selectedCoupons.value.filter(c => c.couponId !== couponId);
    if (coupon) {
      Message.info(`已移除优惠券: ${coupon.name}`);
    }
  }

  function handlePaymentMethodChange(methods: PaymentMethod[]) {
    selectedPaymentMethods.value = methods;
    console.log('支付方式变化:', methods);
  }

  function handleAmountChange(amount: number) {
    console.log('支付金额变化:', amount);
  }

  function handleSubmitPayment() {
    Message.success('支付提交成功！');
    console.log('提交支付:', {
      serviceType: selectedServiceType.value,
      coupons: selectedCoupons.value,
      paymentMethods: selectedPaymentMethods.value,
      finalAmount: finalAmount.value,
    });
  }

  function handleCancelPayment() {
    Message.info('已取消支付');
  }
</script>

<style scoped lang="less">
  .cashier-demo {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;

    h1 {
      color: #262626;
      margin-bottom: 16px;
    }

    p {
      color: #8c8c8c;
      margin-bottom: 12px;
    }

    ol {
      color: #8c8c8c;
      margin-bottom: 32px;
      padding-left: 20px;

      li {
        margin-bottom: 8px;
      }
    }

    .demo-container {
      display: flex;
      flex-direction: column;
      gap: 24px;
    }
  }
</style>
