<template>
  <div style="background-color: #232324; position: relative">
    <router-link to="/home">
      <a-image
        style="position: absolute; top: 15px; left: 148px; z-index: 3"
        width="370.8"
        :src="imgUrl"
      />
    </router-link>
    <div class="layout-content flex justify-between items-center">
      <a-menu mode="horizontal" theme="dark">
        <router-link to="/home">
          <a-menu-item key="1"> 首页 </a-menu-item>
        </router-link>
        <router-link to="/shop/search">
          <a-menu-item key="2"> 数据超市 </a-menu-item>
        </router-link>
        <router-link to="/user/followRecord">
          <a-menu-item key="3"> 我的关注 </a-menu-item>
        </router-link>
        <router-link to="/user/browseRecords">
          <a-menu-item key="4"> 历史浏览 </a-menu-item>
        </router-link>
        <router-link to="/user/tradeList">
          <a-menu-item key="5"> 交易列表 </a-menu-item>
        </router-link>
        <router-link to="/user/assetManage">
          <a-menu-item key="6"> 资产管理 </a-menu-item>
        </router-link>
      </a-menu>
      <a-dropdown trigger="hover">
        <a-avatar style="margin-left: 10px" :size="32">
          <img alt="avatar" :src="user_info.avatar" />
        </a-avatar>
        <template #content>
          <router-link to="/login">
            <a-doption>
              <template #icon>
                <icon-poweroff />
              </template>
              <template #default> 退出登录 </template>
            </a-doption>
          </router-link>
        </template>
      </a-dropdown>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { ref, reactive, onMounted, computed } from 'vue';
  import { storeToRefs } from 'pinia';
  import { useUserStore } from '@/store/index';
  import { getAssetsFile } from '@/utils/tool';
  import { IconPoweroff } from '@arco-design/web-vue/es/icon';

  const userStore = useUserStore();
  const { user_info } = storeToRefs(userStore);
  const ifLogin = computed(() => (user_info.value ? true : false));

  let imgUrl = getAssetsFile('logo-1.png');
</script>
