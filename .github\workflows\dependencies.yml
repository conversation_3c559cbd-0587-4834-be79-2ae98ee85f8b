name: Update Dependencies

on:
  schedule:
    - cron: '0 0 * * 1'  # 每周一运行
  workflow_dispatch:     # 允许手动触发

jobs:
  update-deps:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
          
      - name: Setup PNPM
        uses: pnpm/action-setup@v2
        with:
          version: 8.7.0
          
      - name: Update dependencies
        run: |
          pnpm update --latest
          
      - name: Create Pull Request
        uses: peter-evans/create-pull-request@v5
        with:
          token: ${{ secrets.GITHUB_TOKEN }}
          commit-message: 'chore(deps): update dependencies'
          title: '🤖 依赖更新'
          body: |
            自动依赖更新
            
            该PR由自动化工作流生成，用于更新依赖包到最新版本。
            请在合并前仔细测试。
          branch: deps-update
          base: main
          labels: dependencies,automated pr 