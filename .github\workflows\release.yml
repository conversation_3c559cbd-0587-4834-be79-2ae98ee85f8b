name: Release

on:
  push:
    tags:
      - 'v*'

jobs:
  release:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
        with:
          fetch-depth: 0

      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'

      - name: Setup PNPM
        uses: pnpm/action-setup@v2
        with:
          version: 8.7.0

      - name: Install dependencies
        run: pnpm install --frozen-lockfile

      - name: Build
        run: pnpm run build:release

      - name: Generate changelog
        id: changelog
        uses: conventional-changelog/conventional-changelog-action@v4
        with:
          config-file: ./changelog-config.json
          github-token: ${{ secrets.GITHUB_TOKEN }}

      - name: Create release
        uses: softprops/action-gh-release@v1
        with:
          files: |
            dist.rel.*.zip
          body: ${{ steps.changelog.outputs.release_notes }}
          draft: false
          prerelease: false
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
