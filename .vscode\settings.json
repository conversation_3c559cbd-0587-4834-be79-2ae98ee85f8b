{"editor.formatOnSave": true, "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit"}, "editor.defaultFormatter": "esbenp.prettier-vscode", "[vue]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[typescript]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[javascript]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "typescript.tsdk": "node_modules/typescript/lib", "eslint.validate": ["javascript", "javascriptreact", "typescript", "typescriptreact", "vue"], "cSpell.words": ["arco", "bcpc", "commitlint", "esbuild", "pinia", "tailwindcss", "unplugin", "vite", "vitest", "vueuse"], "i18n-ally.localesPaths": ["src/i18n/locales"], "i18n-ally.keystyle": "nested", "css.validate": false, "explorer.fileNesting.enabled": true, "explorer.fileNesting.expand": false, "explorer.fileNesting.patterns": {".env": ".env.*"}}