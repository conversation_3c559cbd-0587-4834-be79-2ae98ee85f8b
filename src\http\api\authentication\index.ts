import http from "@/http/sopHttp";

// 获取公钥
export function userInfoSave(data: any) {
  return http.put("/portal/user/info/save", data);
}

export function basicType() {
  return http.get("/portal/user/company/basicType");
}

export function getUserFile() {
  return http.get("/portal/user/file");
}

export function portalUserInfo() {
  return http.get("/portal/user/info");
}

export function getBranchBankList(data: any) {
  return http.get("/portal/user/getBranchBankList", data);
}

// 用户文件上传
export function fileUpload(data: any) {
  return http.formDataPost("/portal/user/file/upload", data);
}

// 用户文件上传
export function userInfoSubmit(data: any) {
  return http.put("/portal/user/info/submit", data);
}