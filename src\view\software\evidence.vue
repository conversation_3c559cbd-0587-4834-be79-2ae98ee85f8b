<template>
  <div class="page-container">
    <div class="page-header">
      <h1 class="page-title">软件著作权存证</h1>
      <p class="page-description">通过区块链技术为您的软件作品提供可信存证服务，保障软件开发权益</p>
    </div>

    <div class="content-wrapper">
      <a-steps :current="currentStep" class="mb-8">
        <a-step title="填写基本信息" />
        <a-step title="上传软件材料" />
        <a-step title="提交存证申请" />
        <a-step title="完成" />
      </a-steps>

      <a-card class="form-card">
        <div v-if="currentStep === 0">
          <h2 class="form-section-title">基本信息</h2>
          <a-form :model="formData" layout="vertical">
            <a-form-item field="softwareName" label="软件名称" required>
              <a-input v-model="formData.softwareName" placeholder="请输入软件全称" />
            </a-form-item>
            <a-form-item field="softwareShortName" label="软件简称">
              <a-input v-model="formData.softwareShortName" placeholder="请输入软件简称（可选）" />
            </a-form-item>
            <a-form-item field="softwareVersion" label="软件版本号" required>
              <a-input v-model="formData.softwareVersion" placeholder="如：V1.0、2.1.3等" />
            </a-form-item>
            <a-form-item field="completionDate" label="开发完成日期" required>
              <a-date-picker v-model="formData.completionDate" style="width: 100%" />
            </a-form-item>
            <a-form-item field="developmentType" label="开发方式" required>
              <a-select v-model="formData.developmentType" placeholder="请选择开发方式">
                <a-option value="original"> 原始开发 </a-option>
                <a-option value="commissioned"> 委托开发 </a-option>
                <a-option value="joint"> 合作开发 </a-option>
                <a-option value="adaptation"> 修改、改编 </a-option>
              </a-select>
            </a-form-item>
            <a-form-item field="copyrightOwners" label="著作权人" required>
              <a-input
                v-model="formData.copyrightOwners"
                placeholder="请输入著作权人（多个请用逗号分隔）"
              />
            </a-form-item>
            <a-form-item field="functionalDescription" label="功能简述" required>
              <a-textarea
                v-model="formData.functionalDescription"
                placeholder="请简要描述软件的主要功能和用途"
              />
            </a-form-item>
          </a-form>
          <div class="step-actions">
            <a-button type="primary" @click="nextStep"> 下一步 </a-button>
          </div>
        </div>

        <div v-if="currentStep === 1">
          <h2 class="form-section-title">上传软件材料</h2>
          <p class="upload-description">请上传以下材料用于存证：</p>

          <a-form :model="formData" layout="vertical">
            <a-form-item field="sourceCode" label="源代码" required>
              <a-upload
                action="/"
                :custom-request="handleFileUpload"
                :file-list="formData.sourceCodeFiles"
                accept=".zip,.rar,.7z"
                @change="handleSourceCodeChange"
              >
                <template #upload-button>
                  <a-button>
                    <template #icon>
                      <icon-upload />
                    </template>
                    上传源代码（压缩包）
                  </a-button>
                </template>
                <template #extra>
                  <div class="upload-tip">支持ZIP、RAR、7Z等压缩格式，大小不超过100MB</div>
                </template>
              </a-upload>
            </a-form-item>

            <a-form-item field="documentationFiles" label="软件说明文档" required>
              <a-upload
                action="/"
                :custom-request="handleFileUpload"
                :file-list="formData.documentationFiles"
                accept=".doc,.docx,.pdf"
                @change="handleDocumentationChange"
              >
                <template #upload-button>
                  <a-button>
                    <template #icon>
                      <icon-upload />
                    </template>
                    上传说明文档
                  </a-button>
                </template>
                <template #extra>
                  <div class="upload-tip">支持DOC、DOCX、PDF格式，大小不超过20MB</div>
                </template>
              </a-upload>
            </a-form-item>

            <a-form-item field="screenshots" label="软件截图">
              <a-upload
                action="/"
                list-type="picture-card"
                :custom-request="handleFileUpload"
                :file-list="formData.screenshotFiles"
                accept=".jpg,.jpeg,.png"
                @change="handleScreenshotsChange"
              >
                <template #upload-button>
                  <div>
                    <icon-plus />
                    <div>上传截图</div>
                  </div>
                </template>
              </a-upload>
              <div class="upload-tip">支持JPG、PNG格式，至少3张</div>
            </a-form-item>
          </a-form>

          <div class="step-actions">
            <a-button @click="prevStep"> 上一步 </a-button>
            <a-button type="primary" :disabled="!canProceedToStep2" @click="nextStep">
              下一步
            </a-button>
          </div>
        </div>

        <div v-if="currentStep === 2">
          <h2 class="form-section-title">提交存证申请</h2>
          <p class="confirm-description">请确认以下信息无误，存证成功后将生成区块链存证凭证。</p>

          <a-descriptions
            title="软件基本信息"
            :column="1"
            :data="[
              { label: '软件名称', value: formData.softwareName },
              { label: '软件简称', value: formData.softwareShortName || '无' },
              { label: '软件版本号', value: formData.softwareVersion },
              {
                label: '开发完成日期',
                value: formData.completionDate ? formData.completionDate.toString() : '未设置',
              },
              { label: '开发方式', value: getDevelopmentTypeText(formData.developmentType) },
              { label: '著作权人', value: formData.copyrightOwners },
              { label: '功能简述', value: formData.functionalDescription },
            ]"
          />

          <a-descriptions
            title="上传材料"
            :column="1"
            :data="[
              { label: '源代码', value: getFileListText(formData.sourceCodeFiles) },
              { label: '软件说明文档', value: getFileListText(formData.documentationFiles) },
              { label: '软件截图', value: `已上传 ${formData.screenshotFiles.length} 张` },
            ]"
          />

          <a-alert type="info" class="mt-4">
            <template #message>
              <p>提交后，系统将对您的软件材料进行存证处理，生成区块链存证凭证。</p>
              <p>存证凭证可作为软件著作权归属的技术依据。</p>
            </template>
          </a-alert>

          <a-form :model="formData" layout="vertical" class="mt-4">
            <a-form-item field="agreement">
              <a-checkbox v-model="formData.agreement">
                我已阅读并同意<a href="javascript:void(0)">《存证服务协议》</a
                >，承诺所提供的信息真实有效。
              </a-checkbox>
            </a-form-item>
          </a-form>

          <div class="step-actions">
            <a-button @click="prevStep"> 上一步 </a-button>
            <a-button
              type="primary"
              :loading="submitting"
              :disabled="!formData.agreement"
              @click="submitEvidence"
            >
              提交存证
            </a-button>
          </div>
        </div>

        <div v-if="currentStep === 3">
          <div class="success-container">
            <icon-check-circle class="success-icon" />
            <h2 class="success-title">存证申请提交成功！</h2>
            <p class="success-message">您的软件存证申请已成功提交，存证凭证正在生成中...</p>
            <div class="success-info">
              <p>
                <span class="label">申请编号：</span><span class="value">{{ evidenceId }}</span>
              </p>
              <p>
                <span class="label">提交时间：</span><span class="value">{{ submissionTime }}</span>
              </p>
              <p><span class="label">预计完成：</span><span class="value">约15分钟</span></p>
            </div>
            <a-alert type="info" class="mt-4">
              <template #message>
                存证凭证生成后，我们将通过系统消息通知您。您也可以在"个人中心-我的存证"中查看存证进度和结果。
              </template>
            </a-alert>
            <div class="success-actions">
              <a-button type="primary" @click="goToList"> 查看我的存证 </a-button>
              <a-button @click="resetForm"> 继续添加存证 </a-button>
            </div>
          </div>
        </div>
      </a-card>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref, reactive, computed } from 'vue';
  import { useRouter } from 'vue-router';
  import { Message } from '@arco-design/web-vue';
  import { IconUpload, IconPlus, IconCheckCircle } from '@arco-design/web-vue/es/icon';
  import { createSoftwareRegistrationInfo } from '@/http/api/software/index';

  // 定义文件项接口
  interface FileItem {
    name: string;
    url?: string;
    status?: 'init' | 'uploading' | 'done' | 'error';
    uid: string;
    [key: string]: any;
  }

  // 定义表单数据接口
  interface SoftwareEvidenceForm {
    softwareName: string;
    softwareShortName: string;
    softwareVersion: string;
    completionDate: string | number | Date | undefined;
    developmentType: string;
    copyrightOwners: string;
    functionalDescription: string;
    sourceCodeFiles: FileItem[];
    documentationFiles: FileItem[];
    screenshotFiles: FileItem[];
    agreement: boolean;
  }

  const router = useRouter();
  const currentStep = ref(0);
  const submitting = ref(false);
  const evidenceId = ref('');
  const submissionTime = ref('');

  // 表单数据
  const formData = reactive<SoftwareEvidenceForm>({
    softwareName: '',
    softwareShortName: '',
    softwareVersion: '',
    completionDate: undefined,
    developmentType: '',
    copyrightOwners: '',
    functionalDescription: '',
    sourceCodeFiles: [],
    documentationFiles: [],
    screenshotFiles: [],
    agreement: false,
  });

  // 检查是否可以进行到下一步
  const canProceedToStep2 = computed(() => {
    return formData.sourceCodeFiles.length > 0 && formData.documentationFiles.length > 0;
  });

  // 获取开发方式的文本描述
  const getDevelopmentTypeText = (type: string): string => {
    const typeMap: Record<string, string> = {
      original: '原始开发',
      commissioned: '委托开发',
      joint: '合作开发',
      adaptation: '修改、改编',
    };
    return typeMap[type] || type;
  };

  // 获取文件列表的文本描述
  const getFileListText = (fileList: FileItem[]): string => {
    if (!fileList || fileList.length === 0) {
      return '未上传';
    }
    return fileList.map(file => file.name).join(', ');
  };

  // 处理文件上传
  const handleFileUpload = (option: any) => {
    // 这里只是模拟上传，实际应用中需要对接后端上传接口
    setTimeout(() => {
      option.onSuccess();
    }, 1000);

    return {
      abort: () => {},
    };
  };

  // 处理源代码文件变更
  const handleSourceCodeChange = (fileList: any[]) => {
    formData.sourceCodeFiles = fileList;
  };

  // 处理文档文件变更
  const handleDocumentationChange = (fileList: any[]) => {
    formData.documentationFiles = fileList;
  };

  // 处理截图文件变更
  const handleScreenshotsChange = (fileList: any[]) => {
    formData.screenshotFiles = fileList;
  };

  // 下一步
  const nextStep = () => {
    if (currentStep.value === 0) {
      // 验证第一步表单
      if (
        !formData.softwareName ||
        !formData.softwareVersion ||
        !formData.completionDate ||
        !formData.developmentType ||
        !formData.copyrightOwners ||
        !formData.functionalDescription
      ) {
        Message.error('请填写所有必填项');
        return;
      }
    } else if (currentStep.value === 1) {
      // 验证第二步表单
      if (!canProceedToStep2.value) {
        Message.error('请上传源代码和软件说明文档');
        return;
      }
    }

    currentStep.value += 1;
  };

  // 上一步
  const prevStep = () => {
    currentStep.value -= 1;
  };

  // 提交存证
  const submitEvidence = async () => {
    if (!formData.agreement) {
      Message.error('请先同意《存证服务协议》');
      return;
    }

    submitting.value = true;

    try {
      // 实际应用中这里调用后端API
      // const response = await createSoftwareRegistrationInfo(formData);

      // 模拟成功响应
      evidenceId.value = 'EV' + new Date().getTime();
      submissionTime.value = new Date().toLocaleString();

      Message.success('存证申请提交成功');
      currentStep.value = 3;
    } catch (error) {
      console.error('提交存证失败:', error);
      Message.error('提交失败，请稍后重试');
    } finally {
      submitting.value = false;
    }
  };

  // 查看存证列表
  const goToList = () => {
    router.push('/software/page/evidence-list');
  };

  // 重置表单开始新的存证
  const resetForm = () => {
    Object.keys(formData).forEach(key => {
      if (Array.isArray(formData[key as keyof SoftwareEvidenceForm])) {
        (formData[key as keyof SoftwareEvidenceForm] as any) = [];
      } else if (typeof formData[key as keyof SoftwareEvidenceForm] === 'boolean') {
        (formData[key as keyof SoftwareEvidenceForm] as any) = false;
      } else {
        (formData[key as keyof SoftwareEvidenceForm] as any) = '';
      }
    });

    formData.completionDate = undefined;
    currentStep.value = 0;
  };
</script>

<style lang="scss" scoped>
  .page-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 40px 20px;
  }

  .page-header {
    text-align: center;
    margin-bottom: 40px;

    .page-title {
      font-size: 2rem;
      font-weight: bold;
      margin-bottom: 12px;
      color: #1d2129;
    }

    .page-description {
      font-size: 1.1rem;
      color: #4e5969;
    }
  }

  .content-wrapper {
    background: #fff;
    border-radius: 8px;
  }

  .form-card {
    margin-bottom: 40px;
  }

  .form-section-title {
    font-size: 1.5rem;
    margin-bottom: 24px;
    color: #1d2129;
    font-weight: 500;
  }

  .upload-description,
  .confirm-description {
    margin-bottom: 20px;
    color: #4e5969;
  }

  .upload-tip {
    font-size: 0.9rem;
    color: #86909c;
    margin-top: 4px;
  }

  .step-actions {
    display: flex;
    justify-content: space-between;
    margin-top: 30px;

    a-button + a-button {
      margin-left: 12px;
    }
  }

  .success-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 40px 0;

    .success-icon {
      font-size: 64px;
      color: #00b42a;
      margin-bottom: 20px;
    }

    .success-title {
      font-size: 1.8rem;
      font-weight: bold;
      color: #1d2129;
      margin-bottom: 12px;
    }

    .success-message {
      font-size: 1.1rem;
      color: #4e5969;
      margin-bottom: 24px;
    }

    .success-info {
      background: #f2f3f5;
      padding: 16px 24px;
      border-radius: 4px;
      width: 100%;
      max-width: 500px;
      margin-bottom: 24px;

      p {
        margin-bottom: 8px;
        display: flex;

        &:last-child {
          margin-bottom: 0;
        }

        .label {
          color: #86909c;
          width: 100px;
        }

        .value {
          color: #1d2129;
          font-weight: 500;
        }
      }
    }

    .success-actions {
      display: flex;
      gap: 16px;
      margin-top: 24px;
    }
  }
</style>
