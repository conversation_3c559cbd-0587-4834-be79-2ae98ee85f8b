# Husky Git Hooks 配置文档

## 概述

本项目使用 Husky 来管理 Git hooks，确保代码质量和提交规范。配置包括多个阶段的检查和自动化任务。

## 🪝 Git Hooks 说明

### 1. pre-commit (提交前检查)

**触发时机**: 执行 `git commit` 时

**执行内容**:
- 📝 运行 lint-staged 检查和格式化暂存文件
- 🔧 TypeScript 类型检查
- 🧪 运行单元测试（针对变更文件）

**配置文件**: `.husky/pre-commit`

### 2. commit-msg (提交信息验证)

**触发时机**: 提交信息编写完成后

**执行内容**:
- ✅ 使用 commitlint 验证提交信息格式
- 📋 确保符合 Conventional Commits 规范

**配置文件**: `.husky/commit-msg`

### 3. prepare-commit-msg (准备提交信息)

**触发时机**: 打开提交信息编辑器前

**执行内容**:
- 📝 生成提交信息模板
- 🔍 根据变更文件推荐提交类型
- 🏷️ 自动提取分支中的 issue 号

**配置文件**: `.husky/prepare-commit-msg`

### 4. pre-push (推送前检查)

**触发时机**: 执行 `git push` 时

**执行内容**:
- 🔍 完整的代码检查 (ESLint)
- 🧪 完整的测试套件
- 🏗️ 构建验证
- 📦 包大小检查
- 🔒 主分支额外检查 (TODO/FIXME 检查)

**配置文件**: `.husky/pre-push`

### 5. post-merge (合并后处理)

**触发时机**: 执行 `git merge` 后

**执行内容**:
- 📦 检查依赖变更并自动安装
- ⚙️ 检查环境配置变更
- 🪝 检查 Husky hooks 变更
- 🔧 检查 TypeScript 配置变更
- 🧹 清理缓存

**配置文件**: `.husky/post-merge`

## 📋 提交信息规范

### 格式

```
<type>(<scope>): <subject>

<body>

<footer>
```

### 提交类型 (type)

- `feat`: 新功能
- `add`: 添加功能
- `fix`: 修复bug
- `docs`: 文档变更
- `style`: 代码格式（不影响功能）
- `refactor`: 代码重构
- `perf`: 性能优化
- `test`: 测试相关
- `chore`: 构建过程或辅助工具变更
- `ci`: CI配置变更
- `build`: 构建流程、外部依赖变更
- `revert`: 回滚
- `release`: 发布新版本
- `wip`: 开发中
- `workflow`: 工作流改进
- `types`: 类型定义文件更改

### 示例

```bash
feat(auth): 添加用户登录功能

实现了基于JWT的用户认证系统，包括：
- 登录接口
- 密码加密
- Token验证中间件

Closes #123
```

## 🛠️ 使用指南

### 安装 Hooks

```bash
# 安装 Husky hooks
npm run hooks:install

# 或者
pnpm run hooks:install
```

### 常用命令

```bash
# 类型检查
npm run type-check

# 代码检查和修复
npm run lint:fix

# 格式化检查
npm run format:check

# 完整验证（类型检查 + 代码检查 + 测试 + 构建）
npm run validate

# 使用 commitizen 进行规范化提交
npm run commit
```

### 跳过 Hooks（紧急情况）

```bash
# 跳过 pre-commit hooks
git commit --no-verify

# 跳过 pre-push hooks
git push --no-verify
```

**注意**: 仅在紧急情况下使用，建议后续补充相关检查。

## 🔧 配置文件

### lint-staged 配置

位于 `package.json` 中，定义了对不同文件类型的处理：

- JavaScript/TypeScript/Vue 文件: ESLint + Prettier
- CSS/SCSS 文件: Prettier
- JSON/Markdown/YAML 文件: Prettier
- 图片文件: 图片优化
- package.json: 排序 + Prettier

### commitlint 配置

位于 `commitlint.config.cjs`，定义了提交信息的验证规则。

## 🚨 故障排除

### 常见问题

1. **Hooks 不执行**
   ```bash
   # 重新安装 hooks
   npm run hooks:install
   ```

2. **权限问题**
   ```bash
   # 给 hooks 文件添加执行权限
   chmod +x .husky/*
   ```

3. **Node.js 版本问题**
   - 确保使用 Node.js 18+
   - 检查 `.nvmrc` 文件

4. **依赖问题**
   ```bash
   # 清理并重新安装依赖
   npm run clean
   pnpm install
   ```

## 📈 最佳实践

1. **提交频率**: 小而频繁的提交，每个提交只做一件事
2. **提交信息**: 清晰描述变更内容和原因
3. **测试**: 确保所有测试通过再提交
4. **代码审查**: 推送前进行自我代码审查
5. **分支管理**: 使用有意义的分支名称

## 🔄 更新和维护

定期检查和更新：
- Husky 版本
- commitlint 规则
- lint-staged 配置
- ESLint 和 Prettier 规则

通过这套完整的 Git hooks 配置，我们确保了代码质量的一致性和提交规范的统一性。
