# 存证证书功能实现说明

## 功能概述

本次实现了在存证列表中显示存证证书的功能，包括：

1. **查看证书按钮**：在操作列中添加"查看证书"按钮
2. **证书弹窗**：显示基于模板的存证证书
3. **PDF下载**：支持将证书导出为PDF文件

## 实现的文件

### 1. 新增组件
- `src/components/CertificateModal.vue` - 存证证书弹窗组件

### 2. 修改的文件
- `src/view/user/dataEvidenceList.vue` - 存证列表页面，添加查看证书功能

### 3. 安装的依赖
- `jspdf` - PDF生成库
- `html2canvas` - HTML转图片库

## 功能详情

### 证书弹窗组件 (CertificateModal.vue)

**主要功能：**
- 基于证书模板图片显示存证信息
- 支持PDF导出功能
- 响应式设计，适配不同屏幕尺寸

**证书信息字段：**
- 数据编号 (dataNumber)
- 数据名称 (dataName)
- 存证单位 (certificationUnit)
- 数据分类 (dataClassification)
- 数据级别 (dataLevel)
- 数据生成时间 (dataGenerationTime)
- 数据规模 (dataScale + dataScaleUnit)
- 数据资源形态 (dataResourceForm)
- 数据块哈希值 (dataBlockHashValue)
- 创建时间 (createTime)

**使用方式：**
```vue
<CertificateModal
  v-model:visible="certificateVisible"
  :certificate-data="currentCertificateData"
/>
```

### 存证列表页面修改

**新增功能：**
1. 在审核通过状态的操作列中添加"查看证书"按钮
2. 添加证书弹窗的响应式数据和方法
3. 集成证书组件

**操作按钮逻辑：**
- 已提交、审核中状态：显示"查看"按钮
- 审核通过状态：显示"查看"、"查看证书"、"下载证书"按钮
- 审核拒绝状态：显示"查看"、"编辑"按钮

## 证书模板

证书模板图片位置：`src/assets/images/certification/deposit_certificate_new.png`

**注意事项：**
- 证书字段的位置需要根据实际模板图片进行调整
- 当前位置设置为示例位置，可能需要根据实际模板进行微调

## PDF导出功能

**实现原理：**
1. 使用 `html2canvas` 将证书内容转换为图片
2. 使用 `jsPDF` 将图片嵌入PDF文档
3. 自动下载生成的PDF文件

**文件命名规则：**
`存证证书_${数据编号或ID}.pdf`

## 使用说明

### 查看证书
1. 在存证列表中找到审核通过的记录
2. 点击操作列中的"查看证书"按钮
3. 弹窗将显示基于模板的存证证书

### 下载证书
1. 在证书弹窗中点击"下载证书"按钮
2. 系统将自动生成PDF并下载到本地

### 样式调整

如需调整证书字段在模板上的位置，请修改 `CertificateModal.vue` 中的CSS样式：

```scss
.field {
  position: absolute;
  
  // 调整各字段的位置
  &.data-number {
    top: 20%;  // 调整垂直位置
    left: 25%; // 调整水平位置
  }
  
  // ... 其他字段
}
```

## 技术特点

1. **响应式设计**：证书弹窗适配不同屏幕尺寸
2. **高清输出**：PDF导出使用2倍缩放确保清晰度
3. **错误处理**：完善的错误提示和异常处理
4. **用户体验**：加载状态提示和操作反馈

## 后续优化建议

1. **证书模板管理**：支持多种证书模板选择
2. **批量导出**：支持批量下载多个证书
3. **水印功能**：添加防伪水印
4. **打印功能**：支持直接打印证书
5. **证书验证**：添加二维码等验证功能

## 测试建议

1. 测试不同状态下的操作按钮显示
2. 测试证书弹窗的显示效果
3. 测试PDF导出功能
4. 测试在不同浏览器中的兼容性
5. 测试响应式布局在移动端的表现
