{"version": "2.0.0", "tasks": [{"label": "npm: dev", "type": "shell", "command": "npm run dev", "isBackground": true, "problemMatcher": {"owner": "custom", "pattern": {"regexp": ".", "file": 1, "location": 2, "message": 3}, "background": {"activeOnStart": true, "beginsPattern": "VITE v\\d+\\.\\d+\\.\\d+", "endsPattern": "(ready in|Local:|Network:) .*"}}, "presentation": {"reveal": "always", "revealProblems": "onProblem", "close": false, "panel": "dedicated"}, "group": {"kind": "build", "isDefault": true}, "dependsOn": ["Kill Terminals"]}, {"label": "Kill Terminals", "command": "${command:workbench.action.terminal.kill}", "type": "shell", "problemMatcher": []}]}