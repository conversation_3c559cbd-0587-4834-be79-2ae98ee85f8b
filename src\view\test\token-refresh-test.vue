<template>
  <div class="token-test-container">
    <a-card title="Token刷新机制测试">
      <a-space direction="vertical" size="large" style="width: 100%">
        <!-- Token状态显示 -->
        <a-card title="当前Token状态" size="small">
          <a-descriptions :column="1" bordered>
            <a-descriptions-item label="访问令牌">
              <a-typography-text copyable>
                {{ tokenInfo.accessToken || '无' }}
              </a-typography-text>
            </a-descriptions-item>
            <a-descriptions-item label="刷新令牌">
              <a-typography-text copyable>
                {{ tokenInfo.refreshToken || '无' }}
              </a-typography-text>
            </a-descriptions-item>
            <a-descriptions-item label="过期时间">
              {{ tokenInfo.expiresTime || '无' }}
            </a-descriptions-item>
            <a-descriptions-item label="是否即将过期">
              <a-tag :color="isExpiringSoon ? 'red' : 'green'">
                {{ isExpiringSoon ? '是' : '否' }}
              </a-tag>
            </a-descriptions-item>
            <a-descriptions-item label="是否已过期">
              <a-tag :color="isExpired ? 'red' : 'green'">
                {{ isExpired ? '是' : '否' }}
              </a-tag>
            </a-descriptions-item>
          </a-descriptions>
        </a-card>

        <!-- 测试按钮 -->
        <a-card title="功能测试" size="small">
          <a-space wrap>
            <a-button type="primary" @click="testNormalRequest" :loading="loading.normal">
              测试正常请求
            </a-button>
            <a-button type="primary" @click="testProtectedRequest" :loading="loading.protected">
              测试需要登录的请求
            </a-button>
            <a-button @click="manualRefreshToken" :loading="loading.refresh">
              手动刷新Token
            </a-button>
            <a-button @click="simulateExpiredToken" type="outline">
              模拟Token过期
            </a-button>
            <a-button @click="clearToken" status="danger">
              清除Token
            </a-button>
            <a-button @click="refreshStatus">
              刷新状态
            </a-button>
          </a-space>
        </a-card>

        <!-- 测试结果 -->
        <a-card title="测试结果" size="small">
          <a-textarea
            v-model="testResults"
            :rows="10"
            placeholder="测试结果将显示在这里..."
            readonly
          />
          <div style="margin-top: 10px">
            <a-button @click="clearResults" size="small">清除结果</a-button>
          </div>
        </a-card>
      </a-space>
    </a-card>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, computed, onMounted } from 'vue';
import { useUserStore } from '@/store/index';
import { TokenManager } from '@/utils/token';
import { Message } from '@arco-design/web-vue';
import proHttp from '@/http/proHttp';
import { memberRefreshToken, getMemberUserInfo } from '@/http/api/login';

const userStore = useUserStore();

// 响应式数据
const tokenInfo = reactive({
  accessToken: '',
  refreshToken: '',
  expiresTime: ''
});

const loading = reactive({
  normal: false,
  protected: false,
  refresh: false
});

const testResults = ref('');

// 计算属性
const isExpiringSoon = computed(() => userStore.isTokenExpiringSoon);
const isExpired = computed(() => userStore.isTokenExpired);

// 添加测试结果
const addResult = (message: string) => {
  const timestamp = new Date().toLocaleString();
  testResults.value += `[${timestamp}] ${message}\n`;
};

// 刷新token状态
const refreshStatus = () => {
  tokenInfo.accessToken = TokenManager.getAccessToken() || '';
  tokenInfo.refreshToken = TokenManager.getRefreshToken() || '';
  tokenInfo.expiresTime = TokenManager.getExpiresTime() || '';
  addResult('状态已刷新');
};

// 测试正常请求（不需要token）
const testNormalRequest = async () => {
  loading.normal = true;
  try {
    // 这里可以调用一个不需要token的接口
    addResult('正常请求测试：开始');
    // 模拟请求
    await new Promise(resolve => setTimeout(resolve, 1000));
    addResult('正常请求测试：成功');
    Message.success('正常请求测试成功');
  } catch (error: any) {
    addResult(`正常请求测试：失败 - ${error.message || error}`);
    Message.error('正常请求测试失败');
  } finally {
    loading.normal = false;
  }
};

// 测试需要登录的请求
const testProtectedRequest = async () => {
  loading.protected = true;
  try {
    addResult('受保护请求测试：开始');
    const result = await getMemberUserInfo();
    addResult(`受保护请求测试：成功 - ${JSON.stringify(result)}`);
    Message.success('受保护请求测试成功');
  } catch (error: any) {
    addResult(`受保护请求测试：失败 - ${error.message || error}`);
    Message.error('受保护请求测试失败');
  } finally {
    loading.protected = false;
  }
};

// 手动刷新token
const manualRefreshToken = async () => {
  loading.refresh = true;
  try {
    const refreshToken = TokenManager.getRefreshToken();
    if (!refreshToken) {
      throw new Error('没有刷新令牌');
    }

    addResult('手动刷新Token：开始');
    const result = await memberRefreshToken({ refreshToken });
    
    if (result?.data) {
      TokenManager.setTokenInfo(result.data);
      addResult(`手动刷新Token：成功 - ${JSON.stringify(result.data)}`);
      Message.success('Token刷新成功');
      refreshStatus();
    } else {
      throw new Error('刷新结果无效');
    }
  } catch (error: any) {
    addResult(`手动刷新Token：失败 - ${error.message || error}`);
    Message.error('Token刷新失败');
  } finally {
    loading.refresh = false;
  }
};

// 模拟token过期
const simulateExpiredToken = () => {
  // 设置一个过期的时间
  const expiredTime = new Date(Date.now() - 1000 * 60 * 60).toISOString(); // 1小时前
  userStore.setExpiresTime(expiredTime);
  addResult('已模拟Token过期');
  refreshStatus();
  Message.info('已模拟Token过期');
};

// 清除token
const clearToken = () => {
  TokenManager.removeToken();
  addResult('Token已清除');
  refreshStatus();
  Message.info('Token已清除');
};

// 清除测试结果
const clearResults = () => {
  testResults.value = '';
};

// 组件挂载时刷新状态
onMounted(() => {
  refreshStatus();
  addResult('Token刷新测试页面已加载');
});
</script>

<style lang="scss" scoped>
.token-test-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}
</style>
