<template>
  <div class="module flex">
    <a-tree
      show-line
      style="min-width: 200px"
      :expanded-keys="expandedKeys !== undefined ? [expandedKeys] : []"
      :selected-keys="selectedKeys !== undefined ? [selectedKeys] : []"
      :field-names="{
        key: 'id',
        title: 'productName',
        children: 'childProductList',
      }"
      :data="productList as any"
      @select="getProductInfoRichTextParamFn"
    />
    <a-scrollbar style="height: 90vh; overflow: auto">
      <div v-if="HtmlText" style="height: 90vh" class="py-2" v-html="HtmlText" />
      <a-skeleton v-else style="width: 100%" animation>
        <a-space direction="vertical" :style="{ width: '100%' }" size="large">
          <a-skeleton-line :rows="5" />
        </a-space>
      </a-skeleton>
    </a-scrollbar>
  </div>
</template>

<script lang="ts" setup>
  import { ref } from 'vue';
  import { getProductInfoRichTextParam, getProductList } from '@/http/api/global';
  import { useRouter } from 'vue-router';
  import { Product } from '@/http/api/global/interface';

  // 定义扩展的Product接口，包含树形结构需要的字段
  interface ProductTreeItem {
    id: string | number;
    productName: string;
    childProductList?: ProductTreeItem[];
    [key: string]: any;
  }

  const router = useRouter();

  const productList = ref<ProductTreeItem[]>([]);
  // 转换query参数为数字或保持为undefined
  const expandedKeys = ref(
    router.currentRoute.value.query.upId ? Number(router.currentRoute.value.query.upId) : undefined
  );
  // 选中的id也可能是字符串类型
  const selectedKeys = ref<string | number | undefined>(
    router.currentRoute.value.query.id ? router.currentRoute.value.query.id.toString() : undefined
  );

  const getProductListFn = function () {
    getProductList().then(res => {
      if (res instanceof Array) {
        productList.value = res as unknown as ProductTreeItem[];
      }
    });
  };
  getProductListFn();

  const HtmlText = ref('');
  const getProductInfoRichTextParamFn = (id: (string | number)[]) => {
    if (id && id.length > 0) {
      getProductInfoRichTextParam(id[0]).then((res: any) => {
        HtmlText.value = res.infoRichText || '';
        selectedKeys.value = id[0];
      });
    }
  };

  // 初始加载选中项内容
  if (selectedKeys.value) {
    getProductInfoRichTextParamFn([selectedKeys.value]);
  }
</script>
<style lang="scss" scoped>
  .module {
    width: 1400px;
    margin: auto;
    background-color: white;
    padding: 20px;
  }

  .banner-img {
    height: 400px;
    width: 100vw;
  }
</style>
