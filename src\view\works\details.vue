<template>
  <div class="page-container">
    <div class="page-header">
      <div class="header-content">
        <a-button type="text" @click="goBack" class="back-button">
          <template #icon>
            <icon-arrow-left />
          </template>
          返回
        </a-button>
        <div class="header-info">
          <h1 class="page-title">作品著作权详情</h1>
          <p class="page-description">查看作品著作权登记详细信息</p>
        </div>
      </div>
    </div>

    <div class="content-wrapper">
      <div v-if="loading" class="loading-container">
        <a-spin :size="24" />
        <p>正在加载详情信息...</p>
      </div>

      <div v-else-if="worksInfo" class="details-container">
        <!-- 状态卡片 -->
        <a-card class="status-card">
          <div class="status-header">
            <h2>{{ worksInfo.worksTitle }}</h2>
            <a-tag :color="getStatusColor(worksInfo.status)" size="large">
              {{ getStatusText(worksInfo.status) }}
            </a-tag>
          </div>
          <div class="status-timeline">
            <a-steps
              :current="getCurrentStep(worksInfo.status)"
              direction="horizontal"
              size="small"
            >
              <a-step title="申请提交" />
              <a-step title="材料审核" />
              <a-step title="公示期" />
              <a-step title="证书颁发" />
            </a-steps>
          </div>
        </a-card>

        <!-- 基本信息 -->
        <a-card title="基本信息" class="info-card">
          <div class="info-grid">
            <div class="info-item">
              <span class="label">申请编号：</span>
              <span class="value">{{ worksInfo.applicationNumber }}</span>
            </div>
            <div class="info-item">
              <span class="label">作品类型：</span>
              <span class="value">{{ getWorksTypeText(worksInfo.worksType) }}</span>
            </div>
            <div class="info-item">
              <span class="label">作品分类：</span>
              <span class="value">{{ worksInfo.worksCategory }}</span>
            </div>
            <div class="info-item">
              <span class="label">创作完成日期：</span>
              <span class="value">{{ worksInfo.creationDate }}</span>
            </div>
            <div class="info-item">
              <span class="label">首次发表日期：</span>
              <span class="value">{{ worksInfo.publicationDate || '未发表' }}</span>
            </div>
            <div class="info-item">
              <span class="label">作者姓名：</span>
              <span class="value">{{ worksInfo.authorName }}</span>
            </div>
            <div class="info-item">
              <span class="label">作者国籍：</span>
              <span class="value">{{ worksInfo.authorNationality }}</span>
            </div>
            <div class="info-item">
              <span class="label">著作权人：</span>
              <span class="value">{{ worksInfo.copyrightOwner }}</span>
            </div>
            <div class="info-item">
              <span class="label">创作性质：</span>
              <span class="value">{{ getCreationNatureText(worksInfo.creationNature) }}</span>
            </div>
            <div class="info-item">
              <span class="label">申请时间：</span>
              <span class="value">{{ worksInfo.createTime }}</span>
            </div>
            <div class="info-item full-width">
              <span class="label">作品简介：</span>
              <span class="value">{{ worksInfo.worksDescription }}</span>
            </div>
          </div>
        </a-card>

        <!-- 申请人信息 -->
        <a-card title="申请人信息" class="info-card">
          <div class="info-grid">
            <div class="info-item">
              <span class="label">申请人：</span>
              <span class="value">{{ worksInfo.applicantName }}</span>
            </div>
            <div class="info-item">
              <span class="label">身份证号/统一社会信用代码：</span>
              <span class="value">{{ worksInfo.applicantId }}</span>
            </div>
            <div class="info-item">
              <span class="label">联系地址：</span>
              <span class="value">{{ worksInfo.applicantAddress }}</span>
            </div>
            <div class="info-item">
              <span class="label">联系电话：</span>
              <span class="value">{{ worksInfo.applicantPhone }}</span>
            </div>
            <div class="info-item">
              <span class="label">电子邮箱：</span>
              <span class="value">{{ worksInfo.applicantEmail }}</span>
            </div>
          </div>
        </a-card>

        <!-- 审核进度 -->
        <a-card title="审核进度" class="info-card">
          <a-timeline>
            <a-timeline-item
              v-for="record in worksInfo.auditRecords"
              :key="record.id"
              :dot-color="getTimelineDotColor(record.status)"
            >
              <template #dot>
                <icon-check-circle v-if="record.status === 'completed'" />
                <icon-clock-circle v-else-if="record.status === 'processing'" />
                <icon-exclamation-circle v-else-if="record.status === 'rejected'" />
                <icon-minus-circle v-else />
              </template>
              <div class="timeline-content">
                <div class="timeline-title">{{ record.title }}</div>
                <div class="timeline-time">{{ record.time }}</div>
                <div v-if="record.remark" class="timeline-remark">{{ record.remark }}</div>
              </div>
            </a-timeline-item>
          </a-timeline>
        </a-card>

        <!-- 相关文件 -->
        <a-card title="相关文件" class="info-card">
          <div class="file-list">
            <div v-for="file in worksInfo.attachments" :key="file.id" class="file-item">
              <div class="file-info">
                <icon-file />
                <div class="file-details">
                  <div class="file-name">{{ file.name }}</div>
                  <div class="file-meta">
                    {{ file.type }} · {{ formatFileSize(file.size) }} · {{ file.uploadTime }}
                  </div>
                </div>
              </div>
              <a-button type="text" @click="downloadFile(file)">
                <template #icon>
                  <icon-download />
                </template>
                下载
              </a-button>
            </div>
          </div>
        </a-card>

        <!-- 操作按钮 -->
        <div class="action-buttons">
          <a-button v-if="canDownloadCertificate" type="primary" @click="downloadCertificate">
            <template #icon>
              <icon-download />
            </template>
            下载登记证书
          </a-button>
          <a-button v-if="canApplyEvidence" @click="goToEvidence">
            <template #icon>
              <icon-safe />
            </template>
            申请存证
          </a-button>
          <a-button @click="printDetails">
            <template #icon>
              <icon-printer />
            </template>
            打印详情
          </a-button>
        </div>
      </div>

      <div v-else class="error-container">
        <a-result status="404" title="未找到相关信息" subtitle="请检查申请编号是否正确">
          <template #extra>
            <a-button type="primary" @click="goBack">返回</a-button>
          </template>
        </a-result>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref, onMounted, computed } from 'vue';
  import { useRouter, useRoute } from 'vue-router';
  import { Message } from '@arco-design/web-vue';
  import {
    IconArrowLeft,
    IconCheckCircle,
    IconClockCircle,
    IconExclamationCircle,
    IconMinusCircle,
    IconFile,
    IconDownload,
    IconSafe,
    IconPrinter,
  } from '@arco-design/web-vue/es/icon';
  import { getWorksRegistrationInfo } from '@/http/api/works';

  const router = useRouter();
  const route = useRoute();

  // 数据状态
  const loading = ref(true);
  const worksInfo = ref<any>(null);

  // 计算属性
  const canDownloadCertificate = computed(() => {
    return worksInfo.value?.status === 'completed';
  });

  const canApplyEvidence = computed(() => {
    return worksInfo.value?.status === 'completed';
  });

  // 获取详情数据
  const fetchWorksDetails = async () => {
    try {
      loading.value = true;
      const id = route.params.id as string;

      // 模拟数据，实际应该调用API
      // const response = await getWorksRegistrationInfo(parseInt(id));

      // 模拟数据
      worksInfo.value = {
        id: id,
        applicationNumber: `ZP2024${id.padStart(6, '0')}`,
        worksTitle: '《春江花月夜》音乐作品',
        worksType: 'music',
        worksCategory: '器乐作品',
        creationDate: '2024-01-10',
        publicationDate: '2024-01-15',
        authorName: '张某某',
        authorNationality: '中国',
        copyrightOwner: '张某某',
        creationNature: 'original',
        worksDescription:
          '这是一首以古典诗词为背景创作的现代器乐作品，融合了传统民族音乐元素与现代作曲技法，表达了对自然美景的赞美和对人生的思考。',
        applicantName: '张某某',
        applicantId: '110101199001011234',
        applicantAddress: '北京市朝阳区某某街道某某号',
        applicantPhone: '13800138000',
        applicantEmail: '<EMAIL>',
        status: 'publicizing',
        createTime: '2024-01-20 10:30:00',
        auditRecords: [
          {
            id: 1,
            title: '申请提交',
            time: '2024-01-20 10:30:00',
            status: 'completed',
            remark: '申请材料已成功提交',
          },
          {
            id: 2,
            title: '材料审核',
            time: '2024-01-22 14:20:00',
            status: 'completed',
            remark: '材料审核通过，符合登记要求',
          },
          {
            id: 3,
            title: '公示期',
            time: '2024-01-25 09:00:00',
            status: 'processing',
            remark: '正在进行15天公示，公示期至2024-02-09',
          },
          {
            id: 4,
            title: '证书颁发',
            time: '',
            status: 'pending',
            remark: '',
          },
        ],
        attachments: [
          {
            id: 1,
            name: '作品样本.mp3',
            type: '音频文件',
            size: 5242880,
            uploadTime: '2024-01-20 10:30:00',
          },
          {
            id: 2,
            name: '作品说明书.pdf',
            type: 'PDF文档',
            size: 1048576,
            uploadTime: '2024-01-20 10:31:00',
          },
          {
            id: 3,
            name: '身份证明.pdf',
            type: 'PDF文档',
            size: 512000,
            uploadTime: '2024-01-20 10:32:00',
          },
        ],
      };
    } catch (error) {
      console.error('获取详情失败:', error);
      Message.error('获取详情失败');
    } finally {
      loading.value = false;
    }
  };

  // 工具方法
  const getStatusColor = (status: string) => {
    const colorMap: Record<string, string> = {
      pending: 'gray',
      reviewing: 'blue',
      publicizing: 'orange',
      completed: 'green',
      rejected: 'red',
    };
    return colorMap[status] || 'gray';
  };

  const getStatusText = (status: string) => {
    const textMap: Record<string, string> = {
      pending: '待审核',
      reviewing: '审核中',
      publicizing: '公示中',
      completed: '已完成',
      rejected: '已驳回',
    };
    return textMap[status] || '未知状态';
  };

  const getCurrentStep = (status: string) => {
    const stepMap: Record<string, number> = {
      pending: 0,
      reviewing: 1,
      publicizing: 2,
      completed: 3,
      rejected: 1,
    };
    return stepMap[status] || 0;
  };

  const getWorksTypeText = (type: string) => {
    const typeMap: Record<string, string> = {
      literary: '文字作品',
      music: '音乐作品',
      art: '美术作品',
      photography: '摄影作品',
      film: '影视作品',
      graphic: '图形作品',
      architecture: '建筑作品',
      other: '其他作品',
    };
    return typeMap[type] || type;
  };

  const getCreationNatureText = (nature: string) => {
    const natureMap: Record<string, string> = {
      original: '原创',
      adaptation: '改编',
      translation: '翻译',
      compilation: '汇编',
    };
    return natureMap[nature] || nature;
  };

  const getTimelineDotColor = (status: string) => {
    const colorMap: Record<string, string> = {
      completed: '#00b42a',
      processing: '#165dff',
      rejected: '#f53f3f',
      pending: '#86909c',
    };
    return colorMap[status] || '#86909c';
  };

  const formatFileSize = (size: number) => {
    if (size < 1024) return size + ' B';
    if (size < 1024 * 1024) return (size / 1024).toFixed(1) + ' KB';
    return (size / (1024 * 1024)).toFixed(1) + ' MB';
  };

  // 操作方法
  const goBack = () => {
    router.go(-1);
  };

  const downloadFile = (file: any) => {
    // 这里应该实现文件下载逻辑
    Message.info(`正在下载 ${file.name}...`);
  };

  const downloadCertificate = () => {
    // 这里应该实现证书下载逻辑
    Message.info('正在下载登记证书...');
  };

  const goToEvidence = () => {
    router.push('/works/evidence');
  };

  const printDetails = () => {
    window.print();
  };

  // 组件挂载时获取数据
  onMounted(() => {
    fetchWorksDetails();
  });
</script>

<style lang="scss" scoped>
  .page-container {
    width: 100%;
    min-height: 100vh;
    background-color: #f7f8fa;
    padding-bottom: 40px;
  }

  .page-header {
    background: linear-gradient(135deg, #165dff 0%, #4080ff 100%);
    color: white;
    padding: 24px 0;

    .header-content {
      max-width: 1200px;
      margin: 0 auto;
      padding: 0 24px;
      display: flex;
      align-items: center;
      gap: 16px;

      .back-button {
        color: white;
        border-color: rgba(255, 255, 255, 0.3);

        &:hover {
          background-color: rgba(255, 255, 255, 0.1);
          border-color: rgba(255, 255, 255, 0.5);
        }
      }

      .header-info {
        .page-title {
          font-size: 24px;
          font-weight: 600;
          margin-bottom: 4px;
        }

        .page-description {
          font-size: 14px;
          opacity: 0.9;
          margin: 0;
        }
      }
    }
  }

  .content-wrapper {
    max-width: 1200px;
    margin: 0 auto;
    padding: 32px 24px;
  }

  .loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 80px 0;
    color: #86909c;

    p {
      margin-top: 16px;
      font-size: 14px;
    }
  }

  .details-container {
    display: flex;
    flex-direction: column;
    gap: 24px;
  }

  .status-card {
    .status-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 24px;

      h2 {
        font-size: 20px;
        font-weight: 600;
        color: #1d2129;
        margin: 0;
      }
    }

    .status-timeline {
      :deep(.arco-steps-item-title) {
        font-size: 12px;
      }
    }
  }

  .info-card {
    .info-grid {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 16px;

      .info-item {
        display: flex;
        align-items: flex-start;
        gap: 8px;

        &.full-width {
          grid-column: 1 / -1;
          flex-direction: column;
          gap: 8px;
        }

        .label {
          color: #86909c;
          width: 140px;
          flex-shrink: 0;
          font-size: 14px;
        }

        .value {
          color: #1d2129;
          font-weight: 500;
          flex: 1;
          word-break: break-word;
        }
      }
    }
  }

  .timeline-content {
    .timeline-title {
      font-size: 14px;
      font-weight: 500;
      color: #1d2129;
      margin-bottom: 4px;
    }

    .timeline-time {
      font-size: 12px;
      color: #86909c;
      margin-bottom: 4px;
    }

    .timeline-remark {
      font-size: 12px;
      color: #4e5969;
      line-height: 1.5;
    }
  }

  .file-list {
    .file-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 16px;
      background: #f7f8fa;
      border-radius: 8px;
      margin-bottom: 12px;

      &:last-child {
        margin-bottom: 0;
      }

      .file-info {
        display: flex;
        align-items: center;
        gap: 12px;
        flex: 1;

        .arco-icon {
          font-size: 20px;
          color: #165dff;
        }

        .file-details {
          .file-name {
            font-size: 14px;
            font-weight: 500;
            color: #1d2129;
            margin-bottom: 4px;
          }

          .file-meta {
            font-size: 12px;
            color: #86909c;
          }
        }
      }
    }
  }

  .action-buttons {
    display: flex;
    justify-content: center;
    gap: 16px;
    margin-top: 32px;
    flex-wrap: wrap;

    .arco-btn {
      min-width: 140px;
    }
  }

  .error-container {
    padding: 40px 0;
  }

  :deep(.arco-card-header) {
    border-bottom: 1px solid #e5e6eb;
  }

  :deep(.arco-card-header-title) {
    font-size: 16px;
    font-weight: 600;
    color: #1d2129;
  }

  :deep(.arco-timeline-item-content) {
    padding-bottom: 20px;
  }

  @media (max-width: 768px) {
    .content-wrapper {
      padding: 24px 16px;
    }

    .page-header .header-content {
      padding: 0 16px;
      flex-direction: column;
      align-items: flex-start;
      gap: 12px;

      .back-button {
        align-self: flex-start;
      }
    }

    .status-card .status-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 16px;
    }

    .info-card .info-grid {
      grid-template-columns: 1fr;

      .info-item .label {
        width: auto;
      }
    }

    .action-buttons {
      flex-direction: column;

      .arco-btn {
        width: 100%;
      }
    }

    .file-item {
      flex-direction: column;
      align-items: flex-start;
      gap: 12px;

      .file-info {
        width: 100%;
      }
    }
  }

  @media print {
    .page-header,
    .action-buttons,
    .back-button {
      display: none !important;
    }

    .page-container {
      background: white !important;
    }

    .content-wrapper {
      max-width: none;
      padding: 0;
    }

    .details-container {
      gap: 16px;
    }

    :deep(.arco-card) {
      box-shadow: none !important;
      border: 1px solid #e5e6eb !important;
    }
  }
</style>
