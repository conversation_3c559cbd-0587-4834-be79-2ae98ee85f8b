# 文件上传功能使用指南

## 概述

本项目基于参考代码实现了完整的文件上传功能，支持前端直连上传和后端代理上传两种模式。

## 功能特性

- ✅ 支持多种文件格式（PDF、Word、Excel、PPT、图片、压缩包等）
- ✅ 文件大小验证（默认最大100MB）
- ✅ 文件类型验证
- ✅ SHA256文件名生成（防重复）
- ✅ 上传进度显示
- ✅ 错误处理和用户提示
- ✅ 支持批量上传
- ✅ 前端直连和后端代理两种上传模式

## 使用方法

### 1. 基本使用

```vue
<template>
  <a-upload
    :custom-request="customRequest"
    :accept="acceptTypes.join(',')"
    :limit="5"
    multiple
    @change="handleFileChange"
  >
    <template #upload-button>
      <a-button type="outline">
        <icon-upload />
        上传文件
      </a-button>
    </template>
    <template #extra>
      <div class="upload-tip">
        支持格式：PDF、Word、Excel等，单个文件不超过100MB
      </div>
    </template>
  </a-upload>
</template>

<script setup>
import { useUpload } from '@/utils/upload';

const { customRequest } = useUpload();
const acceptTypes = ['.pdf', '.doc', '.docx', '.xls', '.xlsx'];

const handleFileChange = (fileList) => {
  console.log('文件列表:', fileList);
};
</script>
```

### 2. 高级配置

```typescript
import { useUpload, validateFileType, validateFileSize, formatFileSize } from '@/utils/upload';

// 自定义配置
const uploadConfig = {
  maxSize: 50 * 1024 * 1024, // 50MB
  acceptTypes: ['.pdf', '.jpg', '.png']
};

const { customRequest } = useUpload();

// 上传前验证
const beforeUpload = (file: File) => {
  if (!validateFileType(file, uploadConfig.acceptTypes)) {
    Message.error(`不支持的文件类型: ${file.name}`);
    return false;
  }
  
  if (!validateFileSize(file, uploadConfig.maxSize)) {
    Message.error(`文件大小超过限制: ${formatFileSize(uploadConfig.maxSize)}`);
    return false;
  }
  
  return true;
};
```

## 环境配置

### 环境变量

在 `.env` 文件中配置：

```bash
# 上传模式：client（前端直连）或 server（后端代理）
VITE_UPLOAD_TYPE=server

# PRO API 地址
VITE_PRO_API_TARGET=http://localhost:8080/pro
```

### 上传模式说明

#### 1. 前端直连模式 (client)
- 适用于支持S3协议的对象存储服务
- 前端直接上传到对象存储，减少服务器压力
- 需要获取预签名URL

#### 2. 后端代理模式 (server)
- 文件先上传到后端服务器
- 由后端处理文件存储
- 更好的安全控制

## API 接口

### 文件上传相关接口

```typescript
// 获取文件预签名地址（前端直连模式）
getFilePresignedUrl(path: string): Promise<ApiResponse<FilePresignedUrlRespVO>>

// 创建文件记录
createFile(data: FileCreateReqVO): Promise<ApiResponse<number>>

// 上传文件（后端代理模式）
uploadFile(file: File): Promise<ApiResponse<string>>
```

## 错误处理

系统会自动处理以下错误情况：

1. **文件类型不支持**：显示错误提示，阻止上传
2. **文件大小超限**：显示错误提示，阻止上传
3. **网络错误**：显示网络错误提示
4. **服务器错误**：显示服务器错误信息

## 最佳实践

1. **文件验证**：始终在前端进行文件类型和大小验证
2. **用户体验**：提供清晰的上传提示和进度显示
3. **错误处理**：提供友好的错误提示信息
4. **性能优化**：大文件建议使用前端直连模式
5. **安全考虑**：敏感文件建议使用后端代理模式

## 故障排除

### 常见问题

1. **上传失败**
   - 检查网络连接
   - 确认文件大小和类型是否符合要求
   - 查看浏览器控制台错误信息

2. **文件名重复**
   - 系统使用SHA256算法生成唯一文件名
   - 相同内容的文件会生成相同的文件名

3. **上传速度慢**
   - 建议使用前端直连模式
   - 检查网络带宽

## 更新日志

- v1.0.0: 初始版本，支持基本文件上传功能
- v1.1.0: 添加文件验证和错误处理
- v1.2.0: 支持前端直连和后端代理两种模式
