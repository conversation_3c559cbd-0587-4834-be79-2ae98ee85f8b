<template>
  <div class="page-container">
    <div class="page-header">
      <h1 class="page-title">软件著作权登记详情</h1>
    </div>

    <div v-if="loading" class="content-wrapper">
      <div class="loading-container">
        <a-spin />
        <p>加载中...</p>
      </div>
    </div>

    <div v-else class="content-wrapper">
      <a-descriptions title="基本信息" :column="1" :data="basicInfo" layout="horizontal" />

      <a-divider />

      <div class="section-title">申请主体信息</div>
      <a-descriptions :column="1" :data="applicantInfo" layout="horizontal" />

      <a-divider />

      <div class="section-title">公示状态</div>
      <div class="status-container">
        <a-steps :current="currentStep">
          <a-step title="提交申请" :description="details.submitDate" />
          <a-step title="材料审核" :description="details.auditDate || '处理中'" />
          <a-step title="公示中" :description="getPublishDescription()" />
          <a-step title="发放证书" :description="details.certificateDate || '等待公示完成'" />
        </a-steps>
      </div>

      <div class="btn-container">
        <a-button type="primary" @click="goBack"> 返回列表 </a-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref, computed, onMounted } from 'vue';
  import { useRouter, useRoute } from 'vue-router';
  import { getSoftwareRegistrationInfo } from '@/http/api/software';

  const router = useRouter();
  const route = useRoute();
  const loading = ref(true);
  const details = ref({
    id: '',
    title: '',
    shortName: '',
    version: '',
    completionDate: '',
    developmentType: '',
    copyrightOwners: '',
    description: '',
    applicationNumber: '',
    submitDate: '',
    auditDate: '',
    publishStartDate: '',
    publishEndDate: '',
    certificateDate: '',
    status: '',
    applicant: {
      type: '',
      name: '',
      idNumber: '',
      businessLicense: '',
      address: '',
      contactPerson: '',
      phone: '',
      email: '',
    },
  });

  // 根据状态确定当前步骤
  const currentStep = computed(() => {
    if (details.value.certificateDate) {
      return 3;
    } else if (details.value.publishStartDate) {
      return 2;
    } else if (details.value.auditDate) {
      return 1;
    } else {
      return 0;
    }
  });

  // 基本信息
  const basicInfo = computed(() => [
    {
      label: '软件名称',
      value: details.value.title,
    },
    {
      label: '软件简称',
      value: details.value.shortName || '无',
    },
    {
      label: '版本号',
      value: details.value.version,
    },
    {
      label: '开发完成日期',
      value: details.value.completionDate,
    },
    {
      label: '开发方式',
      value: getDevelopmentTypeText(details.value.developmentType),
    },
    {
      label: '著作权人',
      value: details.value.copyrightOwners,
    },
    {
      label: '功能简述',
      value: details.value.description,
    },
    {
      label: '申请编号',
      value: details.value.applicationNumber,
    },
    {
      label: '当前状态',
      value: details.value.status,
    },
  ]);

  // 申请主体信息
  const applicantInfo = computed(() => {
    const applicant = details.value.applicant;
    const baseInfo = [
      {
        label: '申请类型',
        value: applicant.type === 'personal' ? '个人' : '企业/组织',
      },
      {
        label: applicant.type === 'personal' ? '申请人姓名' : '企业名称',
        value: applicant.name,
      },
      {
        label: applicant.type === 'personal' ? '身份证号' : '统一社会信用代码',
        value: applicant.type === 'personal' ? applicant.idNumber : applicant.businessLicense,
      },
    ];

    if (applicant.type === 'company') {
      baseInfo.push({
        label: '联系人',
        value: applicant.contactPerson,
      });
    }

    baseInfo.push(
      {
        label: '联系地址',
        value: applicant.address,
      },
      {
        label: '联系电话',
        value: applicant.phone,
      },
      {
        label: '电子邮箱',
        value: applicant.email,
      }
    );

    return baseInfo;
  });

  // 获取开发方式的文本描述
  const getDevelopmentTypeText = type => {
    const typeMap = {
      original: '原始开发',
      commissioned: '委托开发',
      joint: '合作开发',
      adaptation: '修改、改编',
    };
    return typeMap[type] || type;
  };

  // 获取公示描述信息
  const getPublishDescription = () => {
    if (!details.value.publishStartDate) {
      return '等待审核通过';
    }

    return `${details.value.publishStartDate} 至 ${details.value.publishEndDate}`;
  };

  // 获取详情
  const fetchDetail = async () => {
    const id = route.params.id;
    if (!id) {
      router.push('/software/page/announcements');
      return;
    }

    try {
      loading.value = true;
      // const response = await getSoftwareRegistrationInfo(Number(id));
      const response = null;

      if (response) {
        details.value = response;
      } else {
        // 模拟数据
        details.value = {
          id: id.toString(),
          title: '某企业管理系统V1.0',
          shortName: '企业管理系统',
          version: 'V1.0',
          completionDate: '2023-11-01',
          developmentType: 'original',
          copyrightOwners: '某科技有限公司',
          description:
            '本软件是一套针对企业内部管理的综合性系统，包括人事管理、财务管理、资产管理等模块，可以帮助企业实现信息化管理，提高工作效率。',
          applicationNumber: 'SW2023-0001',
          submitDate: '2023-12-01',
          auditDate: '2023-12-05',
          publishStartDate: '2023-12-15',
          publishEndDate: '2023-12-31',
          certificateDate: '',
          status: '公示中',
          applicant: {
            type: 'company',
            name: '某科技有限公司',
            idNumber: '',
            businessLicense: '91310000XXXXXXXX',
            address: '上海市浦东新区某路XX号',
            contactPerson: '张三',
            phone: '***********',
            email: '<EMAIL>',
          },
        };
      }
    } catch (error) {
      console.error('获取详情失败:', error);
    } finally {
      loading.value = false;
    }
  };

  // 返回列表
  const goBack = () => {
    router.push('/software/announcements');
  };

  onMounted(() => {
    fetchDetail();
  });
</script>

<style lang="scss" scoped>
  .page-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 40px 20px;
  }

  .page-header {
    text-align: center;
    margin-bottom: 40px;

    .page-title {
      font-size: 2rem;
      font-weight: bold;
      margin-bottom: 12px;
      color: #1d2129;
    }
  }

  .content-wrapper {
    background: #fff;
    border-radius: 8px;
    padding: 30px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  }

  .loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 60px 0;

    p {
      margin-top: 16px;
      color: #86909c;
    }
  }

  .section-title {
    font-size: 18px;
    font-weight: 500;
    margin-bottom: 20px;
    color: #1d2129;
  }

  .status-container {
    padding: 20px 0;
  }

  .btn-container {
    display: flex;
    justify-content: center;
    margin-top: 40px;
  }
</style>
