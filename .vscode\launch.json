{"version": "0.2.0", "configurations": [{"type": "msedge", "request": "launch", "name": "Debug <PERSON> (Edge)", "url": "http://localhost:5173", "webRoot": "${workspaceFolder}", "sourceMaps": true, "sourceMapPathOverrides": {"webpack:///src/*": "${webRoot}/src/*", "/./*": "${webRoot}/*", "/src/*": "${webRoot}/src/*", "/*": "*", "/./~/*": "${webRoot}/node_modules/*"}, "preLaunchTask": "npm: dev", "skipFiles": ["<node_internals>/**"], "userDataDir": "${workspaceFolder}/.vscode/edge-debug-profile", "runtimeArgs": ["--disable-extensions", "--auto-open-devtools-for-tabs"]}, {"type": "chrome", "request": "launch", "name": "Debug Vue App (Chrome)", "url": "http://localhost:5173", "webRoot": "${workspaceFolder}", "sourceMaps": true, "sourceMapPathOverrides": {"webpack:///src/*": "${webRoot}/src/*", "/./*": "${webRoot}/*", "/src/*": "${webRoot}/src/*", "/*": "*", "/./~/*": "${webRoot}/node_modules/*"}, "preLaunchTask": "npm: dev", "skipFiles": ["<node_internals>/**"], "userDataDir": "${workspaceFolder}/.vscode/chrome-debug-profile", "runtimeArgs": ["--disable-extensions", "--auto-open-devtools-for-tabs"]}, {"type": "node", "request": "launch", "name": "Debug Vitest Tests", "skipFiles": ["<node_internals>/**"], "program": "${workspaceFolder}/node_modules/vitest/vitest.mjs", "args": ["run"], "smartStep": true, "console": "integratedTerminal"}]}