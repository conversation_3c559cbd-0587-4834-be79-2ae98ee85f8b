<template>
  <div class="bg-grey p-4">
    <a-card :bordered="false" class="header mb-4">
      <a-descriptions
        :data="userData"
        :column="2"
        align="left"
        title="用户信息"
        layout="inline-horizontal"
        :label-style="{
          width: '140px',
          fontWeight: 'normal',
          color: 'rgb(var(--gray-8))',
        }"
        :value-style="{
          width: '260px',
          textAlign: 'left',
        }"
      >
        <template #value="{ value, data }">
          <a-typography-text
            v-if="value"
            class="flex"
            :copyable="data.label != '商户编码' && data.label != '应用公钥'"
          >
            <div class="truncate">
              {{ value }}
            </div>
          </a-typography-text>
          <div v-else>暂无数据</div>
        </template>
      </a-descriptions>
    </a-card>
    <div class="wrapper">
      <a-tabs
        :active-key="activeKey"
        type="rounded"
        @change="
          key => {
            activeKey = key;
          }
        "
      >
        <!-- <a-tab-pane key="1" title="基础信息">
                    <BasicInformation :formData="userInfo" />
                </a-tab-pane> -->
        <a-tab-pane key="1" title="安全设置">
          <SecuritySettings :form-data="userInfo" />
        </a-tab-pane>
        <a-tab-pane key="2" title="企业认证">
          <Certification />
        </a-tab-pane>
      </a-tabs>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { getAssetsFile, phoneCode, namePrivate } from '@/utils/tool';
  import { ref, reactive, onMounted, computed } from 'vue';
  import BasicInformation from './components/basic-information.vue';
  import SecuritySettings from './components/security-settings.vue';
  import Certification from './components/certification.vue';
  import { transportFile } from '@/utils/transport'; // md5加密
  import { getIsvPortal } from '@/http/api/user';
  import { getMemberUserInfo } from '@/http/api/login/index';
  import type { FileItem, RequestOption } from '@arco-design/web-vue/es/upload/interfaces';
  import { Message } from '@arco-design/web-vue';
  import { IconCamera } from '@arco-design/web-vue/es/icon';
  import { useUserStore } from '@/store/index';
  import { storeToRefs } from 'pinia';
  import { useRouter, useRoute } from 'vue-router';
  import { fileUpload, uploadToServer } from '@/http/api/global/index';

  const userStore = useUserStore();
  const router = useRouter();
  const route = useRoute();
  const isvInfo = ref<any>({});
  const memberUserInfo = ref<any>({}); // 新增：用户基本信息

  const activeKey = ref(); // 选中的tab
  activeKey.value = route.query.tab || '1';

  const { user_info } = storeToRefs(userStore);
  if (!user_info.value) {
    router.replace('/login');
  }

  const userInfoInte = async () => {
    let InfoRes = await getIsvPortal();
    isvInfo.value = InfoRes;
  };

  // 获取用户基本信息
  const fetchMemberUserInfo = async () => {
    try {
      const userInfo = await getMemberUserInfo();
      memberUserInfo.value = (userInfo as any).data;
      console.log('用户基本信息resp:', userInfo);
      console.log('用户基本信息:', memberUserInfo.value);
    } catch (error) {
      console.error('获取用户信息失败:', error);
    }
  };

  // 获取页面初始化内容
  userInfoInte();
  fetchMemberUserInfo();

  const userData = computed(() => [
    // 注释掉原有的商户信息字段
    // { label: '应用ID（appId）', value: isvInfo.value.appId },
    // { label: '应用公钥', value: isvInfo.value.isUploadPublicKey },
    // { label: '商户编码', value: isvInfo.value.code },
    // { label: '支付回调地址', value: isvInfo.value.payCallbackUrl },
    // { label: '合利宝退款回调地址', value: isvInfo.value.refundCallbackUrl },
    // { label: '合利宝进件回调地址', value: isvInfo.value.merchantNetCallbackUrl },
    // { label: '合利宝签约回调地址', value: isvInfo.value.merchantSignCallbackUrl },
    // { label: '合利宝平台分账费率', value: isvInfo.value.customerRate },
    // { label: '银联签约回调地址', value: isvInfo.value.chinaumsMerchantSignCallbackUrl },
    // { label: '银联平台分账费率', value: isvInfo.value.chinaumsCustomerRate },

    // 根据新接口返回的用户基本信息字段
    { label: '用户编号', value: memberUserInfo.value.id },
    { label: '用户账户', value: memberUserInfo.value.username },
    { label: '用户昵称', value: memberUserInfo.value.nickname },
    { label: '用户头像', value: memberUserInfo.value.avatar },
    { label: '手机号', value: memberUserInfo.value.mobile },
    {
      label: '用户性别',
      value:
        memberUserInfo.value.sex === 1 ? '男' : memberUserInfo.value.sex === 2 ? '女' : '未设置',
    },
    { label: '积分', value: memberUserInfo.value.point },
    { label: '经验值', value: memberUserInfo.value.experience },
    { label: '用户等级', value: memberUserInfo.value.level?.name },
    { label: '等级编号', value: memberUserInfo.value.level?.level },
    { label: '等级图标', value: memberUserInfo.value.level?.icon },
    { label: '是否推广员', value: memberUserInfo.value.brokerageEnabled ? '是' : '否' },
    // 404error
  ]);
  const userInfo = computed(() => {
    return {
      email: user_info.value.email,
      nickName: user_info.value.nickName,
      phone: phoneCode(user_info.value.phone),
      username: user_info.value.username,
      avatar: user_info.value.avatar,
    };
  });

  // 用户头像
  const fileList = ref<FileItem[]>([]);
  if (!userInfo.value.avatar) {
    fileList.value = [
      {
        uid: new Date().getTime().toString(),
        name: 'avatar.png',
        url: getAssetsFile('notSetAvatar.png'),
      },
    ];
  } else {
    fileList.value = [
      { uid: new Date().getTime().toString(), name: 'avatar.png', url: userInfo.value.avatar },
    ];
  }

  const customRequest2 = option => {
    const { onProgress, onError, onSuccess, fileItem, name } = option;
    const xhr = new XMLHttpRequest();
    if (xhr.upload) {
      xhr.upload.onprogress = function (event) {
        let percent;
        if (event.total > 0) {
          // 0 ~ 1
          percent = event.loaded / event.total;
        }
        onProgress(percent, event);
      };
    }
    xhr.onerror = function error(e) {
      onError(e);
    };
    xhr.onload = function onload() {
      if (xhr.status < 200 || xhr.status >= 300) {
        return onError(xhr.responseText);
      }
      onSuccess(xhr.response);
    };

    const formData = new FormData();
    formData.append(name || 'file', fileItem.file);
    xhr.open('post', '//upload-z2.qbox.me/', true);
    xhr.send(formData);

    return {
      abort() {
        xhr.abort();
      },
    };
  };
  const customRequest = options => {
    const { fileItem } = options;
    // 文件MD5加密
    transportFile(fileItem.file).then(md5 => {
      let fileData = {
        attachmentObjectTypeCode: 'otherMaterial',
        fileSize: fileItem.file.size as number,
        md5: md5,
        originalFilename: fileItem.name,
        isEncryption: true,
      };
      // 上传至阿里云oss
      fileUpload(fileItem.file).then((res: any) => {
        let params = {
          url: res.presignedUploadUrl,
          md5Base64: res.md5Base64,
          file: fileItem.file,
          contentType: res.mimeType,
        };
        uploadToServer(params).then((r: any) => {
          if (r.status == 200) {
            Message.success('更新成功！');
            userStore.setUserInfo({ ...userStore.getUserInfo, avatar: res.fileUrl });
            fileList.value = [
              { uid: new Date().getTime().toString(), name: 'avatar.png', url: res.fileUrl },
            ];
          } else {
            Message.error('上传失败！');
          }
        });
      });
    });
    return {
      abort() {},
    };
  };
</script>

<style lang="scss" scoped>
  .header {
    display: flex;
    align-items: center;
    padding: 0 20px;
    height: 204px;
    background: url(https://p3-armor.byteimg.com/tos-cn-i-49unhts6dw/41c6b125cc2e27021bf7fcc9a9b1897c.svg~tplv-49unhts6dw-image.image)
      no-repeat;
    background-size: cover;
    border-radius: 4px;

    :deep(.arco-card-body) {
      width: 100%;
    }
  }

  .wrapper {
    padding: 20px 0 0 20px;
    // min-height: 580px;
    background-color: #ffffff;
    border-radius: 4px;
  }
</style>
