<template>
  <div class="p-4 bg-grey">
    <a-page-header
      :show-back="false"
      :style="{ background: 'var(--color-bg-2)' }"
      title="资产管理"
      :subtitle="'共' + total + '条'"
    >
      <template #extra>
        <a-button v-if="list.length > 0" type="text"> 清空全部 </a-button>
      </template>
    </a-page-header>
    <a-row v-if="list.length > 0" class="bg-white px-4">
      <a-col v-for="(item, index) in list" :key="index" :span="8">
        <a-card hoverable class="mb-6 mx-2">
          <template #actions>
            <span class="icon-hover">
              <Icon-Star-Fill
                v-if="item.collectFlag"
                @click.stop="deleteAttentionRecordFn(item.attentionRecordId)"
              />
              <Icon-Star v-else @click.stop="addAttentionRecordFun(item.worksPutId)" />
            </span>
            <span class="icon-hover">
              <a-popconfirm
                content="是否确认删除历史浏览?"
                ok-text="确认"
                cancel-text="取消"
                @ok="deleteBrowseRecordFn(item.browseRecordId)"
              >
                <icon-delete />
              </a-popconfirm>
            </span>
            <span class="icon-hover">
              <IconMore />
            </span>
          </template>
          <template #cover>
            <div
              :style="{
                height: '278px',
                overflow: 'hidden',
              }"
            >
              <img
                :style="{ width: '100%', transform: 'translateY(-20px)' }"
                alt="dessert"
                :src="item.cover"
              />
            </div>
          </template>
          <a-card-meta
            title="苗族蜡染图案、纹饰(系列3)苗族蜡染图案、纹饰(系列3)苗族蜡染图案、纹饰(系列3)"
          >
            <template #avatar>
              <div :style="{ display: 'flex', alignItems: 'center', color: '#1D2129' }">
                <a-avatar
                  class="mr-2"
                  :style="{ backgroundColor: '#ffffff', width: '22px', height: '22px' }"
                >
                  <img alt="avatar" :src="priceIcon" />
                </a-avatar>
                <a-typography-text class="text-xl">
                  {{ item.price }}
                </a-typography-text>
              </div>
            </template>
            <template #description>
              <div>{{ item.worksName }}</div>
              <div class="text-sm text-gray-500">浏览日期：{{ item.browseDate }}</div>
            </template>
          </a-card-meta>
        </a-card>
      </a-col>
    </a-row>
    <div v-else class="bg-white py-10 flex flex-col">
      <a-empty />
      <router-link to="/shop">
        <a-button type="text"> 去逛逛 </a-button>
      </router-link>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { getAssetsFile, IntegerToFloat } from '@/utils/tool';
  import { ref, reactive, onMounted } from 'vue';
  import { IconStar, IconDelete, IconMore, IconStarFill } from '@arco-design/web-vue/es/icon';
  import {
    browseRecordRecord,
    deleteBrowseRecord,
    addAttentionRecord,
    deleteAttentionRecord,
  } from '@/http/api/user';
  import { Message } from '@arco-design/web-vue';

  // 定义资产项接口
  interface AssetItem {
    id: string | number;
    cover: string;
    price: string | number;
    worksName: string;
    browseDate: string;
    browseRecordId: number;
    collectFlag: boolean;
    attentionRecordId: number;
    worksPutId: number;
  }

  const priceIcon = getAssetsFile('icons/price-icon.png');
  // 关注列表
  const total = ref(0);
  const list = ref<AssetItem[]>([]);
  const current = ref(0); // 当前页
  // 查询浏览列表
  const getIndexData = () => {
    let params = {
      current: current.value + 1,
      size: 10,
    };
    browseRecordRecord(params).then((res: any) => {
      for (let i of res.records) {
        i.price = IntegerToFloat(i.price);
      }
      list.value = res.records;
      total.value = res.total;
    });
  };
  // 删除浏览
  const deleteBrowseRecordFn = (id: number) => {
    console.log();
    deleteBrowseRecord(id).then(res => {
      // message.success(res)
      list.value = [];
      getIndexData();
    });
  };
  // 添加关注
  const addAttentionRecordFun = (id: number) => {
    let params = {
      worksPutId: id,
    };
    addAttentionRecord(params).then((res: any) => {
      if (res) {
        Message.normal(res.msg);
      }
    });
  };
  // 取消关注
  const deleteAttentionRecordFn = (id: number) => {
    deleteAttentionRecord(id).then(res => {
      console.log(res);
    });
  };
  onMounted(() => {
    getIndexData();
  });
</script>
<style lang="scss" scoped>
  .icon-hover {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    transition: all 0.1s;
  }

  .icon-hover:hover {
    background-color: rgb(var(--gray-2));
  }
</style>
