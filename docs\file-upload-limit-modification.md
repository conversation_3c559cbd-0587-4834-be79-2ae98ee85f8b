# 文件上传限制修改总结

## 修改概述

将数据存证申请页面中的所有文件上传组件从多文件上传修改为单文件上传，每项只能上传1个文件。

## 修改详情

### 1. 修改的文件上传组件

| 组件名称 | 原限制 | 新限制 | 位置 |
|---------|--------|--------|------|
| 数据样本文件 | 最多5个文件 | 只能上传1个文件 | 存证信息部分 |
| 数据来源佐证材料 | 最多10个文件 | 只能上传1个文件 | 扩展信息部分 |
| 有效控制措施佐证材料 | 最多10个文件 | 只能上传1个文件 | 扩展信息部分 |
| 个人信息采集佐证材料 | 最多10个文件 | 只能上传1个文件 | 扩展信息部分 |

### 2. 技术实现修改

#### 2.1 移除 multiple 属性
```vue
<!-- 修改前 -->
<a-upload
  :limit="5"
  multiple
  @change="handleFileChange"
>

<!-- 修改后 -->
<a-upload
  :limit="1"
  @change="handleFileChange"
>
```

#### 2.2 更新 limit 属性
- 数据样本文件：`:limit="5"` → `:limit="1"`
- 所有佐证材料：`:limit="10"` → `:limit="1"`

#### 2.3 更新提示文案
```vue
<!-- 修改前 -->
<div class="upload-tip">
  支持格式：PDF、Word、Excel、PPT、图片、压缩包等，
  单个文件不超过100MB，最多上传5个文件
</div>

<!-- 修改后 -->
<div class="upload-tip">
  支持格式：PDF、Word、Excel、PPT、图片、压缩包等，
  单个文件不超过100MB，只能上传1个文件
</div>
```

### 3. 注释更新

#### 3.1 变量注释更新
```typescript
// 修改前
// === 文件上传列表 ===
const fileList = ref([]); // 数据样本文件列表
const sourceEvidenceFiles = ref([]); // 数据来源佐证材料文件列表

// 修改后
// === 文件上传（每项限制1个文件） ===
const fileList = ref([]); // 数据样本文件（限制1个）
const sourceEvidenceFiles = ref([]); // 数据来源佐证材料文件（限制1个）
```

#### 3.2 函数注释更新
```typescript
// 修改前
// 2. 收集各类文件的上传路径
const dataSampleFilePaths = collectFilePaths(fileList.value); // 数据样本文件路径

// 修改后
// 2. 收集各类文件的上传路径（每项限制1个文件）
const dataSampleFilePaths = collectFilePaths(fileList.value); // 数据样本文件路径
```

#### 3.3 提交数据注释更新
```typescript
// 修改前
dataSampleFile: dataSampleFilePaths, // 数据样本文件路径，上传的样本文件地址

// 修改后
dataSampleFile: dataSampleFilePaths, // 数据样本文件路径，上传的样本文件地址（限制1个）
```

### 4. 保持不变的功能

1. **文件路径收集逻辑**: `collectFilePaths()` 函数保持不变，仍然支持处理多个文件路径（虽然现在每项只有1个）
2. **文件验证**: 文件类型和大小验证逻辑保持不变
3. **上传机制**: 文件上传的核心逻辑和错误处理保持不变
4. **数据提交**: 提交到后端的数据格式保持不变

### 5. 用户体验影响

#### 5.1 正面影响
- 简化了用户操作，避免上传过多文件
- 减少了文件管理的复杂性
- 降低了服务器存储压力
- 提高了页面加载和提交速度

#### 5.2 注意事项
- 用户需要选择最重要的佐证材料进行上传
- 如果需要多个文件，可能需要将文件合并或压缩后上传
- 需要在用户指南中说明文件上传限制

### 6. 测试建议

1. **功能测试**
   - 验证每个上传组件只能选择1个文件
   - 测试文件上传成功和失败的情况
   - 验证文件路径正确收集和提交

2. **用户体验测试**
   - 确认提示文案清晰明确
   - 验证上传按钮在达到限制后的行为
   - 测试文件替换功能

3. **兼容性测试**
   - 确保修改不影响现有的文件上传功能
   - 验证与后端接口的兼容性

### 7. 后续优化建议

1. **文件压缩功能**: 考虑添加文件压缩功能，允许用户上传压缩包
2. **文件预览**: 为上传的文件添加预览功能
3. **拖拽上传**: 支持拖拽方式上传文件
4. **进度显示**: 优化文件上传进度显示

## 总结

此次修改成功将所有文件上传组件限制为单文件上传，简化了用户操作流程，同时保持了原有功能的完整性。修改涉及4个文件上传组件，更新了相关注释和提示文案，确保了代码的可维护性和用户体验的一致性。
