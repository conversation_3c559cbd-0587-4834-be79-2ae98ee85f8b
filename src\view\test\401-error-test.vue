<!-- 401错误处理测试页面 -->
<template>
  <div class="error-test-container">
    <h2>401错误处理测试</h2>
    <div class="test-buttons">
      <a-button type="primary" @click="testProHttp401"> 测试 proHttp 401错误 </a-button>
      <a-button type="primary" @click="testSopHttp401"> 测试 sopHttp 401错误 </a-button>
      <a-button type="outline" @click="testBusinessCode9"> 测试业务状态码9 </a-button>
    </div>

    <div class="test-info">
      <h3>测试说明：</h3>
      <ul>
        <li>点击按钮会模拟401错误或业务状态码9的情况</li>
        <li>应该显示"登录已过期，请重新登录"的提示</li>
        <li>用户登录状态会被清除</li>
        <li>页面会自动跳转到登录页面</li>
      </ul>
    </div>

    <div class="current-status">
      <h3>当前状态：</h3>
      <p>登录状态: {{ isLoggedIn ? '已登录' : '未登录' }}</p>
      <p>Token: {{ userToken || '无' }}</p>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { ref, computed } from 'vue';
  import { useUserStore } from '@/store/index';
  import { storeToRefs } from 'pinia';
  import proHttp from '@/http/proHttp';
  import sopHttp from '@/http/sopHttp';
  import { Message } from '@arco-design/web-vue';

  const userStore = useUserStore();
  const { user_token } = storeToRefs(userStore);

  const isLoggedIn = computed(() => !!user_token.value);
  const userToken = computed(() => user_token.value);

  // 测试proHttp的401错误
  const testProHttp401 = async () => {
    try {
      Message.info('正在测试 proHttp 401错误...');
      // 调用一个需要认证的接口，但使用无效token或过期token
      await proHttp.get('/app-api/member/user/get');
    } catch (error) {
      console.log('proHttp 401错误测试完成:', error);
    }
  };

  // 测试sopHttp的401错误
  const testSopHttp401 = async () => {
    try {
      Message.info('正在测试 sopHttp 401错误...');
      // 调用一个需要认证的接口
      await sopHttp.get('/api/test/auth-required');
    } catch (error) {
      console.log('sopHttp 401错误测试完成:', error);
    }
  };

  // 测试业务状态码9
  const testBusinessCode9 = async () => {
    try {
      Message.info('正在测试业务状态码9...');
      // 模拟返回业务状态码9的响应
      // 这里可以调用一个实际会返回code=9的接口
      // 或者通过mock的方式模拟
      console.log('业务状态码9测试 - 需要实际的接口支持');
      Message.warning('此测试需要后端接口返回 code=9 的响应');
    } catch (error) {
      console.log('业务状态码9测试完成:', error);
    }
  };
</script>

<style scoped>
  .error-test-container {
    padding: 20px;
    max-width: 800px;
    margin: 0 auto;
  }

  .test-buttons {
    margin: 20px 0;
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
  }

  .test-info {
    margin: 20px 0;
    padding: 15px;
    background-color: #f5f5f5;
    border-radius: 4px;
  }

  .test-info h3 {
    margin-top: 0;
    color: #1890ff;
  }

  .test-info ul {
    margin: 10px 0;
    padding-left: 20px;
  }

  .test-info li {
    margin: 5px 0;
  }

  .current-status {
    margin: 20px 0;
    padding: 15px;
    background-color: #f0f9ff;
    border-radius: 4px;
    border-left: 4px solid #1890ff;
  }

  .current-status h3 {
    margin-top: 0;
    color: #1890ff;
  }

  .current-status p {
    margin: 5px 0;
    font-family: monospace;
  }
</style>
