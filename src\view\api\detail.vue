<template>
  <div class="module">
    <a-page-header
      :style="{ background: 'var(--color-bg-2)' }"
      :title="pageTitle"
      subtitle="开放文档"
      @back="pageBack"
    />
    <a-scrollbar style="height: 90vh; overflow: auto">
      <div v-if="HtmlText" style="height: 90vh" class="py-2" v-html="HtmlText" />
      <a-skeleton v-else style="width: 100%" animation>
        <a-space direction="vertical" :style="{ width: '100%' }" size="large">
          <a-skeleton-line :rows="5" />
        </a-space>
      </a-skeleton>
      <a-table :data="apiList">
        <template #columns>
          <a-table-column title="api名称" data-index="name" />
          <a-table-column title="api描述" data-index="desc" />
          <a-table-column title="操作">
            <template #cell="{ record }">
              <a-link @click="handleLink(record, 1)"> 查看文档 </a-link>
              <a-link @click="handleLink(record, 2)"> 去调试 </a-link>
            </template>
          </a-table-column>
        </template>
      </a-table>
    </a-scrollbar>
  </div>
</template>

<script lang="ts" setup>
  import { ref } from 'vue';
  import { getApiInfoList, getApiInfo } from '@/http/api/global';
  import { useRouter } from 'vue-router';

  // 定义API项目的接口
  interface ApiItem {
    name: string;
    desc: string;
    routeName: string;
    [key: string]: any;
  }

  const router = useRouter();

  // 使用可选链和默认值处理title可能为null的情况
  const pageTitle = ref(router.currentRoute.value.query.title?.toString() || '详情');
  // 确保apiID是string类型
  const apiID = router.currentRoute.value.query.id?.toString() || '';
  const HtmlText = ref('');

  const getApiInfoFn = function () {
    if (apiID) {
      getApiInfo(apiID).then(res => {
        HtmlText.value = res as string;
      });
    }
  };
  getApiInfoFn();

  // 为apiList提供正确的类型
  const apiList = ref<ApiItem[]>([]);
  const getApiInfoListFn = function () {
    if (apiID) {
      getApiInfoList(apiID).then(res => {
        apiList.value = res as ApiItem[];
      });
    }
  };
  getApiInfoListFn();

  const handleLink = (val: ApiItem, type: number) => {
    router.push('/apiDoc?id=' + val.routeName + '&type=' + type);
  };
  const pageBack = () => {
    router.back();
  };
</script>
<style lang="scss" scoped>
  .module {
    width: 1400px;
    margin: auto;
    background-color: white;
    padding: 20px;
  }

  .banner-img {
    height: 400px;
    width: 100vw;
  }
</style>
