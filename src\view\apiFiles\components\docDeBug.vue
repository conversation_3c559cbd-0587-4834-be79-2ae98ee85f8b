<template>
  <div v-if="docType">
    <h2>{{ docInfo.summary }}</h2>
    <a-form :model="deBugForm">
      <a-form-item label="网关地址" hidden>
        <a-input v-model="deBugForm.url" />
      </a-form-item>
      <a-form-item label="AppId">
        <a-input v-model="deBugForm.appKey" disabled />
      </a-form-item>
      <a-form-item label="应用私钥">
        <a-input v-model="deBugForm.privateKey" />
      </a-form-item>
      <!-- <a-form-item label="token">
				<a-input v-model="deBugForm.token" />
			</a-form-item> -->
    </a-form>
    <h2>请求参数</h2>
    <a-table :columns="contDataSann" :data="docInfo.requestParameters">
      <template #paramExampleCom="{ record }">
        <!-- <a-select v-model="record.paramExample" @change="()=>handleChange(rowIndex)">
					<a-option v-for="value of Object.keys(options)">{{value}}</a-option>
				</a-select> -->
        <!-- {{record}} -->
        <a-form v-if="record.type !== 'object'" :model="record" style="display: inline-block">
          <a-form-item
            v-if="record.type === 'file' || record.elementType === 'file'"
            prop="paramExample"
          >
            <a-upload
              action="/"
              :limit="1"
              :auto-upload="true"
              :file-list="fileList1"
              @change="fileChange"
              @error="fileFun(record)"
              @success="fileFun(record)"
            />
          </a-form-item>
          <a-form-item v-else prop="paramExample">
            <a-input v-model="record.paramExample" :size="'medium'" :placeholder="'参数值'" />
          </a-form-item>
        </a-form>
      </template>
    </a-table>
    <a-form size="mini" :model="docInfo">
      <a-form-item label="HttpMethod">
        <a-radio-group v-model="httpMethod">
          <a-radio
            v-for="method in docInfo.httpMethodList"
            :key="method"
            :value="method"
            :label="method"
          >
            {{ method }}
          </a-radio>
        </a-radio-group>
      </a-form-item>
    </a-form>
    <a-button :disabled="deBugForm.appKey ? false : true" @click="deBugSubmit"> 请求 </a-button>
    <a-tabs v-show="resultShow">
      <a-tab-pane key="1" title="请求信息">
        <a-textarea v-model="reqInfo" auto-size />
      </a-tab-pane>
      <a-tab-pane key="2" title="请求结果">
        <a-textarea v-model="resultContent" auto-size />
      </a-tab-pane>
    </a-tabs>
  </div>
</template>

<script lang="ts" setup>
  import { reactive, ref } from 'vue';
  import { deBugFun, getDocItem } from '@/http/api/apiFiles';

  // 定义接口类型
  interface FileItem {
    name: string;
    files: File[];
    [key: string]: any;
  }

  interface ValidationRule {
    required?: boolean;
    max?: number;
    message: string;
    trigger: string;
  }

  interface RequestParameter {
    name: string;
    type: string;
    description?: string;
    paramExample?: string;
    in?: string;
    required?: boolean;
    maxLength?: string;
    refs?: RequestParameter[];
    elementType?: string;
    __file__?: FileItem;
    [key: string]: any;
  }

  interface DocInfo {
    name: string;
    summary: string;
    httpMethodList: string[];
    requestParameters: RequestParameter[];
    version?: string;
    [key: string]: any;
  }

  interface DebugForm {
    url: string;
    appKey: string;
    privateKey: string;
    token: string;
  }

  interface TableColumn {
    title: string;
    dataIndex: string;
    width?: number;
    slotName?: string;
  }

  interface DebugResponse {
    status: number;
    headers: Record<string, string>;
    body?: any;
    data?: any;
    raw?: any;
  }

  const docType = ref<boolean>(false);
  const resultShow = ref<boolean>(false);
  const docInfo = ref<DocInfo>({
    name: '',
    summary: '',
    httpMethodList: [],
    requestParameters: [],
  });
  const fileList = ref<File[]>([]);
  const fileList1 = ref<any[]>([]);
  const httpMethod = ref<string>('');
  const resultContent = ref<string>('');
  const reqInfo = ref<string>('');
  const resultActive = ref<string>('');
  const deBugForm = reactive<DebugForm>({
    url: '',
    appKey: '',
    privateKey: '',
    token: '',
  });
  const contDataSann = ref<TableColumn[]>([
    {
      title: '名称',
      dataIndex: 'name',
      width: 250,
    },
    {
      title: '类型',
      dataIndex: 'type',
      width: 100,
    },
    {
      title: '参数值',
      dataIndex: 'paramExample',
      slotName: 'paramExampleCom',
      width: 200,
    },
    {
      title: '描述',
      dataIndex: 'description',
    },
  ]);

  const getApiInfo = async (item: any) => {
    deBugForm.appKey = item.appId;
    deBugForm.privateKey = item.privateKey;
    deBugForm.url = item.gatewayUrl;
    console.log(item);
    const response = await getDocItem({
      name: item.name,
    });
    docInfo.value = response as DocInfo;
    httpMethod.value = docInfo.value.httpMethodList[0];
    docType.value = true;
    return docInfo.value;
  };

  const fileChange = async (fileItem: any, b: any) => {
    fileList.value[0] = b.file;
  };

  const fileFun = async (row: RequestParameter) => {
    const files: File[] = [];
    fileList.value.forEach((file: File) => {
      const rawFile = file;
      files.push(rawFile);
    });
    row.__file__ = { name: row.name, files: files };
  };

  const deBugSubmit = async () => {
    const bizContent = await buildParamData(docInfo.value.requestParameters);
    const data = {
      gatewayUrl: deBugForm.url,
      appId: deBugForm.appKey,
      privateKey: deBugForm.privateKey,
      token: deBugForm.token,
      method: docInfo.value.name,
      version: docInfo.value.version,
      httpMethod: httpMethod.value,
      bizContent: JSON.stringify(bizContent),
    };
    const files = await buildFiles(docInfo.value.requestParameters);
    const isForm = httpMethod.value.toUpperCase() === 'POST';
    let deBugRes: DebugResponse = (await deBugFun({
      method: httpMethod.value,
      url: '/sandbox/test_v2',
      data,
      isForm,
      files,
    })) as DebugResponse;
    console.log(deBugRes);
    if (deBugRes.status == 200) {
      resultShow.value = true;
      resultActive.value = 'resultContent';
      successHandler(deBugRes);
    }
  };

  const successHandler = (response: DebugResponse) => {
    setReqInfo(response);
    setRespInfo(response);
  };

  const setReqInfo = (response: DebugResponse) => {
    const headers = response.headers;
    if (headers) {
      const html: string[] = [];
      html.push('【请求参数】：' + decodeURIComponent(headers['sendbox-params']));
      html.push('【待签名内容】：' + decodeURIComponent(headers['sendbox-beforesign']));
      html.push('【签名(sign)】：' + decodeURIComponent(headers['sendbox-sign']));
      reqInfo.value = html.join('\r\n');
    }
  };

  const setRespInfo = (response: DebugResponse) => {
    const headers = response.headers;
    const targetHeadersString = headers['target-response-headers'] || '{}';
    const targetHeaders = JSON.parse(targetHeadersString);
    const contentType = targetHeaders['content-type'] || '';
    const contentDisposition = targetHeaders['content-disposition'] || '';
    if (contentType.indexOf('stream') > -1 || contentDisposition.indexOf('attachment') > -1) {
      const filename = getDispositionFilename(contentDisposition);
      downloadFile(filename, response.raw);
    } else {
      const body = response.body || response.data;
      resultContent.value = JSON.stringify(body, null, 4);
    }
  };

  const downloadFile = (filename: string, buffer: any) => {
    const url = window.URL.createObjectURL(new Blob([buffer]));
    const link = document.createElement('a');
    link.href = url;
    link.setAttribute('download', filename);
    document.body.appendChild(link);
    link.click();
  };

  const getDispositionFilename = (disposition: string): string => {
    const dispositionArr = disposition.split(';');
    for (let i = 0; i < dispositionArr.length; i++) {
      const item = dispositionArr[i].trim();
      // filename="xx"
      if (item.toLowerCase().startsWith('filename')) {
        const result = item.match(new RegExp('filename="(.*?)"', 'i'));
        return result ? result[1] : '';
      }
    }
    return '';
  };

  const buildFiles = async (params: RequestParameter[]): Promise<FileItem[]> => {
    const files: FileItem[] = [];
    for (let i = 0; i < params.length; i++) {
      const row = params[i];
      // 处理文件上传
      let fileConfig = row.__file__;
      if (fileConfig) {
        files.push({ ...fileConfig });
      }
    }
    // 全局上传
    // if (fileList.value.length > 0) {
    // 	files.push({
    // 		name: 'file',
    // 		files: fileList.value
    // 	})
    // }
    return files;
  };

  const buildParamData = async (params: RequestParameter[]) => {
    const responseJson: Record<string, any> = {};
    for (let i = 0; i < params.length; i++) {
      const row = params[i];
      if (row.in === 'header') {
        continue;
      }
      let val: any;
      // 如果有子节点
      if (row.refs && row.refs.length > 0) {
        const childrenValue = buildParamData(row.refs);
        // 如果是数组
        if (row.type === 'array') {
          val = [childrenValue];
        } else {
          val = childrenValue;
        }
      } else {
        // 单值
        val = row.paramExample;
      }
      // 如果是数组类型
      if (row.type === 'array' && typeof row.paramExample === 'string') {
        val = row.paramExample.split(',');
      }
      responseJson[row.name] = val;
    }
    const isOneArray =
      Object.keys(responseJson).length === 1 && isArray(Object.values(responseJson)[0]);
    if (isOneArray) {
      return Object.values(responseJson)[0];
    }
    return responseJson;
  };

  const isArray = (obj: any): boolean => {
    return Object.prototype.toString.call(obj) === '[object Array]';
  };

  defineExpose({
    getApiInfo,
  });
</script>

<style scoped></style>
