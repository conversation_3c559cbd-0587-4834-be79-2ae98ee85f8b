<template>
  <a-layout>
    <a-layout-header>
      <Header />
    </a-layout-header>
    <a-layout-content style="padding-top: 58px;">
      <router-view />
    </a-layout-content>
    <a-layout-footer>
      <Footer />
    </a-layout-footer>
  </a-layout>
</template>

<script setup lang="ts">
import Header from "@/components/header.vue";
import Footer from "@/components/footer.vue";
import { useRouter } from "vue-router";
import { watch, onMounted } from "vue";

const router = useRouter();

// 更新页面标题和描述
const updateMetaInfo = (to: any) => {
  const { title, description } = to.meta;
  if (title) {
    document.title = title;
  } else {
    document.title = "文创链开放平台 - 数字文创区块链解决方案";
  }
  
  // 更新描述
  let descElement = document.querySelector('meta[name="description"]');
  if (description) {
    if (descElement) {
      descElement.setAttribute("content", description);
    } else {
      descElement = document.createElement("meta");
      descElement.setAttribute("name", "description");
      descElement.setAttribute("content", description);
      document.head.appendChild(descElement);
    }
  }
};

// 监听路由变化
watch(
  () => router.currentRoute.value,
  (to) => {
    updateMetaInfo(to);
  }
);

onMounted(() => {
  updateMetaInfo(router.currentRoute.value);
});
</script>

<style lang="scss" scoped>
:deep(.arco-menu-overflow-wrap) {
  display: flex;
  justify-content: flex-end;
}
</style>
