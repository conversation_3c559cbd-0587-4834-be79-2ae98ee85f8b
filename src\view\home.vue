<template>
  <div class="page">
    <section aria-label="主横幅轮播">
      <a-carousel
        show-arrow="hover"
        indicator-type="never"
        :auto-play="{ interval: 5000, hoverToPause: true }"
        :current="bannerIndex"
        :move-speed="600"
        :style="{
          width: '100%',
          height: '720px',
        }"
        @change="bannerChange"
      >
        <a-carousel-item v-for="(item, index) in bannerList" :key="item.id || index">
          <div class="banner">
            <img class="banner-img" :src="item.bannerCover" :alt="item.mainTitle" loading="lazy" />
            <div class="banner-title">
              <h1 class="text-gray-700 text-3xl font-bold">
                {{ item.subTitle }}
              </h1>
              <h2 class="text-gray-700 text-6xl font-bold mt-4">
                {{ item.mainTitle }}
              </h2>
              <p class="text-gray-700 mt-10 text-lg">
                {{ item.introduce }}
              </p>
              <div class="banner-btn cursor-pointer">
                <div>了解更多</div>
                <icon-arrow-right />
              </div>
            </div>
          </div>
        </a-carousel-item>
      </a-carousel>
    </section>

    <!-- 产品卡片 -->
    <section aria-label="产品导航">
      <div style="background-color: #ffffff">
        <div class="products-card flex items-center justify-center">
          <div
            v-for="(item, index) in bannerList"
            :key="item.id || index"
            class="text-center card-item p-6"
            :class="[index + 1 == bannerIndex ? 'active' : '']"
            @click="bannerChange(index + 1)"
          >
            <h3 class="text-xl flex items-center">
              <span>{{ item.mainTitle }}</span>
              <a-tag color="arcoblue" class="ml-2"> 产品 </a-tag>
            </h3>
            <p class="text-sm mt-2 text-two" style="max-width: 100%">
              {{ item.introduce }}
            </p>
          </div>
        </div>
      </div>
    </section>

    <!-- 产品能力 -->
    <section aria-label="产品能力">
      <div style="background-color: #ffffff">
        <div class="module pb-20 products-list" style="margin-top: 40px">
          <h2 class="text-4xl text-center py-20">文创链产品能力</h2>
          <a-row class="grid-demo" :gutter="24">
            <a-col v-for="(item, index) in productCapability" :key="item.id || index" :span="6">
              <article
                class="products-item flex flex-col justify-center items-center bg-white py-10 mb-6 rounded"
              >
                <img
                  style="max-width: 82px"
                  :src="item.iconImg"
                  :alt="item.mainTitle"
                  loading="lazy"
                />
                <h3 class="text-center text-lg font-medium">
                  {{ item.mainTitle }}
                </h3>
                <p class="text-sm text mt-1 text-center" style="max-width: 80%">
                  {{ item.subTitle }}
                </p>
              </article>
            </a-col>
          </a-row>
        </div>
      </div>
    </section>

    <!-- 优秀商家案例 -->
    <section aria-label="优秀商家案例">
      <div style="background-color: #ffffff">
        <div class="module pb-20 products-list">
          <h2 class="text-4xl text-center py-20">优秀商家案例</h2>
          <a-tabs>
            <a-tab-pane v-for="(item, index) in merchantCasesList" :key="item.id || index">
              <template #title>
                <div class="text-xl">
                  {{ item.column }}
                </div>
              </template>
              <article class="flex items-center justify-between p-6">
                <div
                  style="width: 50%; height: 50%; border-radius: 20px; flex-shrink: 0"
                  class="flex items-center justify-center"
                >
                  <img
                    style="width: 90%; height: 100%; border-radius: 20px"
                    :src="item.casesCover"
                    :alt="item.title"
                    loading="lazy"
                  />
                </div>
                <div class="p-10" style="width: 50%">
                  <h3 class="text-4xl font-medium mb-4 text-gray-700">
                    {{ item.title }}
                  </h3>
                  <p class="text-base text-gray-600">
                    {{ item.introduce }}
                  </p>
                </div>
              </article>
            </a-tab-pane>
          </a-tabs>
        </div>
      </div>
    </section>

    <!-- 接入文创链 -->
    <section aria-label="接入文创链">
      <div style="background-color: #eff5ff">
        <div class="module jion pb-20">
          <h2 class="text-4xl text-center py-20">接入文创链</h2>
          <a-steps type="arrow" :current="1">
            <a-step description="通过注册实名认证的文创链账号即可成为商家平台用户">
              登陆注册
            </a-step>
            <a-step description="选择产品并提交相关资料签约开通，约一个工作日即可通过审核">
              产品开通
            </a-step>
            <a-step description="部分产品可能需要一定的开发集成能力，完成接入即可上线使用">
              接入或使用
            </a-step>
          </a-steps>
          <div class="text-center py-10">
            <h3 class="text-2xl font-medium">立刻体验文化数据服务一站式解决方案</h3>
            <p class="text-lg mt-2">多年数据化经验沉淀，区块链数字经济艺术价值应用解决方案</p>
          </div>
          <div class="flex items-center justify-around px-40">
            <router-link
              to="/register"
              class="jion-btn jion-btn-register cursor-pointer"
              title="注册文创链账号"
            >
              <div>注册文创链</div>
              <icon-arrow-right />
            </router-link>
            <router-link
              to="/data/page"
              class="jion-btn jion-btn-add cursor-pointer"
              title="了解接入文创链方式"
            >
              <div>接入文创链</div>
              <icon-arrow-right />
            </router-link>
          </div>
        </div>
      </div>
    </section>
  </div>
</template>

<script lang="ts" setup>
  import { ref, onMounted } from 'vue';
  import { IconArrowRight } from '@arco-design/web-vue/es/icon';
  import '@/styles/hover-min.css';
  import { useRouter } from 'vue-router';
  import { getBannerList, getMerchantCasesList, getProductCapability } from '@/http/api/global';
  import { BannerItem, MerchantCase, ProductCapabilityItem } from '@/http/api/global/interface';

  const router = useRouter();

  // 轮播图
  const bannerIndex = ref(1);
  const bannerList = ref<BannerItem[]>([]);

  const getBanner = () => {
    getBannerList()
      .then((res: any) => {
        // Check for the console error structure format
        if (res && typeof res === 'object' && !res.code && !res.data && Array.isArray(res.list)) {
          bannerList.value = res.list.slice(0, 4).reverse();
          return;
        }

        // Check if res directly has the list structure
        if (res && Array.isArray(res.list)) {
          bannerList.value = res.list.slice(0, 4).reverse();
        }
        // Check if res is the standard API response structure
        else if (res && res.data && Array.isArray(res.data.list)) {
          bannerList.value = res.data.list.slice(0, 4).reverse();
        }
        // Additional check for non-standard response
        else if (res && res.code === '0' && res.data && res.data.list) {
          bannerList.value = res.data.list.slice(0, 4).reverse();
        }
      })
      .catch(error => {
        // If the error itself is the data, try to use it
        if (error && Array.isArray(error.list)) {
          bannerList.value = error.list.slice(0, 4).reverse();
          return;
        }
        console.error('Failed to fetch banner list:', error);
      });
  };
  getBanner();

  // 商家案例
  const merchantCasesList = ref<MerchantCase[]>([]);
  const getMerchantCases = () => {
    getMerchantCasesList()
      .then((res: any) => {
        // Check for the console error structure format
        if (res && typeof res === 'object' && !res.code && !res.data && Array.isArray(res.list)) {
          merchantCasesList.value = res.list.reverse();
          return;
        }

        // Check if res directly has the list structure
        if (res && Array.isArray(res.list)) {
          merchantCasesList.value = res.list.reverse();
        }
        // Check if res is the standard API response structure
        else if (res && res.data && Array.isArray(res.data.list)) {
          merchantCasesList.value = res.data.list.reverse();
        }
        // Additional check for non-standard response
        else if (res && res.code === '0' && res.data && res.data.list) {
          merchantCasesList.value = res.data.list.reverse();
        }
      })
      .catch(error => {
        // If the error itself is the data, try to use it
        if (error && Array.isArray(error.list)) {
          merchantCasesList.value = error.list.reverse();
          return;
        }
        console.error('Failed to fetch merchant cases:', error);
      });
  };
  getMerchantCases();

  const productCapability = ref<ProductCapabilityItem[]>([]);
  const getProductCapabilityFn = () => {
    getProductCapability()
      .then((res: any) => {
        // Check for the console error structure format
        if (res && typeof res === 'object' && !res.code && !res.data && Array.isArray(res.list)) {
          productCapability.value = res.list.reverse();
          return;
        }

        // Check if res directly has the list structure
        if (res && Array.isArray(res.list)) {
          productCapability.value = res.list.reverse();
        }
        // Check if res is the standard API response structure
        else if (res && res.data && Array.isArray(res.data.list)) {
          productCapability.value = res.data.list.reverse();
        }
        // Additional check for non-standard response
        else if (res && res.code === '0' && res.data && res.data.list) {
          productCapability.value = res.data.list.reverse();
        }
      })
      .catch(error => {
        // If the error itself is the data, try to use it
        if (error && Array.isArray(error.list)) {
          productCapability.value = error.list.reverse();
          return;
        }
        console.error('Failed to fetch product capabilities:', error);
      });
  };
  getProductCapabilityFn();

  const bannerChange = function (index: number) {
    console.log(index);
    bannerIndex.value = index;
  };
  // 在 script 部分新增方法
  const openExternalLink = () => {
    window.open('https://www.bmark.cn/copyrightRegistration/evidence', '_blank');
    // router.push('/software/page')
  };
  onMounted(() => {});
</script>

<style lang="scss" scoped>
  .page {
    width: 100vw;

    .menu {
      position: relative;
      z-index: 1;
      background-color: #ffffff;

      :deep(.arco-menu-overflow-wrap) {
        justify-content: flex-start !important;
      }
    }

    .banner {
      position: relative;
      height: 720px;

      .banner-img {
        position: absolute;
        left: 0;
        top: 0;
        width: 100%;
        height: 720px;
      }

      .banner-title {
        width: 34vw;
        position: absolute;
        left: 16vw;
        top: 24vh;
      }

      .banner-btn {
        color: #fff;
        width: 240px;
        height: 50px;
        border-radius: 5px;
        padding: 0 20px;
        background-color: #165dff;
        display: flex;
        align-items: center;
        justify-content: space-between;
        font-weight: 500;
        margin-top: 40px;
        font-size: 20px;
        transition: background 400ms;
      }

      .banner-btn:hover {
        background-color: #168cff;
      }
    }

    // 产品卡片
    .products-card {
      width: 1200px;
      margin: -40px auto;
      position: relative;
      z-index: 2;
      box-shadow: 0px 0px 3px rgba(26, 26, 26, 0.2);

      .card-item {
        width: 400px;
        height: 120px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        background-color: #ffffff;
        transition: all 0.4s;
      }

      .card-item.active {
        background: #165dff;
        color: #ffffff;
        z-index: 2;
      }
    }

    // 产品能力
    .products-list {
      .products-item {
        padding-top: 20px;
        background-color: #eff5ff;
        box-shadow: 0px 4px 10px 0px rgb(206 227 248 / 50%);
        border-radius: 4px;
        min-height: 274px;
        transition:
          box-shadow 0.4s,
          padding-top 0.4s;

        .text {
          color: #606266;
        }
      }

      .products-item:hover {
        padding-top: 0px;
        background-image: url(https://src.fanruan.com/website/finereport/index_function_hoverbg.png);
        background-size: cover;
        background-repeat: no-repeat;
        background-position: center;
        box-shadow: 0px 2px 8px 0px rgb(0 105 234 / 40%);
        color: #ffffff;

        .text {
          color: #ffffff;
        }
      }
    }

    // 加入文创链
    .jion {
      .jion-btn {
        width: 340px;
        height: 60px;
        border-radius: 5px;
        padding: 0 30px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        font-weight: 500;
        margin-top: 20px;
        font-size: 20px;
        transition: background 400ms;
      }

      .jion-btn-register {
        color: #165dff;
        background-color: #fff;
        border: 1px solid #165dff;
      }

      .jion-btn-register:hover {
        background-color: #165dff;
        color: #ffffff;
      }

      .jion-btn-add {
        color: #fff;
        background-color: #165dff;
      }

      .jion-btn-add:hover {
        background-color: #168cff;
      }
    }

    .module {
      width: 1200px;
      margin: auto;

      // tab居中显示
      :deep(.arco-tabs-nav-tab) {
        justify-content: center !important;
      }

      // 步骤条背景色白色
      :deep(.arco-steps-item-wait) {
        background-color: #ffffff;
      }

      // 箭头颜色蓝边
      :deep(.arco-steps-item:not(:first-child)::before) {
        border-left: 36px solid #eff5ff;
      }

      // 箭头颜色白色
      :deep(.arco-steps-item:not(:last-child).arco-steps-item-wait::after) {
        border-left: 36px solid #ffffff;
      }
    }
  }
</style>
