<template>
  <div class="p-4" style="width: 1200px; margin: 0 auto; padding: 0 100px; min-height: 720px">
    <template v-if="userInfo.auditStatus !== null">
      <a-descriptions
        :data="organDetail"
        title="企业信息"
        size="large"
        :column="3"
        layout="inline-vertical"
        :value-style="{
          width: '300px',
          marginBottom: '10px',
        }"
      />
      <a-divider />
      <a-descriptions
        :data="bankDetail"
        title="支付信息"
        size="large"
        :column="3"
        layout="inline-vertical"
        :value-style="{
          width: '300px',
          marginBottom: '10px',
        }"
      />
      <a-divider />
      <a-descriptions
        :data="userDetail"
        title="法人信息"
        size="large"
        :column="3"
        layout="inline-vertical"
        :value-style="{
          width: '300px',
          marginBottom: '10px',
        }"
      />
      <a-button type="primary" :disabled="btnType" @click="uacSubmit">
        确认信息，开始认证
      </a-button>
    </template>
    <a-modal
      v-model:visible="bankType"
      :hide-cancel="true"
      :closable="false"
      :mask-closable="false"
      :on-before-ok="handleBeforeOk"
      unmount-on-close
      @cancel="bankType = false"
    >
      <div>
        <div style="padding: 10px">请输入金额：</div>
        <a-input v-model="bankData" placeholder="转账金额" />
        <a-button style="margin-top: 20px" type="primary" @click="uacAgeinSubmit">
          重新发送金额
        </a-button>
      </div>
    </a-modal>
  </div>
</template>

<script lang="ts" setup>
  import { ref, onMounted, computed } from 'vue';
  import { Message } from '@arco-design/web-vue';
  import {
    portalUserInfo,
    chinaumsMerchat,
    getValidationFirst,
    getValidationSecond,
    chinaumsMerchatProcess,
    chinaumsMerchatAgreementSign,
  } from '@/http/api/authentication/uac';

  const bankType = ref<boolean>(false);
  const btnType = ref<boolean>(false);
  const bankData = ref<any>('');
  const countDown = ref<number>(120);

  // 校验余额是否正确
  const handleBeforeOk = async () => {
    let secondRes = await getValidationSecond({
      transAmt: bankData.value,
    });
    window.location.href = secondRes as string;
  };

  const uacSubmit = async () => {
    await chinaumsMerchat();
    bankType.value = true;
  };

  const uacAgeinSubmit = async () => {
    // if( countDown.value != 120 ) return
    let uacRes = await getValidationFirst();
    console.log(uacRes);
  };

  const userInfo = ref({
    auditRemark: '',
    auditStatus: null,
    bankAcctName: '',
    bankAcctNum: '',
    bankName: '',
    branchBankName: '',
    businessAddress: '',
    businessScope: '',
    certificateTypeName: '',
    city: '',
    companyEmail: '',
    companyPhone: '',
    county: '',
    effectiveDate: '',
    idCardNo: '',
    legalAddress: '',
    legalEmail: '',
    legalName: '',
    legalPhone: '',
    province: '',
    registerArea: '',
    registerName: '',
    registerNo: '',
    typeName: '',
  });
  const getUserInfo = () => {
    portalUserInfo().then(res => {
      userInfo.value = res as {
        auditRemark: '';
        auditStatus: null;
        bankAcctName: '';
        bankAcctNum: '';
        bankName: '';
        branchBankName: '';
        businessAddress: '';
        businessScope: '';
        certificateTypeName: '';
        city: '';
        companyEmail: '';
        companyPhone: '';
        county: '';
        effectiveDate: '';
        idCardNo: '';
        legalAddress: '';
        legalEmail: '';
        legalName: '';
        legalPhone: '';
        province: '';
        registerArea: '';
        registerName: '';
        registerNo: '';
        typeName: '';
      };
    });
  };

  const organDetail = computed(() => {
    return [
      {
        label: '企业名称',
        value: userInfo.value.registerName,
      },
      {
        label: '企业类型',
        value: userInfo.value.typeName,
      },
      {
        label: '企业证件类型',
        value: userInfo.value.certificateTypeName,
      },
      {
        label: '统一社会信用代码',
        value: userInfo.value.registerNo,
      },
      {
        label: '注册区域',
        value: userInfo.value.registerArea,
      },
      {
        label: '公司营业地址',
        value: userInfo.value.businessAddress,
      },
      {
        label: '主营业务/业务范围',
        value: userInfo.value.businessScope,
      },
      {
        label: '企业邮箱',
        value: userInfo.value.companyEmail,
      },
    ];
  });
  const userDetail = computed(() => {
    return [
      {
        label: '法人姓名',
        value: userInfo.value.legalName,
      },
      {
        label: '法人手机号',
        value: userInfo.value.legalPhone,
      },
      {
        label: '法人邮箱',
        value: userInfo.value.legalEmail,
      },
      {
        label: '法人身份证号',
        value: userInfo.value.idCardNo,
      },
      {
        label: '有效日期',
        value: userInfo.value.effectiveDate,
      },
      {
        label: '法人联系地址',
        value: userInfo.value.legalAddress,
      },
    ];
  });
  const bankDetail = computed(() => {
    return [
      {
        label: '银行名称',
        value: userInfo.value.bankName,
      },
      {
        label: '银行账号名称',
        value: userInfo.value.bankAcctName,
      },
      {
        label: '银行账号号码',
        value: userInfo.value.bankAcctNum,
      },
      {
        label: '支行名称',
        value: userInfo.value.branchBankName,
      },
    ];
  });

  const process = async () => {
    let stupeType: any = await chinaumsMerchatProcess();
    console.log(stupeType.status);
    switch (stupeType.status) {
      case '00': {
        // 签约中
        let secondRes = await chinaumsMerchatAgreementSign();
        window.location.href = secondRes as string;
        break;
      }

      case '02': {
        // 入网审核中
        btnType.value = true;
        Message.error('已在入网审核中');
        break;
      }

      case '05': {
        // 对公账户待验证
        bankType.value = true;
        break;
      }

      default:
        break;
    }
  };

  onMounted(async () => {
    getUserInfo();
    process();
  });
</script>

<style lang="scss" scoped>
  .pageBtns {
    padding: 50px 200px;
    display: flex;
    justify-content: space-around;
  }
</style>
