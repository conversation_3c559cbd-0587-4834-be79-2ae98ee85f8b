<template>
  <div class="payment-calculation-container">
    <a-card class="calculation-card" :bordered="false">
      <template #title>
        <div class="calculation-header">
          <icon-info-circle class="calculation-icon" />
          <span class="calculation-title">费用明细</span>
        </div>
      </template>

      <div v-if="loading" class="loading-container">
        <a-spin :size="24" />
        <p class="loading-text">正在计算费用...</p>
      </div>

      <div v-else class="calculation-content">
        <!-- 原始金额 -->
        <div class="calculation-section">
          <div class="amount-row base-amount">
            <span class="amount-label">商品金额</span>
            <span class="amount-value">¥{{ formatAmount(calculation.originalAmount) }}</span>
          </div>
        </div>

        <!-- 优惠明细 -->
        <div v-if="hasDiscounts" class="calculation-section discount-section">
          <h5 class="section-title">优惠明细</h5>

          <!-- 优惠券优惠 -->
          <div v-if="calculation.couponDiscount > 0" class="amount-row discount-row">
            <span class="amount-label">
              <icon-gift class="discount-icon" />
              优惠券优惠
              <a-tag v-if="selectedCoupons.length > 0" color="orange" size="small" class="ml-1">
                {{ selectedCoupons.length }}张
              </a-tag>
            </span>
            <span class="amount-value discount-value">
              -¥{{ formatAmount(calculation.couponDiscount) }}
            </span>
          </div>

          <!-- 积分抵扣 -->
          <div v-if="calculation.pointsUsed > 0" class="amount-row discount-row">
            <span class="amount-label">
              <icon-star class="discount-icon" />
              积分抵扣
              <span class="points-info">（{{ formatPoints(calculation.pointsUsed) }}）</span>
            </span>
            <span class="amount-value discount-value">
              -¥{{ formatAmount(calculation.pointsValue) }}
            </span>
          </div>

          <!-- 余额抵扣 -->
          <div v-if="calculation.balanceUsed > 0" class="amount-row discount-row">
            <span class="amount-label">
              <icon-user class="discount-icon" />
              余额抵扣
            </span>
            <span class="amount-value discount-value">
              -¥{{ formatAmount(calculation.balanceUsed) }}
            </span>
          </div>
        </div>

        <!-- 分割线 -->
        <a-divider class="calculation-divider" />

        <!-- 支付方式分布 -->
        <div v-if="hasPaymentMethods" class="calculation-section payment-methods-section">
          <h5 class="section-title">支付方式</h5>

          <div v-if="calculation.onlinePayAmount > 0" class="amount-row payment-row">
            <span class="amount-label">
              <icon-check-circle class="payment-icon" />
              在线支付
            </span>
            <span class="amount-value">¥{{ formatAmount(calculation.onlinePayAmount) }}</span>
          </div>

          <div v-if="calculation.balanceUsed > 0" class="amount-row payment-row">
            <span class="amount-label">
              <icon-user class="payment-icon" />
              余额支付
            </span>
            <span class="amount-value">¥{{ formatAmount(calculation.balanceUsed) }}</span>
          </div>

          <div v-if="calculation.pointsValue > 0" class="amount-row payment-row">
            <span class="amount-label">
              <icon-star class="payment-icon" />
              积分支付
            </span>
            <span class="amount-value">¥{{ formatAmount(calculation.pointsValue) }}</span>
          </div>
        </div>

        <!-- 最终金额 -->
        <div class="calculation-section final-section">
          <div class="amount-row final-amount">
            <span class="amount-label final-label">实付金额</span>
            <span class="amount-value final-value"
              >¥{{ formatAmount(calculation.finalAmount) }}</span
            >
          </div>

          <!-- 节省金额 -->
          <div v-if="calculation.savings > 0" class="savings-info">
            <span class="savings-text"> 已为您节省 ¥{{ formatAmount(calculation.savings) }} </span>
            <icon-check-circle class="savings-icon" />
          </div>
        </div>

        <!-- 计算说明 -->
        <div class="calculation-notes">
          <a-alert type="info" size="small" :show-icon="false">
            <template #icon>
              <icon-info-circle />
            </template>
            <div class="notes-content">
              <p v-if="calculation.pointsUsed > 0">• 积分按 {{ pointsRate }}:1 的比例抵扣现金</p>
              <p v-if="calculation.couponDiscount > 0">• 优惠券优惠已自动计算最优方案</p>
              <p v-if="calculation.balanceUsed > 0">• 余额支付安全便捷，无手续费</p>
              <p>• 实际支付金额以订单确认页为准</p>
            </div>
          </a-alert>
        </div>

        <!-- 重新计算按钮 -->
        <div v-if="hasError" class="recalculate-section">
          <a-button type="outline" @click="handleRecalculate">
            <template #icon>
              <icon-refresh />
            </template>
            重新计算
          </a-button>
        </div>
      </div>
    </a-card>
  </div>
</template>

<script setup lang="ts">
  import { computed } from 'vue';
  import {
    IconGift,
    IconStar,
    IconUser,
    IconCheckCircle,
    IconInfoCircle,
    IconRefresh,
  } from '@arco-design/web-vue/es/icon';
  import { usePaymentStore } from '@/store/modules/payment';
  import type { PaymentCalculation } from '@/http/api/payment/types';

  // Props
  interface Props {
    calculation: PaymentCalculation;
    loading?: boolean;
    error?: string | null;
  }

  const props = withDefaults(defineProps<Props>(), {
    loading: false,
    error: null,
  });

  // Emits
  const emit = defineEmits(['recalculate']);

  // Store
  const paymentStore = usePaymentStore();

  // 计算属性
  const selectedCoupons = computed(() => paymentStore.selectedCoupons);
  const pointsRate = computed(() => paymentStore.userAssets?.pointsRate || 100);

  const hasDiscounts = computed(
    () =>
      props.calculation.couponDiscount > 0 ||
      props.calculation.pointsValue > 0 ||
      props.calculation.balanceUsed > 0
  );

  const hasPaymentMethods = computed(
    () =>
      props.calculation.onlinePayAmount > 0 ||
      props.calculation.balanceUsed > 0 ||
      props.calculation.pointsValue > 0
  );

  const hasError = computed(() => !!props.error);

  // 方法
  function formatAmount(amount: number): string {
    return amount.toFixed(2);
  }

  function formatPoints(points: number): string {
    return points.toLocaleString() + '积分';
  }

  function handleRecalculate() {
    emit('recalculate');
  }
</script>

<style scoped lang="scss">
  .payment-calculation-container {
    width: 100%;
  }

  .calculation-card {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border-radius: 8px;

    :deep(.arco-card-header) {
      border-bottom: 1px solid #f2f3f5;
      padding: 16px 20px;
    }

    :deep(.arco-card-body) {
      padding: 20px;
    }
  }

  .calculation-header {
    display: flex;
    align-items: center;
    gap: 8px;

    .calculation-icon {
      font-size: 18px;
      color: $primary-color;
    }

    .calculation-title {
      font-size: 16px;
      font-weight: 600;
      color: $text-color;
    }
  }

  .loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px 20px;

    .loading-text {
      margin-top: 16px;
      color: $text-color-secondary;
    }
  }

  .calculation-content {
    display: flex;
    flex-direction: column;
    gap: 16px;
  }

  .calculation-section {
    .section-title {
      font-size: 14px;
      font-weight: 600;
      color: $text-color;
      margin: 0 0 12px 0;
    }
  }

  .amount-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;

    &:last-child {
      margin-bottom: 0;
    }

    .amount-label {
      display: flex;
      align-items: center;
      gap: 6px;
      font-size: 14px;
      color: $text-color-secondary;

      .discount-icon,
      .payment-icon {
        font-size: 14px;
      }

      .points-info {
        font-size: 12px;
        color: $text-color-tertiary;
      }
    }

    .amount-value {
      font-size: 14px;
      color: $text-color;
      font-weight: 500;
    }

    &.base-amount {
      .amount-label {
        color: $text-color;
        font-weight: 500;
      }

      .amount-value {
        font-weight: 600;
      }
    }

    &.discount-row {
      .discount-value {
        color: $success-color;
        font-weight: 600;
      }
    }

    &.final-amount {
      padding: 12px 0;
      border-top: 1px solid #f2f3f5;
      border-bottom: 1px solid #f2f3f5;
      margin: 8px 0;

      .final-label {
        font-size: 16px;
        font-weight: 600;
        color: $text-color;
      }

      .final-value {
        font-size: 20px;
        font-weight: 700;
        color: $primary-color;
      }
    }
  }

  .discount-section {
    background-color: #f0f9ff;
    border-radius: 6px;
    padding: 12px;
    border: 1px solid #e0f2fe;
  }

  .payment-methods-section {
    background-color: #f7f8fa;
    border-radius: 6px;
    padding: 12px;
  }

  .final-section {
    background: linear-gradient(135deg, #e8f3ff 0%, #f0f8ff 100%);
    border-radius: 8px;
    padding: 16px;
    border: 1px solid #d1e7ff;
  }

  .savings-info {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 6px;
    margin-top: 8px;

    .savings-text {
      font-size: 13px;
      color: $success-color;
      font-weight: 500;
    }

    .savings-icon {
      font-size: 14px;
      color: $success-color;
    }
  }

  .calculation-divider {
    margin: 8px 0;
  }

  .calculation-notes {
    background-color: #f0f8ff;
    border-radius: 6px;
    padding: 12px;

    .notes-content {
      p {
        margin: 0 0 4px 0;
        font-size: 12px;
        color: $text-color-secondary;

        &:last-child {
          margin-bottom: 0;
        }
      }
    }

    :deep(.arco-alert) {
      background-color: transparent;
      border: none;
      padding: 0;
    }

    :deep(.arco-alert-icon) {
      color: $primary-color;
    }
  }

  .recalculate-section {
    display: flex;
    justify-content: center;
    padding-top: 8px;
  }

  // 响应式设计
  @media (max-width: 768px) {
    .calculation-card {
      :deep(.arco-card-header) {
        padding: 12px 16px;
      }

      :deep(.arco-card-body) {
        padding: 16px;
      }
    }

    .calculation-content {
      gap: 12px;
    }

    .amount-row {
      .amount-label {
        font-size: 13px;
      }

      .amount-value {
        font-size: 13px;
      }

      &.final-amount {
        .final-label {
          font-size: 15px;
        }

        .final-value {
          font-size: 18px;
        }
      }
    }

    .discount-section,
    .payment-methods-section {
      padding: 10px;
    }

    .final-section {
      padding: 12px;
    }

    .calculation-notes {
      padding: 10px;
    }
  }
</style>
