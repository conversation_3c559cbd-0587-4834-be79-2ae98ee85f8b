<template>
  <div class="payment-confirmation-container">
    <a-card class="confirmation-card" :bordered="false">
      <div class="confirmation-content">
        <!-- 支付金额显示 -->
        <div class="payment-amount-section">
          <div class="amount-display">
            <span class="amount-label">实付金额</span>
            <span class="amount-value">¥{{ formatAmount(finalAmount) }}</span>
          </div>

          <div v-if="savings > 0" class="savings-display">
            <span class="savings-text">已节省 ¥{{ formatAmount(savings) }}</span>
          </div>
        </div>

        <!-- 支付方式摘要 -->
        <div v-if="hasSelectedMethods" class="payment-summary">
          <div class="summary-title">支付方式</div>
          <div class="summary-methods">
            <div v-for="method in selectedMethodsSummary" :key="method.type" class="method-summary">
              <span class="method-name">{{ method.name }}</span>
              <span class="method-amount">¥{{ formatAmount(method.amount) }}</span>
            </div>
          </div>
        </div>

        <!-- 支付协议 -->
        <div class="payment-agreement">
          <a-checkbox v-model="agreedToTerms" class="agreement-checkbox">
            我已阅读并同意
            <a-link @click="showTermsModal = true">《支付服务协议》</a-link>
            和
            <a-link @click="showPrivacyModal = true">《隐私政策》</a-link>
          </a-checkbox>
        </div>

        <!-- 支付按钮 -->
        <div class="payment-actions">
          <a-button
            type="primary"
            size="large"
            :loading="submitting"
            :disabled="!canSubmit"
            class="pay-button"
            @click="handlePayment"
          >
            <template #icon>
              <icon-safe v-if="!submitting" />
            </template>
            {{ getPayButtonText() }}
          </a-button>

          <a-button
            v-if="showCancelButton"
            size="large"
            class="cancel-button"
            @click="handleCancel"
          >
            取消支付
          </a-button>
        </div>

        <!-- 安全提示 -->
        <div class="security-tips">
          <div class="tips-content">
            <icon-safe class="security-icon" />
            <div class="tips-text">
              <p>支付过程采用银行级SSL加密技术</p>
              <p>您的资金安全由平台全程保障</p>
            </div>
          </div>
        </div>

        <!-- 验证错误提示 -->
        <div v-if="validationErrors.length > 0" class="validation-errors">
          <a-alert type="error" :show-icon="false">
            <template #icon>
              <icon-exclamation-circle />
            </template>
            <div class="error-content">
              <p v-for="error in validationErrors" :key="error">{{ error }}</p>
            </div>
          </a-alert>
        </div>
      </div>
    </a-card>

    <!-- 支付服务协议弹窗 -->
    <a-modal v-model:visible="showTermsModal" title="支付服务协议" :width="600" :footer="false">
      <div class="modal-content">
        <h4>1. 服务说明</h4>
        <p>本平台为用户提供安全、便捷的支付服务...</p>

        <h4>2. 用户权利与义务</h4>
        <p>用户有权选择合适的支付方式...</p>

        <h4>3. 平台责任</h4>
        <p>平台承诺保护用户资金安全...</p>

        <h4>4. 争议解决</h4>
        <p>如发生争议，双方应友好协商解决...</p>
      </div>
    </a-modal>

    <!-- 隐私政策弹窗 -->
    <a-modal v-model:visible="showPrivacyModal" title="隐私政策" :width="600" :footer="false">
      <div class="modal-content">
        <h4>1. 信息收集</h4>
        <p>我们仅收集必要的支付信息...</p>

        <h4>2. 信息使用</h4>
        <p>收集的信息仅用于完成支付流程...</p>

        <h4>3. 信息保护</h4>
        <p>我们采用先进的加密技术保护您的信息...</p>

        <h4>4. 信息共享</h4>
        <p>除法律要求外，我们不会与第三方共享您的信息...</p>
      </div>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
  import { computed, ref } from 'vue';
  import { IconSafe, IconExclamationCircle } from '@arco-design/web-vue/es/icon';
  import { usePaymentStore } from '@/store/modules/payment';
  import { usePaymentValidation } from '../hooks/usePaymentValidation';
  import type { PaymentMethod } from '@/http/api/payment/types';

  // Props
  interface Props {
    finalAmount: number;
    savings: number;
    submitting?: boolean;
    showCancelButton?: boolean;
  }

  const props = withDefaults(defineProps<Props>(), {
    submitting: false,
    showCancelButton: true,
  });

  // Emits
  const emit = defineEmits(['submit', 'cancel']);

  // Store和Hooks
  const paymentStore = usePaymentStore();
  const { validationErrors, validatePayment } = usePaymentValidation();

  // 本地状态
  const agreedToTerms = ref(false);
  const showTermsModal = ref(false);
  const showPrivacyModal = ref(false);

  // 计算属性
  const hasSelectedMethods = computed(() => paymentStore.selectedMethods.length > 0);

  const selectedMethodsSummary = computed(() => {
    return paymentStore.selectedMethods
      .map(method => ({
        type: method.type,
        name: getMethodDisplayName(method),
        amount: method.amount || 0,
      }))
      .filter(item => item.amount > 0);
  });

  const canSubmit = computed(() => {
    return (
      agreedToTerms.value &&
      !props.submitting &&
      paymentStore.isPaymentValid &&
      validationErrors.value.length === 0
    );
  });

  // 方法
  function getMethodDisplayName(method: PaymentMethod): string {
    const nameMap: Record<string, string> = {
      wechat: '微信支付',
      alipay: '支付宝',
      balance: '余额支付',
      points: '积分支付',
      coupon: '优惠券',
    };
    return nameMap[method.type] || method.name;
  }

  function getPayButtonText(): string {
    if (props.submitting) {
      return '支付处理中...';
    }

    if (props.finalAmount <= 0) {
      return '确认订单';
    }

    const hasOnlinePayment = paymentStore.selectedMethods.some(
      method => method.type === 'wechat' || method.type === 'alipay'
    );

    if (hasOnlinePayment) {
      return `立即支付 ¥${formatAmount(props.finalAmount)}`;
    } else {
      return `确认支付 ¥${formatAmount(props.finalAmount)}`;
    }
  }

  async function handlePayment() {
    if (!canSubmit.value) return;

    // 执行支付验证
    const isValid = await validatePayment();
    if (!isValid) {
      return;
    }

    emit('submit');
  }

  function handleCancel() {
    emit('cancel');
  }

  function formatAmount(amount: number): string {
    return amount.toFixed(2);
  }
</script>

<style scoped lang="scss">
  .payment-confirmation-container {
    width: 100%;
  }

  .confirmation-card {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border-radius: 8px;

    :deep(.arco-card-body) {
      padding: 24px;
    }
  }

  .confirmation-content {
    display: flex;
    flex-direction: column;
    gap: 20px;
  }

  .payment-amount-section {
    text-align: center;
    padding: 20px;
    background: linear-gradient(135deg, #e8f3ff 0%, #f0f8ff 100%);
    border-radius: 12px;
    border: 1px solid #d1e7ff;

    .amount-display {
      .amount-label {
        display: block;
        font-size: 14px;
        color: $text-color-secondary;
        margin-bottom: 8px;
      }

      .amount-value {
        font-size: 32px;
        font-weight: 700;
        color: $primary-color;
        font-family: 'Courier New', monospace;
      }
    }

    .savings-display {
      margin-top: 8px;

      .savings-text {
        font-size: 13px;
        color: $success-color;
        font-weight: 500;
      }
    }
  }

  .payment-summary {
    background-color: #f7f8fa;
    border-radius: 8px;
    padding: 16px;

    .summary-title {
      font-size: 14px;
      font-weight: 600;
      color: $text-color;
      margin-bottom: 12px;
    }

    .summary-methods {
      display: flex;
      flex-direction: column;
      gap: 8px;
    }

    .method-summary {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .method-name {
        font-size: 13px;
        color: $text-color-secondary;
      }

      .method-amount {
        font-size: 13px;
        color: $text-color;
        font-weight: 500;
      }
    }
  }

  .payment-agreement {
    display: flex;
    justify-content: center;

    .agreement-checkbox {
      font-size: 13px;
      color: $text-color-secondary;

      :deep(.arco-link) {
        font-size: 13px;
      }
    }
  }

  .payment-actions {
    display: flex;
    flex-direction: column;
    gap: 12px;

    .pay-button {
      height: 48px;
      font-size: 16px;
      font-weight: 600;
      border-radius: 8px;
      background: linear-gradient(135deg, $primary-color 0%, $primary-light 100%);
      border-color: $primary-color;

      &:hover:not(:disabled) {
        background: linear-gradient(135deg, $primary-light 0%, $primary-color 100%);
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(22, 93, 255, 0.3);
      }

      &:disabled {
        opacity: 0.6;
        cursor: not-allowed;
      }
    }

    .cancel-button {
      height: 40px;
      font-size: 14px;
      border-radius: 6px;
      color: $text-color-secondary;
      border-color: #e5e6eb;

      &:hover {
        color: $text-color;
        border-color: $text-color-tertiary;
      }
    }
  }

  .security-tips {
    background-color: #f0f9ff;
    border: 1px solid #e0f2fe;
    border-radius: 6px;
    padding: 12px;

    .tips-content {
      display: flex;
      gap: 8px;

      .security-icon {
        flex-shrink: 0;
        font-size: 16px;
        color: $success-color;
        margin-top: 2px;
      }

      .tips-text {
        flex: 1;

        p {
          margin: 0 0 4px 0;
          font-size: 12px;
          color: $text-color-secondary;

          &:last-child {
            margin-bottom: 0;
          }
        }
      }
    }
  }

  .validation-errors {
    .error-content {
      p {
        margin: 0 0 4px 0;
        font-size: 13px;

        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }

  .modal-content {
    max-height: 400px;
    overflow-y: auto;

    h4 {
      font-size: 14px;
      font-weight: 600;
      color: $text-color;
      margin: 16px 0 8px 0;

      &:first-child {
        margin-top: 0;
      }
    }

    p {
      font-size: 13px;
      color: $text-color-secondary;
      line-height: 1.5;
      margin-bottom: 12px;
    }
  }

  // 响应式设计
  @media (max-width: 768px) {
    .confirmation-card {
      :deep(.arco-card-body) {
        padding: 20px;
      }
    }

    .confirmation-content {
      gap: 16px;
    }

    .payment-amount-section {
      padding: 16px;

      .amount-display {
        .amount-value {
          font-size: 28px;
        }
      }
    }

    .payment-summary {
      padding: 12px;
    }

    .payment-actions {
      .pay-button {
        height: 44px;
        font-size: 15px;
      }

      .cancel-button {
        height: 36px;
        font-size: 13px;
      }
    }

    .security-tips {
      padding: 10px;
    }
  }
</style>
