/** @format */

// 支付模块接口定义

import type {
  OrderInfo,
  PaymentMethodsResponse,
  CreatePaymentRequest,
  PaymentResult,
  PaymentStatusResponse,
  UserAssets,
  CouponInfo,
  ApiResponse,
  PageResponse
} from './types';

// 支付相关命名空间
export namespace Payment {
  
  // 获取订单信息请求
  export interface GetOrderInfoRequest {
    orderId: string;
  }

  // 获取订单信息响应
  export interface GetOrderInfoResponse extends ApiResponse<OrderInfo> {}

  // 获取支付方式请求
  export interface GetPaymentMethodsRequest {
    orderId: string;
    amount: number;
  }

  // 获取支付方式响应
  export interface GetPaymentMethodsResponse extends ApiResponse<PaymentMethodsResponse> {}

  // 获取用户资产请求
  export interface GetUserAssetsRequest {
    userId?: string; // 可选，默认当前用户
  }

  // 获取用户资产响应
  export interface GetUserAssetsResponse extends ApiResponse<UserAssets> {}

  // 获取可用优惠券请求
  export interface GetAvailableCouponsRequest {
    orderId: string;
    amount: number;
    productType?: string;
  }

  // 获取可用优惠券响应
  export interface GetAvailableCouponsResponse extends ApiResponse<CouponInfo[]> {}

  // 计算支付金额请求
  export interface CalculatePaymentRequest {
    orderId: string;
    originalAmount: number;
    couponIds?: string[];     // 使用的优惠券ID列表
    pointsUsed?: number;      // 使用的积分
    balanceUsed?: number;     // 使用的余额
  }

  // 计算支付金额响应
  export interface CalculatePaymentResponse extends ApiResponse<{
    originalAmount: number;
    couponDiscount: number;
    pointsDiscount: number;
    balanceUsed: number;
    finalAmount: number;
    savings: number;
  }> {}

  // 创建支付订单请求
  export interface CreatePaymentOrderRequest extends CreatePaymentRequest {}

  // 创建支付订单响应
  export interface CreatePaymentOrderResponse extends ApiResponse<PaymentResult> {}

  // 查询支付状态请求
  export interface QueryPaymentStatusRequest {
    paymentId: string;
    orderId?: string;
  }

  // 查询支付状态响应
  export interface QueryPaymentStatusResponse extends ApiResponse<PaymentStatusResponse> {}

  // 取消支付请求
  export interface CancelPaymentRequest {
    paymentId: string;
    orderId: string;
    reason?: string;
  }

  // 取消支付响应
  export interface CancelPaymentResponse extends ApiResponse<{
    cancelled: boolean;
    refundAmount: number;
    message: string;
  }> {}

  // 支付回调通知
  export interface PaymentNotification {
    paymentId: string;
    orderId: string;
    status: 'success' | 'failed';
    amount: number;
    transactionId: string;
    paymentTime: string;
    signature: string;        // 签名验证
  }

  // 退款请求
  export interface RefundRequest {
    paymentId: string;
    orderId: string;
    refundAmount: number;
    reason: string;
  }

  // 退款响应
  export interface RefundResponse extends ApiResponse<{
    refundId: string;
    refundAmount: number;
    refundStatus: 'processing' | 'success' | 'failed';
    estimatedTime: string;    // 预计到账时间
  }> {}

  // 支付记录查询请求
  export interface GetPaymentRecordsRequest {
    pageNum?: number;
    pageSize?: number;
    startTime?: string;
    endTime?: string;
    status?: string;
    orderType?: string;
  }

  // 支付记录查询响应
  export interface GetPaymentRecordsResponse extends ApiResponse<PageResponse<{
    paymentId: string;
    orderId: string;
    orderType: string;
    productName: string;
    amount: number;
    status: string;
    paymentTime: string;
    paymentMethods: string[];
  }>> {}

  // 积分兑换记录请求
  export interface GetPointsRecordsRequest {
    pageNum?: number;
    pageSize?: number;
    type?: 'earn' | 'spend';
  }

  // 积分兑换记录响应
  export interface GetPointsRecordsResponse extends ApiResponse<PageResponse<{
    recordId: string;
    type: 'earn' | 'spend';
    points: number;
    description: string;
    createTime: string;
    orderId?: string;
  }>> {}

  // 余额变动记录请求
  export interface GetBalanceRecordsRequest {
    pageNum?: number;
    pageSize?: number;
    type?: 'recharge' | 'consume' | 'refund';
  }

  // 余额变动记录响应
  export interface GetBalanceRecordsResponse extends ApiResponse<PageResponse<{
    recordId: string;
    type: 'recharge' | 'consume' | 'refund';
    amount: number;
    balance: number;          // 变动后余额
    description: string;
    createTime: string;
    orderId?: string;
  }>> {}

  // 优惠券使用记录请求
  export interface GetCouponRecordsRequest {
    pageNum?: number;
    pageSize?: number;
    status?: 'unused' | 'used' | 'expired';
  }

  // 优惠券使用记录响应
  export interface GetCouponRecordsResponse extends ApiResponse<PageResponse<{
    couponId: string;
    name: string;
    type: 'discount' | 'amount';
    value: number;
    status: 'unused' | 'used' | 'expired';
    receiveTime: string;
    useTime?: string;
    expireTime: string;
    orderId?: string;
  }>> {}
}
