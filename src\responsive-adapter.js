/**
 * 响应式适配入口文件
 * 用于导入所有响应式相关的样式和工具函数
 */

// 导入响应式样式
import './styles/responsive.css';
import './styles/mobile-adapter.css';

// 导入响应式工具函数
import { useScreenSize, useScreenSizeListener, responsiveValue } from './utils/responsive';

// 导出响应式工具函数，以便在组件中使用
export { useScreenSize, useScreenSizeListener, responsiveValue };

/**
 * 初始化响应式适配
 * 在应用启动时调用此函数，设置全局响应式行为
 */
export const initResponsiveAdapter = () => {
  // 添加视口元标签，确保移动设备正确缩放
  if (!document.querySelector('meta[name="viewport"]')) {
    const meta = document.createElement('meta');
    meta.name = 'viewport';
    meta.content = 'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no';
    document.head.appendChild(meta);
  }

  // 监听窗口大小变化，可以在这里添加全局响应式逻辑
  const removeListener = useScreenSizeListener(screen => {
    // 可以在这里根据屏幕尺寸变化执行全局操作
    // 例如，在特定屏幕尺寸下自动关闭菜单等

    // 设置根元素数据属性，用于CSS选择器
    document.documentElement.dataset.screenSize = screen.isMobile
      ? 'mobile'
      : screen.isTablet
        ? 'tablet'
        : 'desktop';
  });

  // 初始化时立即执行一次屏幕尺寸检测
  const screen = useScreenSize();
  document.documentElement.dataset.screenSize = screen.isMobile
    ? 'mobile'
    : screen.isTablet
      ? 'tablet'
      : 'desktop';

  // 返回清理函数
  return () => {
    removeListener();
  };
};
