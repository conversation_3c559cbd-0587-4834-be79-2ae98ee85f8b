// 获取本地图片链接
export const getAssetsFile = (url: string) => {
  return new URL(`../assets/images/${url}`, import.meta.url).href;
};

// 价格补0
export const IntegerToFloat = (data: any) => {
  if (data == null || data == 'NaN') {
    return '';
  }
  const arr = data.toString().split('.');
  if (arr.length === 1) {
    return `${data}.00`;
  } else if (arr.length === 2 && arr[1].length === 1) {
    return `${data}0`;
  } else {
    return data;
  }
};

// 电话号码脱敏
export const phoneCode = (tel: string) => {
  if (!tel) return tel;
  // replace() 方法用于在字符串中用一些字符替换另一些字符，或替换一个与正则表达式匹配的子串。
  const phone = tel.replace(tel.substring(3, 7), '****');
  return phone;
};

// 姓名脱敏
export const namePrivate = (name: string) => {
  if (null != name && name != undefined) {
    if (name.length == 2) {
      return name.substring(0, 1) + '*'; // 截取name的第一个字符，第二个字符变成*
    } else if (name.length == 3) {
      return name.substring(0, 1) + '*' + name.substring(2, 3); // 截取name的第一个和第三个字符，第二个字符变成*
    } else if (name.length > 3) {
      return name.substring(0, 1) + '*' + '*' + name.substring(3, name.length); // 截取第一个和大于第4个字符
    }
  } else {
    return '';
  }
};

// 复制文本到剪切板
export const copyToClip = (content: string) => {
  const aux = document.createElement('input');
  aux.setAttribute('value', content);
  document.body.appendChild(aux);
  aux.select();
  document.execCommand('copy');
  document.body.removeChild(aux);
};
