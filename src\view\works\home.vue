<template>
  <div class="page-container">
    <!-- Hero Banner Section -->
    <div class="hero-section">
      <div class="content-wrapper">
        <h1 class="hero-title">作品著作权登记服务</h1>
        <p class="hero-description">
          作品著作权是指作者对其创作的文学、艺术和科学作品享有的各项专有权利。本平台提供作品著作权登记和存证服务，保护您的创作成果权益，为文学、艺术、音乐、美术、摄影等各类作品提供专业的版权保护。
        </p>
        <div class="hero-buttons">
          <a-button type="primary" size="large" class="hero-button" @click="goToRegister">
            <span>立即登记</span>
            <icon-arrow-right />
          </a-button>
          <a-button type="outline" size="large" class="hero-button" @click="goToEvidence">
            <span>立即存证</span>
            <icon-arrow-right />
          </a-button>
        </div>
      </div>
    </div>

    <!-- Public Announcement Section -->
    <div class="section public-announcement">
      <div class="section-title">
        <h2>作品著作权登记公示</h2>
      </div>
      <div class="announcement-list">
        <a-card v-for="item in announcementList" :key="item.id" class="announcement-card">
          <template #title>
            <div class="card-title">
              {{ item.title }}
            </div>
          </template>
          <template #extra>
            <a-button type="text" @click="viewDetails(item.id)"> 详情 </a-button>
          </template>
          <div class="card-content">
            <div class="card-item">
              <span class="label">申请编号：</span>
              <span class="value">{{ item.applicationNumber }}</span>
            </div>
            <div class="card-item">
              <span class="label">公示截止日期：</span>
              <span class="value">{{ item.endDate }}</span>
            </div>
            <div class="card-item">
              <span class="label">申请主体：</span>
              <span class="value">{{ item.applicant }}</span>
            </div>
            <div class="card-item">
              <span class="label">作品类型：</span>
              <span class="value">{{ item.worksType }}</span>
            </div>
          </div>
        </a-card>
      </div>
      <div class="announcement-pagination">
        <a-pagination
          v-model:current="currentPage"
          :total="totalAnnouncements"
          :page-size="pageSize"
          show-total
          @change="handlePageChange"
        />
      </div>
    </div>

    <!-- Service Features Section -->
    <div class="section service-features">
      <div class="section-title">
        <h2>服务特色</h2>
      </div>
      <div class="features-container">
        <a-row :gutter="24">
          <a-col :span="8">
            <div class="feature-card">
              <div class="feature-icon">
                <icon-safe />
              </div>
              <h3>权威认证</h3>
              <p>国家版权局认可的登记机构，颁发具有法律效力的作品著作权登记证书</p>
            </div>
          </a-col>
          <a-col :span="8">
            <div class="feature-card">
              <div class="feature-icon">
                <icon-clock-circle />
              </div>
              <h3>快速办理</h3>
              <p>在线申请，材料齐全后5-15个工作日完成登记，比传统方式节省50%时间</p>
            </div>
          </a-col>
          <a-col :span="8">
            <div class="feature-card">
              <div class="feature-icon">
                <icon-check-circle />
              </div>
              <h3>区块链存证</h3>
              <p>结合区块链技术进行作品存证，确保作品创作时间和内容的不可篡改性</p>
            </div>
          </a-col>
        </a-row>
      </div>
    </div>

    <!-- Application Scenarios Section -->
    <div class="section application-scenarios">
      <div class="section-title">
        <h2>应用场景</h2>
      </div>
      <div class="scenarios-container">
        <a-row :gutter="24">
          <a-col :span="12">
            <div class="scenario-card">
              <div class="scenario-icon">
                <icon-file />
              </div>
              <div class="scenario-content">
                <h3>作品存证</h3>
                <p>
                  用于对文学、艺术、音乐、美术、摄影等各类创作作品进行存证，保障作品内容的真实性和完整性，为作品著作权保护提供可靠的技术支持和法律依据。
                </p>
                <a-button type="primary" @click="goToEvidence"> 立即使用 </a-button>
              </div>
            </div>
          </a-col>
          <a-col :span="12">
            <div class="scenario-card">
              <div class="scenario-icon">
                <icon-copyright />
              </div>
              <div class="scenario-content">
                <h3>作品著作权登记</h3>
                <p>
                  作品著作权登记证书是作品著作权的法律凭证，可用于作品发表、授权许可、维权诉讼、商业合作等场景，有效保障创作者的合法权益。
                </p>
                <a-button type="primary" @click="goToRegister"> 立即使用 </a-button>
              </div>
            </div>
          </a-col>
        </a-row>
      </div>
    </div>

    <!-- Works Types Section -->
    <div class="section works-types">
      <div class="section-title">
        <h2>支持作品类型</h2>
      </div>
      <div class="types-container">
        <a-row :gutter="16">
          <a-col :span="6">
            <div class="type-card">
              <icon-edit />
              <span>文字作品</span>
            </div>
          </a-col>
          <a-col :span="6">
            <div class="type-card">
              <icon-notification />
              <span>音乐作品</span>
            </div>
          </a-col>
          <a-col :span="6">
            <div class="type-card">
              <icon-image />
              <span>美术作品</span>
            </div>
          </a-col>
          <a-col :span="6">
            <div class="type-card">
              <icon-camera />
              <span>摄影作品</span>
            </div>
          </a-col>
        </a-row>
        <a-row :gutter="16" class="mt-4">
          <a-col :span="6">
            <div class="type-card">
              <icon-play-circle />
              <span>影视作品</span>
            </div>
          </a-col>
          <a-col :span="6">
            <div class="type-card">
              <icon-desktop />
              <span>图形作品</span>
            </div>
          </a-col>
          <a-col :span="6">
            <div class="type-card">
              <icon-home />
              <span>建筑作品</span>
            </div>
          </a-col>
          <a-col :span="6">
            <div class="type-card">
              <icon-more />
              <span>其他作品</span>
            </div>
          </a-col>
        </a-row>
      </div>
    </div>

    <!-- FAQ Section -->
    <div class="section faq">
      <div class="section-title">
        <h2>常见问题</h2>
      </div>
      <div class="faq-content">
        <a-collapse accordion>
          <a-collapse-item key="1" header="什么是作品著作权？">
            <p>
              作品著作权是指作者对其创作的文学、艺术和科学作品享有的各项专有权利，包括发表权、署名权、修改权、保护作品完整权、复制权、发行权、出租权、展览权、表演权、放映权、广播权、信息网络传播权、摄制权、改编权、翻译权、汇编权等。
            </p>
          </a-collapse-item>
          <a-collapse-item key="2" header="作品著作权登记有什么好处？">
            <p>
              作品著作权登记可以作为作品著作权归属的初步证据，有利于维护著作权人的合法权益，预防和解决著作权纠纷。同时，登记证书也是作品发表、授权许可、维权诉讼等活动的重要凭证。
            </p>
          </a-collapse-item>
          <a-collapse-item key="3" header="作品著作权登记需要哪些材料？">
            <p>
              一般需要提供申请表、作品样本、作品说明书、身份证明文件（个人或企业）、著作权归属证明等材料。具体要求根据作品类型有所不同，可参考登记申请页面的指引。
            </p>
          </a-collapse-item>
          <a-collapse-item key="4" header="作品著作权登记多久能完成？">
            <p>
              在材料齐全、符合要求的情况下，从申请到获得登记证书一般需要5-15个工作日。通过本平台申请可以实时查询进度，提高登记效率。
            </p>
          </a-collapse-item>
          <a-collapse-item key="5" header="哪些作品可以申请著作权登记？">
            <p>
              文字作品、音乐作品、美术作品、摄影作品、影视作品、图形作品、建筑作品等各类具有独创性的智力成果都可以申请著作权登记。作品应当具备原创性、可复制性等基本条件。
            </p>
          </a-collapse-item>
        </a-collapse>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref, onMounted } from 'vue';
  import { useRouter } from 'vue-router';
  import {
    IconArrowRight,
    IconSafe,
    IconClockCircle,
    IconCheckCircle,
    IconFile,
    IconCopyright,
    IconEdit,
    IconNotification,
    IconImage,
    IconCamera,
    IconPlayCircle,
    IconDesktop,
    IconHome,
    IconMore,
  } from '@arco-design/web-vue/es/icon';
  import { getWorksCopyrightList } from '@/http/api/works';

  const router = useRouter();

  // 公示公告数据
  const announcementList = ref([
    {
      id: 1,
      title: '《春江花月夜》音乐作品著作权登记公示',
      applicationNumber: 'ZP2024001001',
      endDate: '2024-01-15',
      applicant: '张某某',
      worksType: '音乐作品',
    },
    {
      id: 2,
      title: '《现代都市》摄影作品著作权登记公示',
      applicationNumber: 'ZP2024001002',
      endDate: '2024-01-16',
      applicant: '李某某',
      worksType: '摄影作品',
    },
    {
      id: 3,
      title: '《科技创新论文集》文字作品著作权登记公示',
      applicationNumber: 'ZP2024001003',
      endDate: '2024-01-17',
      applicant: '某某科技有限公司',
      worksType: '文字作品',
    },
  ]);

  // 分页数据
  const currentPage = ref(1);
  const pageSize = ref(10);
  const totalAnnouncements = ref(30);

  // 页面跳转方法
  const goToRegister = () => {
    router.push('/works/register');
  };

  const goToEvidence = () => {
    router.push('/works/evidence');
  };

  const viewDetails = (id: number) => {
    router.push(`/works/details/${id}`);
  };

  const handlePageChange = (page: number) => {
    currentPage.value = page;
    // 这里可以调用API获取对应页面的数据
  };

  // 组件挂载时获取数据
  onMounted(() => {
    // 可以在这里调用API获取实际的公示数据
  });
</script>

<style lang="scss" scoped>
  .page-container {
    width: 100%;
    min-height: 100vh;
    background-color: #f7f8fa;
  }

  .hero-section {
    background: linear-gradient(135deg, #165dff 0%, #4080ff 100%);
    color: white;
    padding: 80px 0;
    text-align: center;

    .content-wrapper {
      max-width: 1200px;
      margin: 0 auto;
      padding: 0 24px;
    }

    .hero-title {
      font-size: 48px;
      font-weight: 700;
      margin-bottom: 24px;
      line-height: 1.2;
    }

    .hero-description {
      font-size: 18px;
      line-height: 1.6;
      margin-bottom: 40px;
      max-width: 800px;
      margin-left: auto;
      margin-right: auto;
      opacity: 0.9;
    }

    .hero-buttons {
      display: flex;
      gap: 16px;
      justify-content: center;
      flex-wrap: wrap;

      .hero-button {
        height: 48px;
        padding: 0 32px;
        font-size: 16px;
        border-radius: 24px;
        display: flex;
        align-items: center;
        gap: 8px;
      }
    }
  }

  .section {
    padding: 80px 0;

    &:nth-child(even) {
      background-color: white;
    }

    .section-title {
      text-align: center;
      margin-bottom: 48px;

      h2 {
        font-size: 36px;
        font-weight: 600;
        color: #1d2129;
        margin: 0;
      }
    }

    .content-wrapper {
      max-width: 1200px;
      margin: 0 auto;
      padding: 0 24px;
    }
  }

  .public-announcement {
    .announcement-list {
      max-width: 1200px;
      margin: 0 auto;
      padding: 0 24px;

      .announcement-card {
        margin-bottom: 16px;
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

        .card-title {
          font-weight: 600;
          color: #1d2129;
        }

        .card-content {
          .card-item {
            display: flex;
            margin-bottom: 8px;

            .label {
              color: #86909c;
              width: 120px;
              flex-shrink: 0;
            }

            .value {
              color: #1d2129;
              font-weight: 500;
            }
          }
        }
      }
    }

    .announcement-pagination {
      display: flex;
      justify-content: center;
      margin-top: 32px;
    }
  }

  .service-features {
    .features-container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 0 24px;

      .feature-card {
        text-align: center;
        padding: 32px 24px;
        background: white;
        border-radius: 12px;
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
        height: 100%;

        .feature-icon {
          font-size: 48px;
          color: #165dff;
          margin-bottom: 24px;
        }

        h3 {
          font-size: 20px;
          font-weight: 600;
          color: #1d2129;
          margin-bottom: 16px;
        }

        p {
          color: #4e5969;
          line-height: 1.6;
          margin: 0;
        }
      }
    }
  }

  .application-scenarios {
    .scenarios-container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 0 24px;

      .scenario-card {
        display: flex;
        align-items: flex-start;
        gap: 24px;
        padding: 32px;
        background: white;
        border-radius: 12px;
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
        height: 100%;

        .scenario-icon {
          font-size: 48px;
          color: #165dff;
          flex-shrink: 0;
        }

        .scenario-content {
          flex: 1;

          h3 {
            font-size: 20px;
            font-weight: 600;
            color: #1d2129;
            margin-bottom: 16px;
          }

          p {
            color: #4e5969;
            line-height: 1.6;
            margin-bottom: 24px;
          }
        }
      }
    }
  }

  .works-types {
    .types-container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 0 24px;

      .type-card {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 12px;
        padding: 24px;
        background: white;
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;
        cursor: pointer;

        &:hover {
          transform: translateY(-4px);
          box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
        }

        .arco-icon {
          font-size: 32px;
          color: #165dff;
        }

        span {
          font-size: 14px;
          font-weight: 500;
          color: #1d2129;
        }
      }
    }
  }

  .faq {
    .faq-content {
      max-width: 800px;
      margin: 0 auto;
      padding: 0 24px;

      :deep(.arco-collapse-item-header) {
        font-size: 16px;
        font-weight: 500;
        color: #1d2129;
      }

      :deep(.arco-collapse-item-content) {
        color: #4e5969;
        line-height: 1.6;
      }
    }
  }

  @media (max-width: 768px) {
    .hero-section {
      padding: 60px 0;

      .hero-title {
        font-size: 32px;
      }

      .hero-description {
        font-size: 16px;
      }

      .hero-buttons {
        flex-direction: column;
        align-items: center;

        .hero-button {
          width: 200px;
        }
      }
    }

    .section {
      padding: 60px 0;

      .section-title h2 {
        font-size: 28px;
      }
    }

    .scenario-card {
      flex-direction: column;
      text-align: center;

      .scenario-icon {
        align-self: center;
      }
    }
  }
</style>
