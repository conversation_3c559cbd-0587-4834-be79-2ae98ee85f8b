# 登录校验功能实现文档

## 概述

本文档描述了在文创链开放平台项目中实现的登录校验功能，确保在调用proHttp接口之前校验用户登录状态，如果用户未登录则自动跳转到登录页面。

## 实现方案

### 1. 登录校验工具类 (`src/utils/auth.ts`)

创建了一个通用的登录校验工具类 `AuthUtils`，提供以下功能：

- **`isLoggedIn()`**: 检查用户是否已登录
- **`getToken()`**: 获取用户token
- **`getUserInfo()`**: 获取用户信息
- **`checkLoginAndRedirect()`**: 检查登录状态，未登录时跳转到登录页
- **`logout()`**: 用户登出
- **`needsAuth()`**: 判断接口是否需要登录校验
- **`handleAuthError()`**: 处理认证失败的情况

#### 不需要登录的接口列表

以下接口被配置为不需要登录校验：

```typescript
const publicApis = [
  '/admin-api/system/auth/login',      // 登录接口
  '/app-api/member/auth/login',        // 会员登录接口
  '/app-api/member/auth/sms-login',    // 短信登录接口
  '/app-api/member/auth/register',     // 注册接口
  '/app-api/member/auth/send-sms-code', // 发送短信验证码
  '/app-api/member/auth/reset-password', // 重置密码
  '/app-api/system/dict-data/type',    // 字典数据（部分可能需要公开访问）
  '/infra/file/presigned-url',         // 文件预签名URL（如果需要公开访问）
];
```

### 2. ProHttp请求拦截器 (`src/http/proHttp.ts`)

在proHttp的请求拦截器中添加了登录校验逻辑：

```typescript
// 添加PRO API专用的请求拦截器，用于登录校验
proInstance.interceptors.request.use(
  (config: InternalAxiosRequestConfig) => {
    // 获取请求URL
    const requestUrl = config.url || '';
    
    // 检查是否需要登录校验
    if (AuthUtils.needsAuth(requestUrl)) {
      // 检查用户是否已登录
      if (!AuthUtils.isLoggedIn()) {
        // 用户未登录，阻止请求并跳转到登录页
        AuthUtils.checkLoginAndRedirect(true, undefined);
        
        // 返回一个被拒绝的Promise来阻止请求继续
        return Promise.reject({
          code: 'AUTH_REQUIRED',
          message: '用户未登录，请先登录',
          config
        });
      }
    }
    
    // 用户已登录或不需要登录校验，继续请求
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);
```

### 3. 路由守卫 (`src/router/index.ts`)

完善了路由守卫，添加对需要登录页面的保护：

```typescript
router.beforeEach((to, from, next) => {
  NProgress.start();
  
  // 检查路由是否需要认证
  const requiresAuth = to.matched.some(record => record.meta.requiresAuth);
  
  // 如果路由需要认证但用户未登录
  if (requiresAuth && !AuthUtils.isLoggedIn()) {
    Message.warning('请先登录后再访问该页面');
    
    // 跳转到登录页，并保存当前要访问的路径
    next({
      path: '/login',
      query: {
        redirect: to.fullPath !== '/login' ? to.fullPath : '/home'
      }
    });
    return;
  }
  
  // 如果已登录用户访问登录页，重定向到首页或指定页面
  if (to.path === '/login' && AuthUtils.isLoggedIn()) {
    const redirectPath = (to.query.redirect as string) || '/home';
    next(redirectPath);
    return;
  }
  
  next();
});
```

### 4. 需要登录的路由配置

为以下路由添加了 `requiresAuth: true` 配置：

- **用户中心** (`/user` 及其所有子路由)
  - 个人信息、产品列表、套餐购买等
- **数据存证申请** (`/data/eviApply`)
- **软件著作权存证** (`/software/evidence`)
- **软件著作权登记** (`/software/register`)

### 5. 登录页面重定向 (`src/view/login/index.vue`)

修改了登录成功后的重定向逻辑：

```typescript
// 获取重定向路径，如果没有则跳转到首页
const redirectPath = (router.currentRoute.value.query.redirect as string) || '/home';
router.replace(redirectPath);
```

## 测试功能

创建了测试页面 `/data/auth-test` 用于验证登录校验功能：

- 显示当前登录状态
- 测试proHttp接口调用
- 验证未登录时的跳转行为
- 验证已登录时的正常访问

## 使用方法

### 1. 在组件中使用

```typescript
import { AuthUtils, checkLogin } from '@/utils/auth';

// 检查登录状态
if (AuthUtils.isLoggedIn()) {
  // 用户已登录，执行相关操作
}

// 检查登录并重定向
if (!checkLogin()) {
  // 用户未登录，已自动跳转到登录页
  return;
}
```

### 2. 在API调用中

proHttp会自动进行登录校验，无需额外处理：

```typescript
import proHttp from '@/http/proHttp';

// 这个调用会自动检查登录状态
const response = await proHttp.get('/app-api/some-protected-endpoint');
```

### 3. 添加新的需要登录的路由

```typescript
{
  path: '/new-protected-page',
  component: () => import('@/view/NewProtectedPage.vue'),
  meta: {
    requiresAuth: true, // 添加这个配置
    title: '受保护的页面',
  },
}
```

## 工作流程

1. **用户访问需要登录的页面**
   - 路由守卫检查 `requiresAuth` 配置
   - 如果未登录，跳转到登录页并保存重定向路径

2. **用户调用proHttp接口**
   - 请求拦截器检查接口是否需要登录
   - 如果需要登录但用户未登录，阻止请求并跳转登录页

3. **用户登录成功**
   - 保存token和用户信息到store
   - 重定向到之前要访问的页面

4. **用户已登录访问登录页**
   - 自动重定向到首页或指定页面

## 注意事项

1. **公开接口配置**: 需要在 `AuthUtils.needsAuth()` 方法中正确配置不需要登录的接口
2. **路由配置**: 新增需要登录的页面时，记得添加 `requiresAuth: true`
3. **错误处理**: 系统会自动处理token过期等认证错误
4. **用户体验**: 登录后会自动跳转到用户原本要访问的页面

## 测试建议

1. 访问测试页面：`http://localhost:5174/#/data/auth-test`
2. 测试未登录状态下的接口调用
3. 测试登录后的正常访问
4. 测试路由守卫的重定向功能
5. 测试登录成功后的重定向功能
