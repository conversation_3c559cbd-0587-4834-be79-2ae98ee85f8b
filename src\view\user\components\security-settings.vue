<template>
  <a-list :bordered="false">
    <a-list-item>
      <a-list-item-meta>
        <template #avatar>
          <a-typography-paragraph>登录密码</a-typography-paragraph>
        </template>
        <template #description>
          <div class="content">
            <a-typography-paragraph>
              已设置。密码4-16位字符，且必须包含至少2种类型（数字、大写字母、小写字母、特殊字符，不包括空格）。
            </a-typography-paragraph>
          </div>
          <div class="operation">
            <a-link @click="visible = true"> 修改 </a-link>
          </div>
        </template>
      </a-list-item-meta>
    </a-list-item>
    <a-list-item>
      <a-list-item-meta>
        <template #avatar>
          <a-typography-paragraph>密保问题</a-typography-paragraph>
        </template>
        <template #description>
          <div class="content">
            <a-typography-paragraph class="tip">
              您暂未设置密保问题，密保问题可以有效的保护账号的安全。
            </a-typography-paragraph>
          </div>
          <div class="operation">
            <a-tooltip content="功能暂未开放">
              <a-link>设置</a-link>
            </a-tooltip>
          </div>
        </template>
      </a-list-item-meta>
    </a-list-item>
    <!-- <a-list-item>
      <a-list-item-meta>
        <template #avatar>
          <a-typography-paragraph>安全手机</a-typography-paragraph>
        </template>
        <template #description>
          <div class="content">
            <a-typography-paragraph>已绑定：{{ formData.phone }}</a-typography-paragraph>
          </div>
          <div class="operation">
            <a-link>修改</a-link>
          </div>
        </template>
      </a-list-item-meta>
    </a-list-item> -->
    <a-list-item>
      <a-list-item-meta>
        <template #avatar>
          <a-typography-paragraph>安全邮箱</a-typography-paragraph>
        </template>
        <template #description>
          <div class="content">
            <a-typography-paragraph>已绑定：{{ formData.email }}</a-typography-paragraph>
            <!-- <a-typography-paragraph class="tip">您暂未设置邮箱，绑定邮箱可以用来找回密码、接收通知等。</a-typography-paragraph> -->
          </div>
          <div class="operation">
            <a-tooltip content="功能暂未开放">
              <a-link>设置</a-link>
            </a-tooltip>
          </div>
        </template>
      </a-list-item-meta>
    </a-list-item>
  </a-list>
  <!-- 修改密码弹窗 -->
  <a-modal
    v-model:visible="visible"
    title="修改登陆密码"
    :ok-loading="btnLoading"
    :mask-closable="false"
    :on-before-ok="handleBeforeOk"
    @cancel="handleCancel"
  >
    <a-form ref="formRef" size="large" :model="form">
      <a-form-item
        field="mobile"
        label="手机号"
        :rules="[{ required: true, message: '请输入手机号' }]"
      >
        <a-input
          v-model="form.mobile"
          :style="{ width: '340px' }"
          placeholder="请输入手机号"
          allow-clear
        />
      </a-form-item>
      <a-form-item
        field="code"
        label="验证码"
        :rules="[{ required: true, message: '请输入验证码' }]"
      >
        <a-input-group>
          <a-input
            v-model="form.code"
            :style="{ width: '240px' }"
            placeholder="请输入验证码"
            allow-clear
          />
          <a-button :style="{ width: '100px' }" :disabled="countdown > 0" @click="sendSmsCode">
            {{ countdown > 0 ? `${countdown}s` : '发送验证码' }}
          </a-button>
        </a-input-group>
      </a-form-item>
      <a-form-item field="password" label="新密码" :rules="passwordRules">
        <a-input-password
          v-model="form.password"
          :style="{ width: '340px' }"
          placeholder="请输入新密码"
          allow-clear
        />
      </a-form-item>
      <a-form-item field="password2" label="重复新密码" :rules="confirmPasswordRules">
        <a-input-password
          v-model="form.password2"
          :style="{ width: '340px' }"
          placeholder="请再次输入新密码"
          allow-clear
        />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script lang="ts" setup>
  import { ref } from 'vue';
  import { useUserStore } from '@/store/index';
  import { updateMemberPassword, memberSendResetPasswordSmsCode } from '@/http/api/login/index';
  import { Message } from '@arco-design/web-vue';
  import { useRouter } from 'vue-router';
  const userStore = useUserStore();
  const router = useRouter();
  type Props = {
    formData: {
      phone: string;
      email: string;
    };
  };
  defineProps<Props>();

  // 密码验证规则
  const passwordRules = [
    { required: true, message: '必填' },
    {
      validator: (value: string, callback: (error?: string) => void) => {
        if (!value) {
          callback('必填');
          return;
        }
        // 检查长度
        if (value.length < 4 || value.length > 16) {
          callback('密码长度必须在4-16位之间');
          return;
        }
        // 检查是否包含空格
        if (/\s/.test(value)) {
          callback('密码不能包含空格');
          return;
        }
        // 检查至少包含2种类型
        let typeCount = 0;
        if (/[0-9]/.test(value)) typeCount++; // 数字
        if (/[a-z]/.test(value)) typeCount++; // 小写字母
        if (/[A-Z]/.test(value)) typeCount++; // 大写字母
        if (/[^a-zA-Z0-9\s]/.test(value)) typeCount++; // 特殊字符（除空格外）

        if (typeCount < 2) {
          callback('密码必须包含至少2种类型（数字、大写字母、小写字母、特殊字符）');
          return;
        }
        callback();
      },
    },
  ];

  const visible = ref(false);
  const btnLoading = ref(false);
  const countdown = ref(0); // 验证码倒计时
  const form = ref({
    mobile: '',
    code: '',
    password: '',
    password2: '',
  });

  // 确认密码验证规则
  const confirmPasswordRules = [
    { required: true, message: '请再次输入新密码' },
    {
      validator: (value: string, callback: (error?: string) => void) => {
        if (!value) {
          callback('请再次输入新密码');
          return;
        }
        if (value !== form.value.password) {
          callback('两次输入的密码不一致');
          return;
        }
        callback();
      },
    },
  ];

  const handleClick = () => {
    visible.value = true;
  };
  const formRef = ref<any>(null);

  // 发送验证码
  const sendSmsCode = async () => {
    if (!form.value.mobile) {
      Message.error('请输入手机号');
      return;
    }
    // 验证手机号格式
    if (!/^1[3-9]\d{9}$/.test(form.value.mobile)) {
      Message.error('请输入正确的手机号');
      return;
    }

    try {
      await memberSendResetPasswordSmsCode({
        mobile: form.value.mobile,
        scene: '3', // 会员用户-重置密码场景
      });
      Message.success('验证码发送成功');
      startCountdown();
    } catch (error: any) {
      Message.error(error.msg || '验证码发送失败');
    }
  };

  // 开始倒计时
  const startCountdown = () => {
    countdown.value = 60;
    const timer = setInterval(() => {
      countdown.value--;
      if (countdown.value <= 0) {
        clearInterval(timer);
      }
    }, 1000);
  };

  const handleBeforeOk = (done: (closed: boolean) => void) => {
    formRef.value.validate((validateResult: any) => {
      if (validateResult) {
        // 表单验证失败，不关闭弹窗
        done(false);
        return;
      }

      // 表单验证通过，继续执行API调用
      const params = {
        password: form.value.password,
        code: form.value.code,
      };
      btnLoading.value = true;
      updateMemberPassword(params)
        .then(() => {
          btnLoading.value = false;
          formRef.value.resetFields();
          userStore.userOutLogin();
          Message.success('密码修改成功！');
          form.value = {
            mobile: '',
            code: '',
            password: '',
            password2: '',
          };
          setTimeout(() => {
            router.replace('/login');
          }, 1000);
          // API调用成功，关闭弹窗
          done(true);
        })
        .catch((error: any) => {
          btnLoading.value = false;
          Message.error(error.msg || '密码修改失败');
          // 清空敏感信息但保留用户输入的手机号
          form.value.code = '';
          form.value.password = '';
          form.value.password2 = '';
          // API调用失败，不关闭弹窗
          done(false);
        });
    });
  };
  // $refs.validate()
  const handleCancel = () => {
    visible.value = false;
  };
</script>

<style scoped lang="scss">
  :deep(.arco-list-item) {
    border-bottom: none !important;

    .arco-typography {
      margin-bottom: 20px;
    }

    .arco-list-item-meta-avatar {
      margin-bottom: 1px;
    }

    .arco-list-item-meta {
      padding: 0;
    }
  }

  :deep(.arco-list-item-meta-content) {
    flex: 1;
    border-bottom: 1px solid var(--color-neutral-3);

    .arco-list-item-meta-description {
      display: flex;
      flex-flow: row;
      justify-content: space-between;

      .tip {
        color: rgb(var(--gray-6));
      }

      .operation {
        margin-right: 6px;
      }
    }
  }
</style>
