<template>
  <a-layout>
    <a-layout-content>
      <div class="layout-content">
        <a-row>
          <a-col :span="4">
            <a-menu
              :auto-open="true"
              :selected-keys="current"
              :default-collapsed="false"
              @menu-item-click="onMenuClick"
            >
              <a-sub-menu key="0">
                <template #icon>
                  <icon-user />
                </template>
                <template #title> 个人中心 </template>
                <a-menu-item key="/user/userInfo"> 用户信息 </a-menu-item>
                <!-- <a-menu-item key="/user/productsList"> 产品申请 </a-menu-item> -->
                <!-- <a-menu-item key="/user/quantityUsed"> 用量查询 </a-menu-item> -->
              </a-sub-menu>
              <a-sub-menu key="1">
                <template #icon>
                  <icon-storage />
                </template>
                <template #title> 套餐管理 </template>
                <a-menu-item key="/user/packagePurchase"> 套餐购买 </a-menu-item>
              </a-sub-menu>
              <a-sub-menu key="2">
                <template #icon>
                  <icon-file />
                </template>
                <template #title> 存证管理 </template>
                <a-menu-item key="/user/evidenceList"> 存证列表 </a-menu-item>
              </a-sub-menu>
              <a-sub-menu key="3">
                <template #icon>
                  <icon-copyright />
                </template>
                <template #title> 软著管理 </template>
                <a-menu-item key="/user/softwareRegList"> 登记列表 </a-menu-item>
              </a-sub-menu>
            </a-menu>
          </a-col>
          <a-col :span="20">
            <router-view />
          </a-col>
        </a-row>
      </div>
    </a-layout-content>
  </a-layout>
</template>

<script lang="ts" setup>
  import Header from './components/header.vue';
  import { ref, watch, onMounted } from 'vue';
  import { useRouter } from 'vue-router';
  import { IconUser, IconStorage, IconFile, IconCopyright } from '@arco-design/web-vue/es/icon';
  import { getMemberUserInfo } from '@/http/api/login/index';

  const router = useRouter();
  const current = ref(['/user/userInfo']);

  watch(
    () => router.currentRoute.value,
    newValue => {
      current.value = [newValue.path];
    },
    { immediate: true, deep: true }
  );
  const onMenuClick = (val: any) => {
    router.push(val);
  };

  // 获取用户基本信息
  const fetchUserInfo = async () => {
    try {
      const userInfo = await getMemberUserInfo();
      console.log('用户基本信息:', userInfo);
    } catch (error) {
      console.error('获取用户信息失败:', error);
    }
  };

  // 页面加载时调用接口
  onMounted(() => {
    fetchUserInfo();
  });
</script>
