import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse, AxiosError } from 'axios';
import { useUserStore } from '@/store/index';
import NProgress from 'nprogress';
import { Message } from '@arco-design/web-vue';
import router from '@/router';

// 定义扩展的请求配置接口
export interface AxiosInstanceConfig extends AxiosRequestConfig {
  tokenKey?: string; // 自定义token的key
  getToken?: () => string | null; // 自定义获取token的方法
  tenantId?: string; // 租户ID
  customHeaders?: Record<string, string>; // 自定义请求头
}

// 定义响应数据接口
export interface ResType<T> {
  code: number;
  data?: T;
  msg: string;
  err?: string;
  sign?: boolean;
}

/**
 * 创建自定义的axios实例
 * @param config 实例配置
 * @returns AxiosInstance
 */
export function createAxiosInstance(config: AxiosInstanceConfig): AxiosInstance {
  const userStore = useUserStore();

  // 默认值和用户配置合并
  const {
    tokenKey = 'token',
    getToken = () => userStore.getToken,
    customHeaders = {},
    tenantId,
  } = config;

  // 创建axios实例
  const instance = axios.create({
    timeout: 10000,
    headers: {
      'Content-Type': 'application/json',
      ...(tenantId ? { 'tenant-id': tenantId } : {}),
      ...customHeaders,
    },
    ...config,
  });

  // 请求拦截器
  instance.interceptors.request.use(
    (config: any) => {
      NProgress.start();
      let token = getToken();

      // 处理不需要token的请求
      if (config.params && config.params.tokenNai) {
        delete config.params.tokenNai;
        token = null;
      }

      // 设置token
      if (token) {
        if (!config.headers) {
          config.headers = {};
        }
        config.headers[tokenKey] = token;
      }

      return config;
    },
    (error: Error) => {
      NProgress.done();
      return Promise.reject(error);
    }
  );

  // 响应拦截器
  instance.interceptors.response.use(
    (response: AxiosResponse) => {
      NProgress.done();

      if (response.status === 200) {
        // 处理签名数据
        if (response.data.sign) {
          return response;
        }

        // 处理业务状态码
        if (response.data.code !== undefined) {
          if (response.data.code === 9) {
            Message.error('需要重新登录');
            userStore.userOutLogin();
            router.replace('/login');
            return Promise.reject(response.data);
          } else if (response.data.code !== '0' && response.data.code !== 0) {
            // 修复：同时检查字符串'0'和数字0，因为后台可能返回不同类型
            Message.error(response.data.msg || '请求错误');
            return Promise.reject(response.data);
          }
        }
        return response;
      }
      return response;
    },
    (error: AxiosError) => {
      NProgress.done();
      handleError(error);
      return Promise.reject(error);
    }
  );

  return instance;
}

/**
 * HTTP错误处理函数
 * @param error AxiosError
 */
function handleError(error: AxiosError) {
  const userStore = useUserStore();

  if (error.response) {
    switch (error.response.status) {
      case 400:
        error.message = '错误请求';
        break;
      case 401:
        error.message = '未授权，请重新登录';
        userStore.userOutLogin();
        router.replace('/login');
        break;
      case 403:
        error.message = '拒绝访问';
        break;
      case 404:
        error.message = '请求错误,未找到该资源';
        break;
      case 405:
        error.message = '请求方法未允许';
        break;
      case 408:
        error.message = '请求超时';
        break;
      case 426:
        error.message = '账号密码错误';
        break;
      case 428:
        error.message = '验证码不正确,请刷新';
        break;
      case 500:
        error.message = '服务器端出错';
        break;
      case 501:
        error.message = '网络未实现';
        break;
      case 502:
        error.message = '网络错误';
        break;
      case 503:
        error.message = '服务不可用';
        break;
      case 504:
        error.message = '网络超时';
        break;
      case 505:
        error.message = 'http版本不支持该请求';
        break;
      default:
        error.message = `连接错误${error.response.status}`;
    }
  } else {
    if (JSON.stringify(error).includes('timeout')) {
      Message.error('服务器响应超时，请刷新当前页');
    } else {
      Message.error('连接服务器失败');
    }
  }
  Message.error(error.message);
}

/**
 * 通用的HTTP请求类
 * 提供各种HTTP方法的封装
 */
export class HttpService {
  constructor(private instance: AxiosInstance) {}

  /**
   * GET请求
   * @param url 请求地址
   * @param config 请求配置，可以包含params等
   * @returns Promise
   */
  get(url: string, config?: any) {
    return new Promise((resolve, reject) => {
      NProgress.start();
      this.instance
        .get(url, config)
        .then(res => {
          NProgress.done();
          resolve(res.data || res);
        })
        .catch(err => {
          NProgress.done();
          reject(err.data);
        });
    });
  }

  /**
   * POST请求 - JSON格式
   * @param url 请求地址
   * @param params 请求参数
   * @returns Promise
   */
  post(url: string, params?: any) {
    return new Promise((resolve, reject) => {
      NProgress.start();
      this.instance
        .post(url, JSON.stringify(params))
        .then(res => {
          NProgress.done();
          resolve(res.data);
        })
        .catch(err => {
          NProgress.done();
          // 确保 reject 的值不是 null 或 undefined
          const errorData = err.data || err.response?.data || err;
          reject(errorData);
        });
    });
  }

  /**
   * POST请求 - 表单格式
   * @param url 请求地址
   * @param params 请求参数
   * @returns Promise
   */
  postwww(url: string, params?: any) {
    return new Promise((resolve, reject) => {
      NProgress.start();
      this.instance
        .post(url, params, {
          headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
        })
        .then(res => {
          NProgress.done();
          resolve(res.data);
        })
        .catch(err => {
          NProgress.done();
          // 确保 reject 的值不是 null 或 undefined
          const errorData = err.data || err.response?.data || err;
          reject(errorData);
        });
    });
  }

  /**
   * POST请求 - 表单数据格式
   * @param url 请求地址
   * @param params 请求参数
   * @returns Promise
   */
  formDataPost(url: string, params?: any) {
    return new Promise((resolve, reject) => {
      NProgress.start();
      this.instance
        .post(url, params, {
          headers: { 'Content-Type': 'multipart/form-data' },
        })
        .then(res => {
          NProgress.done();
          resolve(res.data);
        })
        .catch(err => {
          NProgress.done();
          // 确保 reject 的值不是 null 或 undefined
          const errorData = err.data || err.response?.data || err;
          reject(errorData);
        });
    });
  }

  /**
   * PUT请求
   * @param url 请求地址
   * @param params 请求参数
   * @returns Promise
   */
  put(url: string, params?: any) {
    return new Promise((resolve, reject) => {
      NProgress.start();
      this.instance
        .put(url, params)
        .then(res => {
          NProgress.done();
          resolve(res);
        })
        .catch(err => {
          NProgress.done();
          // 确保 reject 的值不是 null 或 undefined
          const errorData = err.data || err.response?.data || err;
          reject(errorData);
        });
    });
  }

  /**
   * DELETE请求
   * @param url 请求地址
   * @param params 请求参数
   * @returns Promise
   */
  delete(url: string, params?: any) {
    return new Promise((resolve, reject) => {
      NProgress.start();
      this.instance
        .delete(url, params)
        .then(res => {
          NProgress.done();
          resolve(res);
        })
        .catch(err => {
          NProgress.done();
          // 确保 reject 的值不是 null 或 undefined
          const errorData = err.data || err.response?.data || err;
          reject(errorData);
        });
    });
  }

  /**
   * 上传文件
   * @param url 请求地址
   * @param file 文件
   * @param config 配置
   * @returns Promise
   */
  uploadFile(url: string, file: File, config?: any) {
    const formData = new FormData();
    formData.append('file', file);
    return this.formDataPost(url, formData);
  }

  /**
   * 上传文件的简化方法
   * @param url 请求地址
   * @param file 文件
   * @returns Promise
   */
  upload(url: string, file: File) {
    return this.uploadFile(url, file);
  }

  /**
   * 下载文件
   * @param url 文件地址
   */
  download(url: string) {
    const iframe = document.createElement('iframe');
    iframe.style.display = 'none';
    iframe.src = url;
    iframe.onload = function () {
      document.body.removeChild(iframe);
    };
    document.body.appendChild(iframe);
  }
}
