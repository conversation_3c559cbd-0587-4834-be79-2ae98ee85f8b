# 构建产物
.hbuilderx
node_modules
unpackage
.dist
dist/
*.zip

# 编辑器和IDE
#.vscode/*
#!.vscode/extensions.json
#.idea
#*.suo
#*.ntvs*
#*.njsproj
#*.sln
#*.sw?

# 日志
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# 操作系统
.DS_Store
Thumbs.db

# 本地配置文件
.env.local
.env.*.local

# 测试
/coverage
/test/unit/coverage/

# 临时文件
.tmp
.temp

# 生成的文档
#/docs

yarn.lock
package-lock.json
edge-debug-profile
