<template>
  <div class="p-4" style="width: 1200px; margin: 0 auto; padding: 0 100px; min-height: 720px">
    <a-steps :current="pageType" style="padding: 50px 0">
      <a-step description="填写基本资料"> 企业信息 </a-step>
      <a-step description="填写企业开户信息"> 开户信息 </a-step>
      <a-step description="填写控股股东信息"> 股东信息 </a-step>
      <a-step description="填写企业法人信息"> 法人信息 </a-step>
      <a-step description="等待审核"> 认证资料完成 </a-step>
    </a-steps>
    <!-- <a-form :model="formData"> -->
    <!-- <a-form-item>
                <a-input v-model="formData.auditRemark" placeholder="审核状态对应备注" />
            </a-form-item> -->
    <!-- <a-form-item>
                <a-input v-model="formData.auditStatus" placeholder="审核状态" />
            </a-form-item> -->
    <a-form v-if="pageType == 1" :model="formData" @submit="nextFun">
      <a-form-item
        label="公司营业地址"
        field="businessAddress"
        :rules="[{ required: true, message: '请填写公司地址' }]"
      >
        <a-input v-model="formData.businessAddress" placeholder="公司营业地址" />
      </a-form-item>
      <a-form-item
        label="主营业务/业务范围"
        field="businessScope"
        :rules="[{ required: true, message: '主营业务/业务范围' }]"
      >
        <a-textarea v-model="formData.businessScope" placeholder="主营业务/业务范围" />
      </a-form-item>
      <a-form-item
        label="企业证件类型"
        field="certificateType"
        :rules="[{ required: true, message: '主营业务/业务范围' }]"
      >
        <!-- <a-input v-model="formData.certificateType" placeholder="企业证件类型" /> -->
        <a-dropdown :popup-max-height="false" @select="certificateTypeSelect">
          <a-button>
            {{ certificateTypeName ? certificateTypeName : '请选择' }}<icon-down />
          </a-button>
          <template #content>
            <a-doption
              v-for="item in certificateTypeArr"
              :key="item.value || item.id"
              :value="item"
            >
              {{ item.name }}
            </a-doption>
          </template>
        </a-dropdown>
      </a-form-item>
      <a-form-item
        label="企业邮箱"
        field="companyEmail"
        :rules="[{ required: true, message: '企业邮箱' }]"
      >
        <a-input v-model="formData.companyEmail" placeholder="企业邮箱" />
      </a-form-item>
      <a-form-item
        label="企业电话"
        field="companyPhone"
        :rules="[{ required: true, message: '企业电话' }]"
      >
        <a-input v-model="formData.companyPhone" placeholder="企业电话" />
      </a-form-item>
      <a-form-item
        label="注册区域"
        field="registerArea"
        :rules="[{ required: true, message: '注册区域' }]"
      >
        <a-input v-model="formData.registerArea" placeholder="注册区域" />
      </a-form-item>
      <a-form-item
        label="企业注册名称"
        field="registerName"
        :rules="[{ required: true, message: '企业注册名称' }]"
      >
        <a-input v-model="formData.registerName" placeholder="企业注册名称" />
      </a-form-item>
      <a-form-item
        label="企业注册编号/统一社会信用代码"
        field="registerNo"
        :rules="[{ required: true, message: '企业注册编号/统一社会信用代码' }]"
      >
        <a-input v-model="formData.registerNo" placeholder="企业注册编号/统一社会信用代码" />
      </a-form-item>
      <a-form-item label="企业类型" field="type" :rules="[{ required: true, message: '企业类型' }]">
        <!-- <a-input v-model="formData.type" placeholder="企业类型" /> -->
        <a-dropdown :popup-max-height="false" @select="typeSelect">
          <a-button>{{ typeName ? typeName : '请选择' }}<icon-down /></a-button>
          <template #content>
            <a-doption v-for="item in typeArr" :key="item.value || item.id" :value="item">
              {{ item.name }}
            </a-doption>
          </template>
        </a-dropdown>
      </a-form-item>
      <a-form-item
        label="企业营业地址"
        field="city"
        :rules="[{ required: true, message: '企业营业地址' }]"
      >
        <a-cascader
          v-model="cityid"
          :options="addressJson"
          :style="{ width: '320px' }"
          placeholder="请选择营业地址"
          @change="cityChange"
        />
      </a-form-item>
      <a-form-item label="营业执照">
        <a-upload
          action="/"
          :file-list="certificateFile ? [certificateFile] : []"
          :show-file-list="false"
          @progress="certificateOnProgress"
        >
          <template #upload-button>
            <div
              :class="`arco-upload-list-item${
                certificateFile && certificateFile.status === 'error'
                  ? ' arco-upload-list-item-error'
                  : ''
              }`"
            >
              <div
                v-if="certificateFile && certificateFile.url"
                class="arco-upload-list-picture custom-upload-avatar"
              >
                <img :src="certificateFile.url" />
                <div class="arco-upload-list-picture-mask">
                  <IconEdit />
                </div>
                <a-progress
                  v-if="certificateFile.status === 'uploading' && certificateFile.percent < 100"
                  :percent="certificateFile.percent"
                  type="circle"
                  size="mini"
                  :style="{
                    position: 'absolute',
                    left: '50%',
                    top: '50%',
                    transform: 'translateX(-50%) translateY(-50%)',
                  }"
                />
              </div>
              <div v-else class="arco-upload-picture-card">
                <div class="arco-upload-picture-card-text">
                  <IconPlus />
                  <div style="margin-top: 10px; font-weight: 600">点击上传</div>
                </div>
              </div>
            </div>
          </template>
        </a-upload>
      </a-form-item>
      <a-form-item label="店铺门面照">
        <a-upload
          action="/"
          :file-list="companyFaceFile ? [companyFaceFile] : []"
          :show-file-list="false"
          @progress="companyFaceOnProgress"
        >
          <template #upload-button>
            <div
              :class="`arco-upload-list-item${
                companyFaceFile && companyFaceFile.status === 'error'
                  ? ' arco-upload-list-item-error'
                  : ''
              }`"
            >
              <div
                v-if="companyFaceFile && companyFaceFile.url"
                class="arco-upload-list-picture custom-upload-avatar"
              >
                <img :src="companyFaceFile.url" />
                <div class="arco-upload-list-picture-mask">
                  <IconEdit />
                </div>
                <a-progress
                  v-if="companyFaceFile.status === 'uploading' && companyFaceFile.percent < 100"
                  :percent="companyFaceFile.percent"
                  type="circle"
                  size="mini"
                  :style="{
                    position: 'absolute',
                    left: '50%',
                    top: '50%',
                    transform: 'translateX(-50%) translateY(-50%)',
                  }"
                />
              </div>
              <div v-else class="arco-upload-picture-card">
                <div class="arco-upload-picture-card-text">
                  <IconPlus />
                  <div style="margin-top: 10px; font-weight: 600">点击上传</div>
                </div>
              </div>
            </div>
          </template>
        </a-upload>
      </a-form-item>
      <a-form-item label="开户许可证">
        <a-upload
          action="/"
          :file-list="openingPermitFile ? [openingPermitFile] : []"
          :show-file-list="false"
          @progress="openingPermitOnProgress"
        >
          <template #upload-button>
            <div
              :class="`arco-upload-list-item${
                openingPermitFile && openingPermitFile.status === 'error'
                  ? ' arco-upload-list-item-error'
                  : ''
              }`"
            >
              <div
                v-if="openingPermitFile && openingPermitFile.url"
                class="arco-upload-list-picture custom-upload-avatar"
              >
                <img :src="openingPermitFile.url" />
                <div class="arco-upload-list-picture-mask">
                  <IconEdit />
                </div>
                <a-progress
                  v-if="openingPermitFile.status === 'uploading' && openingPermitFile.percent < 100"
                  :percent="openingPermitFile.percent"
                  type="circle"
                  size="mini"
                  :style="{
                    position: 'absolute',
                    left: '50%',
                    top: '50%',
                    transform: 'translateX(-50%) translateY(-50%)',
                  }"
                />
              </div>
              <div v-else class="arco-upload-picture-card">
                <div class="arco-upload-picture-card-text">
                  <IconPlus />
                  <div style="margin-top: 10px; font-weight: 600">点击上传</div>
                </div>
              </div>
            </div>
          </template>
        </a-upload>
      </a-form-item>
      <a-form-item label="商户网站/APP截图">
        <a-upload
          action="/"
          :file-list="companyWebsiteFile ? [companyWebsiteFile] : []"
          :show-file-list="false"
          @progress="companyWebsiteFileOnProgress"
        >
          <template #upload-button>
            <div
              :class="`arco-upload-list-item${
                companyWebsiteFile && companyWebsiteFile.status === 'error'
                  ? ' arco-upload-list-item-error'
                  : ''
              }`"
            >
              <div
                v-if="companyWebsiteFile && companyWebsiteFile.url"
                class="arco-upload-list-picture custom-upload-avatar"
              >
                <img :src="companyWebsiteFile.url" />
                <div class="arco-upload-list-picture-mask">
                  <IconEdit />
                </div>
                <a-progress
                  v-if="
                    companyWebsiteFile.status === 'uploading' && companyWebsiteFile.percent < 100
                  "
                  :percent="companyWebsiteFile.percent"
                  type="circle"
                  size="mini"
                  :style="{
                    position: 'absolute',
                    left: '50%',
                    top: '50%',
                    transform: 'translateX(-50%) translateY(-50%)',
                  }"
                />
              </div>
              <div v-else class="arco-upload-picture-card">
                <div class="arco-upload-picture-card-text">
                  <IconPlus />
                  <div style="margin-top: 10px; font-weight: 600">点击上传</div>
                </div>
              </div>
            </div>
          </template>
        </a-upload>
      </a-form-item>
      <div class="pageBtns">
        <a-button type="primary" html-type="submit"> 下一步 </a-button>
      </div>
    </a-form>
    <a-form v-if="pageType == 2" :model="formData" @submit="nextFun">
      <a-form-item
        label="银行账号名称"
        field="bankAcctName"
        :rules="[{ required: true, message: '银行账号名称' }]"
      >
        <a-input v-model="formData.bankAcctName" placeholder="银行账号名称" />
      </a-form-item>
      <a-form-item
        label="银行帐号号码"
        field="bankAcctNum"
        :rules="[{ required: true, message: '银行帐号号码' }]"
      >
        <a-input v-model="formData.bankAcctNum" placeholder="银行账号号码" />
      </a-form-item>
      <a-form-item
        label="银行名称"
        field="bankName"
        :rules="[{ required: true, message: '银行名称' }]"
      >
        <a-input v-model="formData.bankName" placeholder="银行名称" />
      </a-form-item>
      <a-form-item label="选择支行">
        <a-button type="primary" @click="bankSreachType = true"> 选择支行 </a-button>
      </a-form-item>
      <a-form-item
        label="支行名称"
        field="branchBankName"
        :rules="[{ required: true, message: '支行名称' }]"
      >
        <a-input v-model="formData.branchBankName" :disabled="true" placeholder="支行名称" />
      </a-form-item>
      <div class="pageBtns">
        <a-button type="primary" @click="upFun"> 上一步 </a-button>
        <a-button type="primary" html-type="submit"> 下一步 </a-button>
      </div>
      <a-modal
        v-model:visible="bankSreachType"
        :on-before-ok="handleBeforeOk"
        unmount-on-close
        @cancel="bankSreachType = false"
      >
        <div>
          <div style="padding: 10px">第一步.请输入支行关键字：</div>
          <a-input v-model="bankData.key" placeholder="支行关键字" />
          <div style="padding: 10px">第二步.请选择支行所在地区：</div>
          <a-cascader
            v-model="bankData.areaCode"
            :options="lotusAddressJson"
            :style="{ width: '320px' }"
            placeholder="请选择营业地址"
          />
          <div style="padding: 10px">第三步.查询支行</div>
          <a-button type="primary" @click="getBranchBank"> 查询支行 </a-button>
          <div style="padding: 10px">第四步.请选择支行：</div>
          <!-- <a-cascader @change="bankSelectFun" :field-names="{
                            value: 'code',
                            label: 'bankBranchName'
                        }" :options="bankOptions" :style="{width:'320px'}" placeholder="请选择支行" /> -->
          <a-select
            v-model="bankSelect"
            :style="{ width: '320px' }"
            placeholder="Please select ..."
          >
            <a-option
              v-for="item of bankOptions"
              :key="item.code || item.bankBranchName"
              :value="item"
              :label="item.bankBranchName"
            />
          </a-select>
        </div>
      </a-modal>
    </a-form>
    <a-form v-if="pageType == 3" :model="formData" @submit="nextFun">
      <a-form-item
        label="控股股东姓名"
        field="shareholderName"
        :rules="[{ required: true, message: '控股股东姓名' }]"
      >
        <a-input v-model="formData.shareholderName" placeholder="控股股东姓名" />
      </a-form-item>
      <a-form-item
        label="控股股东证件号"
        field="shareholderCertno"
        :rules="[{ required: true, message: '控股股东证件号' }]"
      >
        <a-input v-model="formData.shareholderCertno" placeholder="控股股东证件号" />
      </a-form-item>
      <a-form-item
        label="控股股东证件有效期"
        field="shareholderCertExpire"
        :rules="[{ required: true, message: '控股股东证件有效期' }]"
      >
        <a-date-picker
          v-model="formData.shareholderCertExpire"
          style="width: 200px"
          :value-format="'YYYY-MM-DD'"
        />
      </a-form-item>
      <a-form-item
        label="控股股东证件证件类型"
        field="shareholderCertType"
        :rules="[{ required: true, message: '控股股东证件证件类型' }]"
      >
        <a-dropdown :popup-max-height="false" @select="shareholderTypeSelect">
          <a-button>
            {{ shareholderTypeName ? shareholderTypeName : '请选择' }}<icon-down />
          </a-button>
          <template #content>
            <a-doption :value="{ value: 1, name: '身份证' }"> 身份证 </a-doption>
            <a-doption :value="{ value: 11, name: '营业执照' }"> 营业执照 </a-doption>
          </template>
        </a-dropdown>
      </a-form-item>
      <a-form-item
        label="控股股东家庭地址"
        field="shareholderHomeAddr"
        :rules="[{ required: true, message: '控股股东家庭地址' }]"
      >
        <a-input v-model="formData.shareholderHomeAddr" placeholder="控股股东家庭地址" />
      </a-form-item>
      <div class="pageBtns">
        <a-button type="primary" @click="upFun"> 上一步 </a-button>
        <a-button type="primary" html-type="submit"> 下一步 </a-button>
      </div>
    </a-form>
    <a-form v-if="pageType == 4" :model="formData" @submit="nextFun">
      <a-form-item
        label="法人身份证号"
        field="idCardNo"
        :rules="[{ required: true, message: '支行名称' }]"
      >
        <a-input v-model="formData.idCardNo" placeholder="法人身份证号" />
      </a-form-item>
      <a-form-item
        label="有效日期"
        field="effectiveDate"
        :rules="[{ required: true, message: '支行名称' }]"
      >
        <a-date-picker
          v-model="formData.effectiveDate"
          style="width: 200px"
          :value-format="'YYYY-MM-DD'"
        />
        <!-- <a-input v-model="formData.effectiveDate" placeholder="有效日期" /> -->
      </a-form-item>
      <a-form-item
        label="法人联系地址"
        field="legalAddress"
        :rules="[{ required: true, message: '支行名称' }]"
      >
        <a-input v-model="formData.legalAddress" placeholder="法人联系地址" />
      </a-form-item>
      <a-form-item
        label="法人邮箱"
        field="legalEmail"
        :rules="[{ required: true, message: '支行名称' }]"
      >
        <a-input v-model="formData.legalEmail" placeholder="法人邮箱" />
      </a-form-item>
      <a-form-item
        label="法人姓名"
        field="legalName"
        :rules="[{ required: true, message: '支行名称' }]"
      >
        <a-input v-model="formData.legalName" placeholder="法人姓名" />
      </a-form-item>
      <a-form-item
        label="法人手机号"
        field="legalPhone"
        :rules="[{ required: true, message: '支行名称' }]"
      >
        <a-input v-model="formData.legalPhone" placeholder="法人手机号" />
      </a-form-item>
      <a-form-item label="法人身份证正面" field="">
        <a-upload
          action="/"
          :file-list="idCardFile ? [idCardFile] : []"
          :show-file-list="false"
          @progress="cardOnProgress"
        >
          <template #upload-button>
            <div
              :class="`arco-upload-list-item${
                idCardFile && idCardFile.status === 'error' ? ' arco-upload-list-item-error' : ''
              }`"
            >
              <div
                v-if="idCardFile && idCardFile.url"
                class="arco-upload-list-picture custom-upload-avatar"
              >
                <img :src="idCardFile.url" />
                <div class="arco-upload-list-picture-mask">
                  <IconEdit />
                </div>
                <a-progress
                  v-if="idCardFile.status === 'uploading' && idCardFile.percent < 100"
                  :percent="idCardFile.percent"
                  type="circle"
                  size="mini"
                  :style="{
                    position: 'absolute',
                    left: '50%',
                    top: '50%',
                    transform: 'translateX(-50%) translateY(-50%)',
                  }"
                />
              </div>
              <div v-else class="arco-upload-picture-card">
                <div class="arco-upload-picture-card-text">
                  <IconPlus />
                  <div style="margin-top: 10px; font-weight: 600">点击上传</div>
                </div>
              </div>
            </div>
          </template>
        </a-upload>
      </a-form-item>
      <a-form-item label="法人身份证反面" field="">
        <a-upload
          action="/"
          :file-list="idCardBFile ? [idCardBFile] : []"
          :show-file-list="false"
          @progress="cardBOnProgress"
        >
          <template #upload-button>
            <div
              :class="`arco-upload-list-item${
                idCardBFile && idCardBFile.status === 'error' ? ' arco-upload-list-item-error' : ''
              }`"
            >
              <div
                v-if="idCardBFile && idCardBFile.url"
                class="arco-upload-list-picture custom-upload-avatar"
              >
                <img :src="idCardBFile.url" />
                <div class="arco-upload-list-picture-mask">
                  <IconEdit />
                </div>
                <a-progress
                  v-if="idCardBFile.status === 'uploading' && idCardBFile.percent < 100"
                  :percent="idCardBFile.percent"
                  type="circle"
                  size="mini"
                  :style="{
                    position: 'absolute',
                    left: '50%',
                    top: '50%',
                    transform: 'translateX(-50%) translateY(-50%)',
                  }"
                />
              </div>
              <div v-else class="arco-upload-picture-card">
                <div class="arco-upload-picture-card-text">
                  <IconPlus />
                  <div style="margin-top: 10px; font-weight: 600">点击上传</div>
                </div>
              </div>
            </div>
          </template>
        </a-upload>
      </a-form-item>
      <div class="pageBtns">
        <a-button type="primary" @click="upFun"> 上一步 </a-button>
        <a-button type="primary" html-type="submit"> 完成 </a-button>
      </div>
    </a-form>
    <template v-if="pageType == 5">
      <a-result status="success" title="基础信息保存成功！">
        <template #subtitle> 信息将作为产品申请时的认证材料 </template>
        <template #extra>
          <a-space>
            <router-link to="/user/userInfo">
              <a-button type="primary"> 回到首页 </a-button>
            </router-link>
          </a-space>
        </template>
      </a-result>
    </template>
    <!-- </a-form> -->
  </div>
</template>

<script lang="ts" setup>
  import { ref, reactive, onMounted } from 'vue';
  import { Message } from '@arco-design/web-vue';
  import { addressJson } from './yuanshishuju.js';
  import { lotusAddressJson } from './bankListData.js';
  import {
    userInfoSave,
    basicType,
    getBranchBankList,
    fileUpload,
    portalUserInfo,
    getUserFile,
    userInfoSubmit,
  } from '@/http/api/authentication';
  import { IconDown } from '@arco-design/web-vue/es/icon';

  const pageType = ref<number>(1);
  const bankSreachType = ref<boolean>(false);

  const certificateTypeArr = ref<any>([]);
  const certificateTypeName = ref<string>('');
  const shareholderTypeName = ref<string>('');

  const idCardFile = ref();
  const idCardBFile = ref();
  const bankFile = ref();
  const certificateFile = ref();
  const openingPermitFile = ref();
  const companyWebsiteFile = ref();
  const companyFaceFile = ref();

  const typeArr = ref<any>([]);
  const typeName = ref<string>('');
  const cityid = ref<string>('');

  const bankOptions = ref<any>([]);
  const bankSelect = ref<any>('');
  const bankData = reactive<any>({
    areaCode: '',
    key: '',
  });
  const formData = reactive<any>({
    auditRemark: '',
    auditStatus: 0,
    bankAcctName: '',
    bankAcctNum: '',
    bankName: '',
    branchBankName: '',
    branchBankNo: '',
    businessAddress: '',
    businessScope: '',
    certificateType: '',
    city: '',
    companyEmail: '',
    companyPhone: '',
    county: '',
    effectiveDate: '',
    idCardNo: '',
    legalAddress: '',
    legalEmail: '',
    legalName: '',
    legalPhone: '',
    province: '',
    registerArea: '',
    registerName: '',
    registerNo: '',
    type: '',
    // 控股股东信息
    shareholderName: '',
    shareholderCertno: '',
    shareholderCertExpire: '',
    shareholderCertType: '',
    shareholderHomeAddr: '',
  });

  // 初始化页面数据
  const pageInit = async () => {
    let initRes: any = await basicType();
    fileInit();
    certificateTypeArr.value = initRes.certificateType;
    typeArr.value = initRes.companyType;
  };

  // 图片回显处理
  const fileInit = async () => {
    let fileRes: any = await getUserFile();
    if (fileRes.length == 0) return;
    let data = {
      status: 'uploading',
      percent: 1,
    };
    fileRes.forEach(element => {
      switch (element.bizType) {
        case 'identity':
          if (element.fileType == 1) {
            idCardFile.value = { ...element, ...data };
          } else {
            idCardBFile.value = { ...element, ...data };
          }
          break;
        case 'bank':
          bankFile.value = { ...element, ...data };
          break;
        case 'certificate':
          certificateFile.value = { ...element, ...data };
          break;
        case 'openingPermit':
          openingPermitFile.value = { ...element, ...data };
          break;
        case 'companyFace':
          companyFaceFile.value = { ...element, ...data };
          break;
        case 'companyWebsite':
          companyWebsiteFile.value = { ...element, ...data };
          break;

        default:
          break;
      }
    });
  };

  // 保存企业基本信息
  const InfoSave = async () => {
    let saveRes = await userInfoSave({ ...formData });
    console.log(saveRes);
  };

  // 企业经营地址
  const cityChange = (v: any) => {
    if (v.length < 6) return;
    formData.province = v.slice(0, 2);
    formData.city = v.slice(0, 4);
    formData.county = v.slice(0, 6);
  };

  // 获取草稿
  const getPortalUserInfo = async () => {
    let userRes: any = await portalUserInfo();
    for (let key in userRes) {
      if (userRes[key]) {
        formData[key] = userRes[key];
        switch (key) {
          case 'county':
            cityid.value = userRes[key];
            break;

          case 'certificateType':
            certificateTypeArr.value.forEach(element => {
              if (element.value == userRes[key]) certificateTypeName.value = element.name;
            });
            break;

          case 'type':
            typeArr.value.forEach(element => {
              if (element.value == userRes[key]) typeName.value = element.name;
            });
            break;

          case 'shareholderCertType':
            if (userRes[key] == 1) {
              shareholderTypeName.value = '身份证';
            } else {
              shareholderTypeName.value = '营业执照';
            }
            break;

          default:
            break;
        }
      }
    }
  };

  // 下一步
  const nextFun = async (values: any, _: any) => {
    console.log(values);
    if (values != undefined) return;
    switch (pageType.value) {
      case 1:
        if (!certificateFile.value) {
          Message.error('请上传营业执照');
          return;
        }
        if (!companyFaceFile.value) {
          Message.error('请上传店铺门面照');
          return;
        }
        if (!openingPermitFile.value) {
          Message.error('请上传开户许可证');
          return;
        }
        // 检查图片是否上传
        InfoSave();
        pageType.value++;
        break;
      case 2:
        // 检查图片是否上传
        InfoSave();
        pageType.value++;
        break;
      case 3:
        InfoSave();
        pageType.value++;
        break;
      case 4:
        if (!idCardFile.value) {
          Message.error('请上传法人身份证');
          return;
        }
        if (!idCardBFile.value) {
          Message.error('请上传法人身份证反面');
          return;
        }
        // 检查图片是否上传
        InfoSave();
        userInfoPost();
        // pageType.value++
        break;
      case 5:
        // 检查图片是否上传
        break;

      default:
        break;
    }
  };

  // 上一步
  const upFun = async () => {
    pageType.value--;
  };

  const userInfoPost = async () => {
    let postRes: any = await userInfoSubmit({ ...formData });
    if (postRes.code == 0) {
      pageType.value++;
    }
  };

  // 选择企业证件类型
  const shareholderTypeSelect = async (v: any) => {
    formData.shareholderCertType = v.value;
    shareholderTypeName.value = v.name;
  };

  // 选择企业证件类型
  const certificateTypeSelect = async (v: any) => {
    formData.certificateType = v.value;
    certificateTypeName.value = v.name;
  };

  // 选择企业证件类型
  const typeSelect = async (v: any) => {
    formData.type = v.value;
    typeName.value = v.name;
  };

  // 身份证上传
  const cardOnProgress = async currentFile => {
    idCardFile.value = currentFile;
    const _ = await fileUpload({
      bizType: 'identity',
      file: currentFile.file,
      fileType: '1',
    });
  };

  // 身份证上传
  const cardBOnProgress = async currentFile => {
    idCardBFile.value = currentFile;
    const _ = await fileUpload({
      bizType: 'identity',
      file: currentFile.file,
      fileType: '2',
    });
  };

  // 银行卡上传 (currently unused)
  const _bankOnProgress = async currentFile => {
    bankFile.value = currentFile;
    const _ = await fileUpload({
      bizType: 'bank',
      file: currentFile.file,
      fileType: '1',
    });
  };

  // 营业执照上传
  const certificateOnProgress = async currentFile => {
    console.log(currentFile);
    certificateFile.value = currentFile;
    const _ = await fileUpload({
      bizType: 'certificate',
      file: currentFile.file,
      fileType: '1',
    });
  };

  // 开户许可证上传
  const openingPermitOnProgress = async currentFile => {
    openingPermitFile.value = currentFile;
    const _ = await fileUpload({
      bizType: 'openingPermit',
      file: currentFile.file,
      fileType: '1',
    });
  };

  // 店铺门面照上传
  const companyFaceOnProgress = async currentFile => {
    companyFaceFile.value = currentFile;
    const _ = await fileUpload({
      bizType: 'companyFace',
      file: currentFile.file,
      fileType: '1',
    });
  };

  // 商户网站/APP截图上传
  const companyWebsiteFileOnProgress = async currentFile => {
    companyWebsiteFile.value = currentFile;
    const _ = await fileUpload({
      bizType: 'companyWebsite',
      file: currentFile.file,
      fileType: '1',
    });
  };

  // 查询银行支行
  const getBranchBank = async () => {
    let bankList = await getBranchBankList({
      areaCode: bankData.areaCode.slice(0, 4),
      key: bankData.key,
    });
    bankOptions.value = bankList;
  };

  // 支行确认
  const handleBeforeOk = async (_a: any) => {
    if (bankSelect.value) {
      formData.branchBankName = bankSelect.value.bankBranchName;
      formData.branchBankNo = bankSelect.value.code;
    }
  };

  // const bankSelectFun = (a: any) => {
  //     console.log(a)
  //     bankSelect.value = a
  // }

  onMounted(async () => {
    await pageInit();
    await getPortalUserInfo();
  });
</script>

<style lang="scss" scoped>
  .pageBtns {
    padding: 50px 200px;
    display: flex;
    justify-content: space-around;
  }
</style>
