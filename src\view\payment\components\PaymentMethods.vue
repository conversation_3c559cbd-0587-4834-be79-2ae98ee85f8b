<template>
  <div class="payment-methods-container">
    <a-card class="payment-card" :bordered="false">
      <template #title>
        <div class="payment-header">
          <icon-credit-card class="payment-icon" />
          <span class="payment-title">选择支付方式</span>
        </div>
      </template>

      <div v-if="loading" class="loading-container">
        <a-spin :size="24" />
        <p class="loading-text">正在加载支付方式...</p>
      </div>

      <div v-else class="payment-content">
        <!-- 在线支付方式 -->
        <div v-if="hasOnlinePayment" class="payment-section">
          <h4 class="section-title">在线支付</h4>
          <div class="payment-options">
            <OnlinePayment
              :methods="onlinePaymentMethods"
              :selected-method="selectedOnlineMethod"
              :amount="onlinePaymentAmount"
              @select="handleOnlinePaymentSelect"
            />
          </div>
        </div>

        <!-- 账户支付方式 -->
        <div v-if="hasAccountPayment" class="payment-section">
          <h4 class="section-title">账户支付</h4>

          <!-- 余额支付 -->
          <div v-if="balanceMethod" class="payment-option">
            <BalancePayment
              :method="balanceMethod"
              :user-balance="userAssets?.balance || 0"
              :max-amount="maxBalanceAmount"
              :amount="balanceAmount"
              @update:amount="handleBalanceAmountChange"
              @toggle="handleBalanceToggle"
            />
          </div>

          <!-- 积分支付 -->
          <div v-if="pointsMethod" class="payment-option">
            <PointsPayment
              :method="pointsMethod"
              :user-points="userAssets?.points || 0"
              :points-rate="userAssets?.pointsRate || 100"
              :max-points="maxPointsAmount"
              :points="pointsAmount"
              @update:points="handlePointsAmountChange"
              @toggle="handlePointsToggle"
            />
          </div>
        </div>

        <!-- 优惠券 -->
        <div v-if="hasCoupons" class="payment-section">
          <h4 class="section-title">优惠券</h4>
          <div class="payment-option">
            <CouponPayment
              :available-coupons="availableCoupons"
              :selected-coupons="selectedCoupons"
              :order-amount="orderAmount"
              @select="handleCouponSelect"
              @remove="handleCouponRemove"
            />
          </div>
        </div>

        <!-- 支付方式为空提示 -->
        <div v-if="!hasAnyPaymentMethod" class="empty-payment">
          <a-empty description="暂无可用的支付方式">
            <template #image>
              <icon-credit-card style="font-size: 64px; color: #c9cdd4" />
            </template>
          </a-empty>
        </div>

        <!-- 支付方式说明 -->
        <div v-if="hasAnyPaymentMethod" class="payment-tips">
          <a-alert type="info" :show-icon="false">
            <template #icon>
              <icon-info-circle />
            </template>
            <div class="tips-content">
              <p>• 支持多种支付方式组合使用</p>
              <p>• 优惠券和积分可与其他支付方式叠加</p>
              <p>• 余额支付安全便捷，无手续费</p>
            </div>
          </a-alert>
        </div>
      </div>
    </a-card>
  </div>
</template>

<script setup lang="ts">
  import { computed, ref, watch } from 'vue';
  import { IconCheckCircle, IconInfoCircle } from '@arco-design/web-vue/es/icon';
  import { usePaymentStore } from '@/store/modules/payment';
  import { usePaymentCalculation } from '../hooks/usePaymentCalculation';
  import OnlinePayment from './methods/OnlinePayment.vue';
  import BalancePayment from './methods/BalancePayment.vue';
  import PointsPayment from './methods/PointsPayment.vue';
  import CouponPayment from './methods/CouponPayment.vue';
  import type { PaymentMethod, CouponInfo } from '@/http/api/payment/types';

  // Props
  interface Props {
    loading?: boolean;
  }

  const props = withDefaults(defineProps<Props>(), {
    loading: false,
  });

  // Emits
  const emit = defineEmits(['payment-change', 'amount-change']);

  // Store和Hooks
  const paymentStore = usePaymentStore();
  const paymentCalculation = usePaymentCalculation();

  // 本地状态
  const selectedOnlineMethod = ref<string>('');
  const balanceAmount = ref(0);
  const pointsAmount = ref(0);

  // 计算属性
  const userAssets = computed(() => paymentStore.userAssets);
  const orderAmount = computed(() => paymentStore.currentOrder?.finalAmount || 0);
  const availableCoupons = computed(() => userAssets.value?.availableCoupons || []);
  const selectedCoupons = computed(() => paymentStore.selectedCoupons);

  // 支付方式分类
  const onlinePaymentMethods = computed(() =>
    paymentStore.paymentMethods.filter(m => m.type === 'wechat' || m.type === 'alipay')
  );

  const balanceMethod = computed(() => paymentStore.paymentMethods.find(m => m.type === 'balance'));

  const pointsMethod = computed(() => paymentStore.paymentMethods.find(m => m.type === 'points'));

  // 支付方式可用性
  const hasOnlinePayment = computed(() => onlinePaymentMethods.value.length > 0);
  const hasAccountPayment = computed(() => balanceMethod.value || pointsMethod.value);
  const hasCoupons = computed(() => availableCoupons.value.length > 0);
  const hasAnyPaymentMethod = computed(
    () => hasOnlinePayment.value || hasAccountPayment.value || hasCoupons.value
  );

  // 金额限制
  const onlinePaymentAmount = computed(() => paymentCalculation.onlinePaymentAmount.value);
  const maxBalanceAmount = computed(() => paymentCalculation.maxBalanceDeduction.value);
  const maxPointsAmount = computed(() =>
    Math.floor(paymentCalculation.maxPointsDeduction.value * (userAssets.value?.pointsRate || 100))
  );

  // 事件处理
  function handleOnlinePaymentSelect(methodType: string) {
    selectedOnlineMethod.value = methodType;

    // 清除其他在线支付方式
    onlinePaymentMethods.value.forEach(method => {
      if (method.type !== methodType) {
        paymentStore.removePaymentMethod(method.type);
      }
    });

    // 选择当前支付方式
    const method = onlinePaymentMethods.value.find(m => m.type === methodType);
    if (method && onlinePaymentAmount.value > 0) {
      paymentStore.selectPaymentMethod(method, onlinePaymentAmount.value);
    }

    emitPaymentChange();
  }

  function handleBalanceAmountChange(amount: number) {
    balanceAmount.value = amount;
    paymentCalculation.setBalanceUsage(amount);
    emitPaymentChange();
  }

  function handleBalanceToggle(enabled: boolean) {
    if (enabled && balanceMethod.value) {
      const amount = Math.min(maxBalanceAmount.value, orderAmount.value);
      handleBalanceAmountChange(amount);
    } else {
      handleBalanceAmountChange(0);
    }
  }

  function handlePointsAmountChange(points: number) {
    pointsAmount.value = points;
    paymentCalculation.setPointsUsage(points);
    emitPaymentChange();
  }

  function handlePointsToggle(enabled: boolean) {
    if (enabled && pointsMethod.value) {
      const maxPoints = maxPointsAmount.value;
      handlePointsAmountChange(maxPoints);
    } else {
      handlePointsAmountChange(0);
    }
  }

  function handleCouponSelect(coupon: CouponInfo) {
    paymentStore.selectCoupon(coupon);
    emitPaymentChange();
  }

  function handleCouponRemove(couponId: string) {
    const coupon = selectedCoupons.value.find(c => c.couponId === couponId);
    if (coupon) {
      paymentStore.selectCoupon(coupon); // 再次选择会移除
    }
    emitPaymentChange();
  }

  function emitPaymentChange() {
    emit('payment-change', paymentStore.selectedMethods);
    emit('amount-change', paymentCalculation.onlinePaymentAmount.value);
  }

  // 监听在线支付金额变化
  watch(onlinePaymentAmount, newAmount => {
    if (selectedOnlineMethod.value && newAmount > 0) {
      const method = onlinePaymentMethods.value.find(m => m.type === selectedOnlineMethod.value);
      if (method) {
        paymentStore.selectPaymentMethod(method, newAmount);
      }
    }
  });

  // 监听支付方式变化
  watch(
    () => paymentStore.selectedMethods,
    () => {
      emitPaymentChange();
    },
    { deep: true }
  );
</script>

<style scoped lang="scss">
  .payment-methods-container {
    width: 100%;
  }

  .payment-card {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border-radius: 8px;

    :deep(.arco-card-header) {
      border-bottom: 1px solid #f2f3f5;
      padding: 16px 20px;
    }

    :deep(.arco-card-body) {
      padding: 20px;
    }
  }

  .payment-header {
    display: flex;
    align-items: center;
    gap: 8px;

    .payment-icon {
      font-size: 18px;
      color: $primary-color;
    }

    .payment-title {
      font-size: 16px;
      font-weight: 600;
      color: $text-color;
    }
  }

  .loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px 20px;

    .loading-text {
      margin-top: 16px;
      color: $text-color-secondary;
    }
  }

  .payment-content {
    display: flex;
    flex-direction: column;
    gap: 24px;
  }

  .payment-section {
    .section-title {
      font-size: 14px;
      font-weight: 600;
      color: $text-color;
      margin: 0 0 16px 0;
      padding-bottom: 8px;
      border-bottom: 1px solid #f2f3f5;
    }

    .payment-options {
      display: flex;
      flex-direction: column;
      gap: 12px;
    }

    .payment-option {
      margin-bottom: 12px;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }

  .empty-payment {
    padding: 40px 20px;
    text-align: center;
  }

  .payment-tips {
    background-color: #f0f8ff;
    border-radius: 8px;
    padding: 16px;

    .tips-content {
      p {
        margin: 0 0 4px 0;
        font-size: 13px;
        color: $text-color-secondary;

        &:last-child {
          margin-bottom: 0;
        }
      }
    }

    :deep(.arco-alert) {
      background-color: transparent;
      border: none;
      padding: 0;
    }

    :deep(.arco-alert-icon) {
      color: $primary-color;
    }
  }

  // 响应式设计
  @media (max-width: 768px) {
    .payment-card {
      :deep(.arco-card-header) {
        padding: 12px 16px;
      }

      :deep(.arco-card-body) {
        padding: 16px;
      }
    }

    .payment-content {
      gap: 20px;
    }

    .payment-section {
      .section-title {
        margin-bottom: 12px;
      }
    }

    .payment-tips {
      padding: 12px;
    }
  }
</style>
