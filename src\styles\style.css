#app {
  /* font-family: 'LXGWWenKai-Regular' !important; */
}

#nprogress .bar {
  background-color: #165dff !important;
}

.layout-content {
  width: 100%;
  max-width: 1200px;
  margin: auto;
}

.bg-dark {
  background-color: #17171a;
}

.bg-grey {
  background-color: #f2f3f5;
}

.bg-secondary {
  background-color: #f7f8fa;
}

.bg-tertiary {
  background-color: #f2f3f5;
}

.text-blue {
  color: #165dff;
}

.text-primary {
  color: #165dff;
}

.text-secondary {
  color: #4e5969;
}

.text-tertiary {
  color: #86909c;
}

.text-success {
  color: #00b42a;
}

.text-warning {
  color: #ff7d00;
}

.text-error {
  color: #f53f3f;
}

.border-primary {
  border-color: #165dff;
}

.border-light {
  border-color: #f2f3f5;
}

.border-default {
  border-color: #e5e6eb;
}

/* 仅能显示一行文本 需要手动设置宽度 */
.text-one {
  max-width: 100%;
  display: inline-block;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  word-break: break-all;
}

/* 仅能显示两行文本 需要手动设置宽度 */
.text-two {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  display: -moz-box;
  -moz-line-clamp: 2;
  -moz-box-orient: vertical;
  word-wrap: break-word;
  word-break: break-all;
  white-space: normal;
}

/* 仅能显示三行文本 需要手动设置宽度 */
.text-three {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  display: -moz-box;
  -moz-line-clamp: 3;
  -moz-box-orient: vertical;
  word-wrap: break-word;
  word-break: break-all;
  white-space: normal;
}

/* 跳动动画 */
.tagItem {
  animation: bounce 0.75s cubic-bezier(0.05, 0, 0.2, 1) infinite alternate;
  transform: translate3d(0, 0, 0);
}

.tagItem:nth-child(1) {
  animation-delay: 0s;
}

.tagItem:nth-child(2) {
  animation-delay: 0.0333333333s;
}

.tagItem:nth-child(3) {
  animation-delay: 0.1333333333s;
}

.tagItem:nth-child(4) {
  animation-delay: 0.2333333333s;
}

.tagItem:nth-child(5) {
  animation-delay: 0.3333333333s;
}

.tagItem:nth-child(6) {
  animation-delay: 0.4333333333s;
}

.tagItem:nth-child(7) {
  animation-delay: 0.5333333333s;
}

.tagItem:nth-child(8) {
  animation-delay: 0.6333333333s;
}

.tagItem:nth-child(9) {
  animation-delay: 0.7333333333s;
}

.tagItem:nth-child(10) {
  animation-delay: 0.8333333333s;
}

.tagItem:nth-child(11) {
  animation-delay: 0.9333333333s;
}

@keyframes bounce {
  0% {
    transform: translate3d(0, 0, 0);
  }

  100% {
    transform: translate3d(0, -1em, 0);
  }
}
