{"name": "bcpc", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --mode development", "build": "vue-tsc && vite build --mode production", "build:dev": "vue-tsc && vite build --mode development", "build:prod": "vue-tsc && vite build --mode production", "preview": "vite preview", "build:release": "rimraf dist && vue-tsc && vite build --mode production && node scripts/zip-dist.js", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "format": "prettier --write src/", "test": "vitest", "test:unit": "vitest run", "test:coverage": "vitest run --coverage", "prepare": "husky install", "docs": "jsdoc -c jsdoc.config.json", "clean": "rimraf dist node_modules/.cache .temp coverage", "analyze": "vite build --mode production --debug analyze", "build:analyze": "vite build --mode production --debug analyze", "test:e2e": "playwright test"}, "_lint-staged-disabled": {"*.{vue,js,jsx,cjs,mjs,ts,tsx,cts,mts}": ["eslint --fix", "prettier --write"], "*.{css,scss,sass,less}": ["prettier --write"]}, "dependencies": {"arco-design": "^1.0.0", "axios": "^1.2.2", "browser-md5-file": "^1.1.1", "crypto-js": "^4.2.0", "encryptlong": "^3.1.4", "esbuild": "^0.25.2", "html2canvas": "^1.4.1", "js-md5": "^0.7.3", "jsencrypt": "^3.3.1", "jspdf": "^3.0.1", "nprogress": "^0.2.0", "pinia": "^2.0.29", "pinia-plugin-persistedstate": "^3.0.2", "sass": "^1.57.1", "scrollreveal": "^4.0.9", "vue": "^3.2.45", "vue-i18n": "^9.2.2", "vue-router": "4"}, "devDependencies": {"@arco-design/web-vue": "^2.42.0", "@commitlint/cli": "^18.6.1", "@commitlint/config-conventional": "^18.6.3", "@playwright/test": "^1.41.1", "@types/crypto-js": "^4.2.2", "@types/node": "^18.11.18", "@types/nprogress": "^0.2.0", "@typescript-eslint/eslint-plugin": "^6.13.0", "@typescript-eslint/parser": "^6.13.0", "@vitejs/plugin-vue": "^4.0.0", "@vitest/coverage-v8": "^0.34.6", "@vue/test-utils": "^2.4.1", "archiver": "^7.0.1", "autoprefixer": "^10.4.13", "babel-plugin-transform-remove-strict-mode": "^0.0.2", "eslint": "^8.54.0", "eslint-plugin-vue": "^9.18.1", "happy-dom": "^12.10.3", "husky": "^8.0.3", "jsdoc": "^4.0.2", "jsdoc-vuejs": "^4.0.0", "less": "^4.4.0", "lint-staged": "^15.1.0", "postcss": "^8.4.21", "prettier": "^3.1.0", "rimraf": "^5.0.10", "rollup-plugin-visualizer": "^5.9.2", "tailwindcss": "^3.2.4", "typescript": "^4.9.3", "vite": "^4.0.0", "vite-plugin-compression": "^0.5.1", "vite-plugin-pwa": "^0.17.0", "vitest": "^0.34.6", "vue-tsc": "^1.0.11", "workbox-window": "^7.0.0"}}