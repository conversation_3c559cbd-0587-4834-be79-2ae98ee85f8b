<template>
  <div class="p-4 bg-grey">
    <a-page-header
      :show-back="false"
      :style="{ background: 'var(--color-bg-2)' }"
      title="软著登记列表"
    />
    <div class="py-4">
      <a-card>
        <a-row class="mb-4">
          <a-col :flex="1">
            <a-form :model="formModel" layout="inline">
              <a-form-item field="keyword" label="关键词">
                <a-input v-model="formModel.keyword" placeholder="请输入软件名称或登记号" />
              </a-form-item>
              <a-form-item field="status" label="登记状态">
                <a-select v-model="formModel.status" placeholder="请选择" style="width: 160px">
                  <a-option value=""> 全部 </a-option>
                  <a-option value="pending"> 审核中 </a-option>
                  <a-option value="registered"> 已登记 </a-option>
                  <a-option value="rejected"> 已驳回 </a-option>
                </a-select>
              </a-form-item>
              <a-form-item field="dateRange" label="申请时间">
                <a-range-picker v-model="formModel.dateRange" style="width: 240px" />
              </a-form-item>
              <a-form-item>
                <a-button type="primary" @click="search"> 搜索 </a-button>
                <a-button style="margin-left: 8px" @click="reset"> 重置 </a-button>
              </a-form-item>
            </a-form>
          </a-col>
          <a-col :flex="'86px'">
            <a-button type="primary" @click="createRegistration"> 新增登记 </a-button>
          </a-col>
        </a-row>
        <a-table
          :loading="loading"
          :pagination="pagination"
          :columns="columns"
          :data="tableData"
          @page-change="onPageChange"
        >
          <template #status="{ record }">
            <a-tag v-if="record.status === 'pending'" color="blue"> 审核中 </a-tag>
            <a-tag v-else-if="record.status === 'registered'" color="green"> 已登记 </a-tag>
            <a-tag v-else-if="record.status === 'rejected'" color="red"> 已驳回 </a-tag>
          </template>
          <template #operations="{ record }">
            <a-space>
              <a-button type="text" @click="viewDetail(record)"> 查看 </a-button>
              <a-button
                v-if="record.status === 'registered'"
                type="text"
                @click="downloadCert(record)"
              >
                下载证书
              </a-button>
              <a-button v-if="record.status === 'rejected'" type="text" @click="reapply(record)">
                重新申请
              </a-button>
            </a-space>
          </template>
        </a-table>
      </a-card>
    </div>

    <!-- 详情弹出层 -->
    <a-modal
      v-model:visible="detailVisible"
      :width="800"
      title="软著登记详情"
      ok-text="关闭"
      hide-cancel
      @ok="detailVisible = false"
    >
      <a-descriptions :data="detailData" bordered />
      <div v-if="attachmentFiles.length" class="mt-4">
        <h3>附件材料</h3>
        <a-space>
          <a-card v-for="(file, index) in attachmentFiles" :key="index" :style="{ width: '160px' }">
            <template #cover>
              <div class="file-preview">
                <icon-file-pdf v-if="file.type === 'pdf'" />
                <icon-file-image v-else-if="file.type === 'image'" />
                <icon-file v-else />
              </div>
            </template>
            <a-typography-text>{{ file.name }}</a-typography-text>
            <a-link @click="downloadFile(file)"> 下载 </a-link>
          </a-card>
        </a-space>
      </div>
    </a-modal>
  </div>
</template>

<script lang="ts" setup>
  import { ref, reactive } from 'vue';
  import { Message } from '@arco-design/web-vue';
  import { useRouter } from 'vue-router';
  import { IconFilePdf, IconFileImage, IconFile } from '@arco-design/web-vue/es/icon';

  // 软件登记项接口
  interface SoftwareRegItem {
    registrationNo: string;
    softwareName: string;
    version: string;
    applyTime: string;
    status: string;
  }

  // 详情数据项接口
  interface DetailItem {
    label: string;
    value: string;
  }

  // 附件文件接口
  interface AttachmentFile {
    name: string;
    type: string;
    url: string;
  }

  const router = useRouter();

  // 表单数据
  const formModel = reactive({
    keyword: '',
    status: '',
    dateRange: [],
  });

  // 表格数据
  const loading = ref(false);
  const tableData = ref<SoftwareRegItem[]>([]);
  const pagination = reactive({
    total: 0,
    current: 1,
    pageSize: 10,
  });

  // 表格列定义
  const columns = [
    {
      title: '登记号',
      dataIndex: 'registrationNo',
    },
    {
      title: '软件名称',
      dataIndex: 'softwareName',
    },
    {
      title: '版本号',
      dataIndex: 'version',
    },
    {
      title: '申请时间',
      dataIndex: 'applyTime',
    },
    {
      title: '登记状态',
      dataIndex: 'status',
      slotName: 'status',
    },
    {
      title: '操作',
      slotName: 'operations',
    },
  ];

  // 详情弹窗
  const detailVisible = ref(false);
  const detailData = ref<DetailItem[]>([]);
  const attachmentFiles = ref<AttachmentFile[]>([]);

  // 获取登记列表
  const fetchData = () => {
    loading.value = true;

    // 这里替换为实际的API调用
    // getSoftwareRegList({
    //     page: pagination.current,
    //     pageSize: pagination.pageSize,
    //     keyword: formModel.keyword,
    //     status: formModel.status,
    //     startTime: formModel.dateRange?.[0],
    //     endTime: formModel.dateRange?.[1],
    // }).then(res => {
    //     tableData.value = res.records;
    //     pagination.total = res.total;
    // }).finally(() => {
    //     loading.value = false;
    // });

    // 模拟数据
    setTimeout(() => {
      tableData.value = [
        {
          registrationNo: '2023SR1234567',
          softwareName: '智能合约管理系统',
          version: 'v1.0.0',
          applyTime: '2023-10-15 14:30:45',
          status: 'registered',
        },
        {
          registrationNo: '2023SR7654321',
          softwareName: '区块链数据分析平台',
          version: 'v2.1.5',
          applyTime: '2023-11-20 09:15:22',
          status: 'pending',
        },
        {
          registrationNo: '2023SR9876543',
          softwareName: '分布式存储管理工具',
          version: 'v0.9.2',
          applyTime: '2023-09-05 16:42:33',
          status: 'rejected',
        },
      ];
      pagination.total = 3;
      loading.value = false;
    }, 500);
  };

  // 页码变化
  const onPageChange = (page: number) => {
    pagination.current = page;
    fetchData();
  };

  // 搜索
  const search = () => {
    pagination.current = 1;
    fetchData();
  };

  // 重置
  const reset = () => {
    formModel.keyword = '';
    formModel.status = '';
    formModel.dateRange = [];
    pagination.current = 1;
    fetchData();
  };

  // 新增登记
  const createRegistration = () => {
    // Message.info('新增软著登记功能待实现');
    router.push('/software/page/register');
  };

  // 查看详情
  const viewDetail = (record: SoftwareRegItem) => {
    // 实际场景中这里应该调用API获取详情
    detailData.value = [
      {
        label: '登记号',
        value: record.registrationNo,
      },
      {
        label: '软件名称',
        value: record.softwareName,
      },
      {
        label: '版本号',
        value: record.version,
      },
      {
        label: '申请时间',
        value: record.applyTime,
      },
      {
        label: '开发完成日期',
        value: '2023-08-01',
      },
      {
        label: '著作权人',
        value: '某科技有限公司',
      },
      {
        label: '登记状态',
        value:
          record.status === 'registered'
            ? '已登记'
            : record.status === 'pending'
              ? '审核中'
              : '已驳回',
      },
      {
        label: '软件简介',
        value: '这是一款基于区块链技术的软件应用，主要用于数据存证和版权保护。',
      },
    ];

    // 模拟附件文件数据
    attachmentFiles.value = [
      { name: '源代码.zip', type: 'file', url: '#' },
      { name: '操作手册.pdf', type: 'pdf', url: '#' },
      { name: '软件界面截图.png', type: 'image', url: '#' },
    ];

    detailVisible.value = true;
  };

  // 下载证书
  const downloadCert = (record: SoftwareRegItem) => {
    Message.info(`下载证书：${record.registrationNo}`);
  };

  // 重新申请
  const reapply = (record: SoftwareRegItem) => {
    Message.info(`重新申请：${record.softwareName}`);
  };

  // 下载文件
  const downloadFile = (file: AttachmentFile) => {
    Message.info(`下载文件：${file.name}`);
  };

  // 初始加载
  fetchData();
</script>

<style lang="scss" scoped>
  .file-preview {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100px;
    background-color: var(--color-fill-2);
    font-size: 36px;
    color: var(--color-text-3);
  }
</style>
