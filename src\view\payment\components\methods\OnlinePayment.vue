<template>
  <div class="online-payment-container">
    <div class="payment-methods">
      <div
        v-for="method in methods"
        :key="method.type"
        class="payment-method"
        :class="{
          selected: selectedMethod === method.type,
          disabled: !method.enabled,
        }"
        @click="handleMethodSelect(method)"
      >
        <div class="method-content">
          <div class="method-icon">
            <img :src="getMethodIcon(method.type)" :alt="method.name" class="icon-img" />
          </div>

          <div class="method-info">
            <div class="method-name">{{ method.name }}</div>
            <div v-if="method.description" class="method-description">
              {{ method.description }}
            </div>
          </div>

          <div class="method-status">
            <a-radio
              :model-value="selectedMethod === method.type"
              :disabled="!method.enabled"
              @click.stop
            />
          </div>
        </div>

        <!-- 支付金额显示 -->
        <div v-if="selectedMethod === method.type && amount > 0" class="payment-amount">
          <div class="amount-info">
            <span class="amount-label">需支付：</span>
            <span class="amount-value">¥{{ formatAmount(amount) }}</span>
          </div>
        </div>

        <!-- 不可用原因 -->
        <div v-if="!method.enabled" class="disabled-reason">
          <a-tag color="red" size="small">
            {{ getDisabledReason(method) }}
          </a-tag>
        </div>
      </div>
    </div>

    <!-- 支付说明 -->
    <div v-if="selectedMethod" class="payment-notice">
      <div class="notice-content">
        <icon-info-circle class="notice-icon" />
        <div class="notice-text">
          <p v-if="selectedMethod === 'wechat'">点击确认支付后，将跳转到微信支付页面完成付款</p>
          <p v-else-if="selectedMethod === 'alipay'">点击确认支付后，将跳转到支付宝页面完成付款</p>
          <p class="security-tip">
            <icon-lock class="security-icon" />
            支付过程采用SSL加密，保障您的资金安全
          </p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { computed } from 'vue';
  import { IconInfoCircle, IconLock } from '@arco-design/web-vue/es/icon';
  import type { PaymentMethod } from '@/http/api/payment/types';

  // Props
  interface Props {
    methods: PaymentMethod[];
    selectedMethod: string;
    amount: number;
  }

  const props = defineProps<Props>();

  // Emits
  const emit = defineEmits(['select']);

  // 计算属性
  const enabledMethods = computed(() => props.methods.filter(method => method.enabled));

  // 方法
  function handleMethodSelect(method: PaymentMethod) {
    if (!method.enabled) return;

    if (props.selectedMethod === method.type) {
      // 如果已选中，则取消选择
      emit('select', '');
    } else {
      emit('select', method.type);
    }
  }

  function getMethodIcon(type: string): string {
    const iconMap: Record<string, string> = {
      wechat: '/src/assets/images/payment/wechat-pay.png',
      alipay: '/src/assets/images/payment/alipay.png',
    };

    return iconMap[type] || '/src/assets/images/payment/default.png';
  }

  function getDisabledReason(method: PaymentMethod): string {
    if (method.type === 'wechat') {
      return '微信支付暂不可用';
    } else if (method.type === 'alipay') {
      return '支付宝支付暂不可用';
    }
    return '暂不可用';
  }

  function formatAmount(amount: number): string {
    return amount.toFixed(2);
  }
</script>

<style scoped lang="scss">
  .online-payment-container {
    width: 100%;
  }

  .payment-methods {
    display: flex;
    flex-direction: column;
    gap: 12px;
  }

  .payment-method {
    border: 1px solid #e5e6eb;
    border-radius: 8px;
    padding: 16px;
    cursor: pointer;
    transition: all 0.3s ease;
    background-color: #ffffff;

    &:hover:not(.disabled) {
      border-color: $primary-color;
      box-shadow: 0 2px 8px rgba(22, 93, 255, 0.1);
    }

    &.selected {
      border-color: $primary-color;
      background-color: #f0f8ff;
      box-shadow: 0 2px 8px rgba(22, 93, 255, 0.15);
    }

    &.disabled {
      background-color: #f7f8fa;
      border-color: #e5e6eb;
      cursor: not-allowed;
      opacity: 0.6;

      .method-content {
        opacity: 0.5;
      }
    }
  }

  .method-content {
    display: flex;
    align-items: center;
    gap: 12px;
  }

  .method-icon {
    flex-shrink: 0;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 6px;
    background-color: #f7f8fa;

    .icon-img {
      width: 32px;
      height: 32px;
      object-fit: contain;
    }
  }

  .method-info {
    flex: 1;

    .method-name {
      font-size: 15px;
      font-weight: 500;
      color: $text-color;
      margin-bottom: 4px;
    }

    .method-description {
      font-size: 13px;
      color: $text-color-secondary;
      line-height: 1.4;
    }
  }

  .method-status {
    flex-shrink: 0;

    :deep(.arco-radio) {
      .arco-radio-button {
        border-color: $primary-color;
      }

      &.arco-radio-checked .arco-radio-button {
        background-color: $primary-color;
        border-color: $primary-color;
      }
    }
  }

  .payment-amount {
    margin-top: 12px;
    padding-top: 12px;
    border-top: 1px solid #e8f3ff;

    .amount-info {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .amount-label {
        font-size: 14px;
        color: $text-color-secondary;
      }

      .amount-value {
        font-size: 16px;
        font-weight: 600;
        color: $primary-color;
      }
    }
  }

  .disabled-reason {
    margin-top: 8px;
  }

  .payment-notice {
    margin-top: 16px;
    background-color: #f0f8ff;
    border: 1px solid #d1e7ff;
    border-radius: 6px;
    padding: 12px;

    .notice-content {
      display: flex;
      gap: 8px;

      .notice-icon {
        flex-shrink: 0;
        font-size: 16px;
        color: $primary-color;
        margin-top: 2px;
      }

      .notice-text {
        flex: 1;

        p {
          margin: 0 0 8px 0;
          font-size: 13px;
          color: $text-color-secondary;
          line-height: 1.4;

          &:last-child {
            margin-bottom: 0;
          }

          &.security-tip {
            display: flex;
            align-items: center;
            gap: 4px;
            color: $success-color;
            font-weight: 500;

            .security-icon {
              font-size: 12px;
            }
          }
        }
      }
    }
  }

  // 响应式设计
  @media (max-width: 768px) {
    .payment-method {
      padding: 12px;
    }

    .method-content {
      gap: 10px;
    }

    .method-icon {
      width: 36px;
      height: 36px;

      .icon-img {
        width: 28px;
        height: 28px;
      }
    }

    .method-info {
      .method-name {
        font-size: 14px;
      }

      .method-description {
        font-size: 12px;
      }
    }

    .payment-amount {
      margin-top: 10px;
      padding-top: 10px;

      .amount-info {
        .amount-label {
          font-size: 13px;
        }

        .amount-value {
          font-size: 15px;
        }
      }
    }

    .payment-notice {
      margin-top: 12px;
      padding: 10px;

      .notice-content {
        gap: 6px;

        .notice-icon {
          font-size: 14px;
        }

        .notice-text p {
          font-size: 12px;
        }
      }
    }
  }
</style>
