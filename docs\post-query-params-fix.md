# POST请求Query参数问题修复

## 问题描述

在调用后台创建存证接口时，发现发送的数据（data）是null，这是因为：

1. 后端接口期望使用 query 参数而不是请求体
2. 前端调用方式：`proHttp.post('/app-api/bisness/certificate-info/create', null, { params })`
3. 但是原有的 `HttpService.post` 方法不支持第三个参数（config）

## 根本原因

原有的 `post` 方法签名：
```typescript
post(url: string, params?: any) {
  // 只支持两个参数，第二个参数作为请求体
}
```

但我们需要的调用方式：
```typescript
post(url: string, data: null, config: { params: object }) {
  // 需要支持第三个参数作为配置，包含 query 参数
}
```

## 修复方案

### 1. 更新 HttpService.post 方法

**修改前**:
```typescript
post(url: string, params?: any) {
  return new Promise((resolve, reject) => {
    this.instance
      .post(url, JSON.stringify(params))
      .then(res => {
        resolve(res.data);
      })
      .catch(err => {
        reject(err.data);
      });
  });
}
```

**修改后**:
```typescript
post(url: string, data?: any, config?: any) {
  return new Promise((resolve, reject) => {
    // 如果 data 不为 null/undefined，则进行 JSON 序列化
    const requestData = data !== null && data !== undefined ? JSON.stringify(data) : data;
    
    this.instance
      .post(url, requestData, config)
      .then(res => {
        resolve(res.data);
      })
      .catch(err => {
        reject(err.data);
      });
  });
}
```

### 2. 修复类型错误

同时修复了相关的 TypeScript 类型错误：
- 移除了不存在的 `InternalAxiosRequestConfig` 导入
- 修复了 `config.headers` 可能为 undefined 的问题

## 修复效果

### 修复前的调用
```typescript
// 这样调用时，{ params } 被忽略，实际发送的是 null 作为请求体
proHttp.post('/app-api/bisness/certificate-info/create', null, { params });
```

### 修复后的调用
```typescript
// 现在正确支持第三个参数，params 会作为 query 参数发送
proHttp.post('/app-api/bisness/certificate-info/create', null, { params });
```

## 网络请求验证

修复后，网络请求应该显示：

### 请求方式
- Method: `POST`
- URL: `/app-api/bisness/certificate-info/create?dataNumber=xxx&dataName=xxx&...`
- Request Body: `null` 或空

### 请求参数
所有参数都应该出现在 URL 的 Query String 中：
```
?dataNumber=DATA_20241216_001
&dataName=测试数据
&certificationUnit=测试单位
&dataClassification=分类1
&dataLevel=级别1
&dataGenerationTime=2024-12-16T10:00:00.000Z
&dataResourceOwnership=权属1,权属2
&dataSampleFile=file1.pdf
&dataSourceEvidenceMaterial=evidence1.pdf
&effectiveControlEvidenceMaterial=control1.pdf
&personalInfoCollectionEvidenceMaterial=personal1.pdf
&statusCd=PENDING
```

## 向后兼容性

这个修改保持了向后兼容性：

### 原有调用方式仍然有效
```typescript
// 两个参数的调用方式仍然工作
proHttp.post('/some-api', { data: 'value' });
```

### 新的调用方式
```typescript
// 三个参数的调用方式现在也支持
proHttp.post('/some-api', { data: 'value' }, { params: { query: 'param' } });
proHttp.post('/some-api', null, { params: { query: 'param' } });
```

## 测试验证

### 1. 功能测试
- [ ] 创建存证接口调用成功
- [ ] 参数正确传递到后端
- [ ] 请求体为 null 或空
- [ ] Query 参数包含所有字段

### 2. 兼容性测试
- [ ] 其他使用 proHttp.post 的接口仍然正常工作
- [ ] 两个参数的调用方式正常
- [ ] 三个参数的调用方式正常

### 3. 网络请求检查
打开浏览器开发者工具 → Network 标签：
- [ ] 请求方法为 POST
- [ ] URL 包含 query 参数
- [ ] Request Body 为空或 null
- [ ] 响应正常返回

## 调试方法

### 1. 控制台检查
在提交表单时，查看控制台输出：
```
=== 开始收集文件路径 ===
=== 文件路径收集结果 ===
提交存证数据: { dataNumber: "...", dataName: "...", ... }
```

### 2. 网络请求检查
在 Network 标签中找到创建存证的请求：
- 请求 URL 应该包含所有参数
- Request Body 应该为空
- Response 应该返回成功结果

### 3. 手动测试
可以在控制台执行：
```javascript
// 测试修复后的 post 方法
window.debugFileStatus(); // 检查文件状态
```

## 相关文件

- `src/http/axios.ts` - 修复了 HttpService.post 方法
- `src/http/api/data/index.ts` - 创建存证接口调用
- `src/view/data/eviApply.vue` - 存证申请页面

## 注意事项

1. **URL长度限制**: 虽然使用 query 参数，但通常不会超过 URL 长度限制
2. **参数编码**: axios 会自动处理参数的 URL 编码
3. **特殊字符**: 确保特殊字符在 query 参数中正确编码
4. **调试信息**: 保留了详细的调试日志便于问题排查

## 总结

这个修复解决了 POST 请求无法正确传递 query 参数的问题，使得创建存证接口能够正确接收所有参数。修改保持了向后兼容性，不会影响其他接口的正常使用。
