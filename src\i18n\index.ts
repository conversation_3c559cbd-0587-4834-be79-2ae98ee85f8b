import { createI18n } from 'vue-i18n';
import zhCN from './locales/zh-CN';
import enUS from './locales/en-US';

// 获取浏览器语言
function getBrowserLanguage() {
  const browserLang = navigator.language;
  // 只支持简体中文和英文
  if (browserLang.includes('zh')) {
    return 'zh-CN';
  }
  return 'en-US';
}

// 定义支持的语言类型
type SupportedLocale = 'zh-CN' | 'en-US';

// 创建国际化实例
const i18n = createI18n({
  legacy: false, // 使用Composition API
  locale: (localStorage.getItem('locale') as SupportedLocale) || getBrowserLanguage(),
  fallbackLocale: 'zh-CN', // 回退语言
  messages: {
    'zh-CN': zhCN,
    'en-US': enUS,
  },
});

// 设置语言
export function setLanguage(locale: SupportedLocale) {
  if (i18n.global.locale.value !== locale) {
    // 使用类型断言来解决类型错误
    i18n.global.locale.value = locale as any;
    localStorage.setItem('locale', locale);
    // 更新页面标题和元信息
    updateMetaInfo(locale);
  }
}

// 根据当前语言更新页面元信息
function updateMetaInfo(locale: SupportedLocale) {
  // 此处可以根据语言切换更新页面元信息
  if (locale === 'en-US') {
    // 英文元信息更新逻辑
  } else {
    // 中文元信息更新逻辑
  }
}

export default i18n;
