/** @format */

import http from '@/http/proHttp';

// 作品登记信息相关接口
// 创建作品登记信息
export function createWorksRegistrationInfo(params: {
  id?: string;
  worksType?: string;
  worksTitle?: string;
  worksCategory?: string;
  creationDate?: string;
  publicationDate?: string;
  authorName?: string;
  authorNationality?: string;
  copyrightOwner?: string;
  worksDescription?: string;
  creationNature?: string;
  worksForm?: string;
  registrantEmail?: string;
  statusCd?: string;
  remark?: string;
}) {
  return http.post('/admin-api/works/registration-info/create', params);
}

// 更新作品登记信息
export function updateWorksRegistrationInfo(params: {
  id?: string;
  worksType?: string;
  worksTitle?: string;
  worksCategory?: string;
  creationDate?: string;
  publicationDate?: string;
  authorName?: string;
  authorNationality?: string;
  copyrightOwner?: string;
  worksDescription?: string;
  creationNature?: string;
  worksForm?: string;
  registrantEmail?: string;
  statusCd?: string;
  remark?: string;
}) {
  return http.put('/admin-api/works/registration-info/update', params);
}

// 获取作品登记信息分页
export function getWorksRegistrationInfoPage(params: {
  worksType?: string;
  worksTitle?: string;
  worksCategory?: string;
  creationDate?: string;
  publicationDate?: string;
  authorName?: string;
  authorNationality?: string;
  copyrightOwner?: string;
  worksDescription?: string;
  creationNature?: string;
  worksForm?: string;
  registrantEmail?: string;
  statusCd?: string;
  remark?: string;
  creator?: string;
  createTime?: string;
  pageNo: string;
  pageSize: string;
}) {
  return http.get('/admin-api/works/registration-info/page', { params });
}

// 获取作品登记信息详情
export function getWorksRegistrationInfo(id: number) {
  return http.get('/admin-api/works/registration-info/get', { params: { id } });
}

// 导出作品登记信息Excel
export function exportWorksRegistrationInfoExcel(params: {
  worksType?: string;
  worksTitle?: string;
  worksCategory?: string;
  creationDate?: string;
  publicationDate?: string;
  authorName?: string;
  authorNationality?: string;
  copyrightOwner?: string;
  worksDescription?: string;
  creationNature?: string;
  worksForm?: string;
  registrantEmail?: string;
  statusCd?: string;
  remark?: string;
  creator?: string;
  createTime?: string;
}) {
  const queryString = new URLSearchParams(params as any).toString();
  const url = `/admin-api/works/registration-info/export-excel${queryString ? `?${queryString}` : ''}`;
  return http.download(url);
}

// 删除作品登记信息
export function deleteWorksRegistrationInfo(id: number) {
  return http.delete('/admin-api/works/registration-info/delete', { params: { id } });
}

// 获取作品著作权公示列表
export function getWorksCopyrightList(params: { page: number; size: number }) {
  // 请替换为实际的API端点
  return http.get('/api/works/copyright/list', { params });
}

// 获取作品著作权政策信息
export function getWorksCopyrightPolicies() {
  // 请替换为实际的API端点
  return http.get('/api/works/copyright/policies');
}

// 获取作品著作权常见问题
export function getWorksCopyrightFAQs() {
  // 请替换为实际的API端点
  return http.get('/api/works/copyright/faqs');
}

// 作品存证相关接口
export function createWorksEvidence(params: {
  worksTitle: string;
  worksType: string;
  authorName: string;
  worksDescription: string;
  evidenceFiles: string[];
}) {
  return http.post('/api/works/evidence/create', params);
}

// 获取作品存证列表
export function getWorksEvidenceList(params: { page: number; size: number }) {
  return http.get('/api/works/evidence/list', { params });
}

// 获取作品存证详情
export function getWorksEvidenceDetail(id: string) {
  return http.get(`/api/works/evidence/detail/${id}`);
}
