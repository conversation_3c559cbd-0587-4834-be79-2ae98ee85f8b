# 收银台页面改造说明

## 改造概述

按照新的业务需求，对收银台页面进行了重新设计和改造，采用垂直布局，将支付流程分为四个清晰的步骤：

1. **服务类型选择** - 页面顶部
2. **优惠券选择** - 页面中上部
3. **支付方式选择** - 页面中下部  
4. **支付确认** - 页面底部

## 新增组件

### 1. ServiceTypeSelector.vue
- **位置**: `src/view/payment/components/ServiceTypeSelector.vue`
- **功能**: 服务类型选择组件
- **特性**:
  - 网格布局展示各种服务类型
  - 支持服务类型启用/禁用状态
  - 显示服务起步价格
  - 响应式设计，移动端自适应

**支持的服务类型**:
- 数据存证
- 软件著作权
- 版权登记
- 区块链存证
- API服务

### 2. CouponSelector.vue
- **位置**: `src/view/payment/components/CouponSelector.vue`
- **功能**: 优惠券选择组件
- **特性**:
  - 显示已选择的优惠券
  - 可用优惠券列表展示
  - 支持立减券和折扣券
  - 优惠券适用条件检查
  - 支持展开/收起更多优惠券

### 3. PaymentMethodSelector.vue
- **位置**: `src/view/payment/components/PaymentMethodSelector.vue`
- **功能**: 简化版支付方式选择组件
- **特性**:
  - 在线支付方式（微信、支付宝）
  - 账户支付方式（钱包余额、积分支付）
  - 支付金额输入和自动计算
  - 响应式布局

## 页面布局改造

### 原布局
```
┌─────────────────┬─────────────┐
│                 │             │
│   订单信息      │  费用明细   │
│                 │             │
│   支付方式      │  支付确认   │
│                 │             │
└─────────────────┴─────────────┘
```

### 新布局
```
┌─────────────────┬─────────────┐
│                 │             │
│  1.服务类型选择  │  订单信息   │
│                 │             │
│  2.优惠券选择    │  费用明细   │
│                 │             │
│  3.支付方式选择  │             │
│                 │             │
│  4.支付确认      │             │
│                 │             │
└─────────────────┴─────────────┘
```

## 主要文件修改

### 1. Cashier.vue
- 重新组织页面布局结构
- 更新组件导入和使用
- 添加新的事件处理方法
- 更新CSS样式支持垂直布局

### 2. payment store (src/store/modules/payment.ts)
- 添加 `removeCoupon` 方法
- 支持优惠券的移除操作

### 3. 路由配置 (src/router/index.ts)
- 添加演示页面路由 `/payment/demo`

## 演示页面

创建了 `CashierDemo.vue` 演示页面，可以通过以下URL访问：
```
http://localhost:5173/#/payment/demo
```

演示页面包含：
- 模拟的服务类型数据
- 模拟的优惠券数据
- 模拟的用户资产数据
- 完整的交互流程演示

## 技术特性

### 响应式设计
- 桌面端：网格布局，多列展示
- 移动端：单列布局，垂直堆叠

### 用户体验优化
- 清晰的步骤指引
- 实时的金额计算
- 友好的错误提示
- 流畅的交互动画

### 组件化设计
- 高度模块化的组件结构
- 可复用的业务组件
- 清晰的数据流和事件传递

## 使用方法

### 1. 基本使用
```vue
<template>
  <div>
    <!-- 服务类型选择 -->
    <ServiceTypeSelector
      :loading="loading"
      :selected-type="selectedServiceType"
      @select="handleServiceTypeSelect"
      @change="handleServiceTypeChange"
    />

    <!-- 优惠券选择 -->
    <CouponSelector
      :available-coupons="availableCoupons"
      :selected-coupons="selectedCoupons"
      :order-amount="orderAmount"
      @select="handleCouponSelect"
      @remove="handleCouponRemove"
    />

    <!-- 支付方式选择 -->
    <PaymentMethodSelector
      :payment-methods="paymentMethods"
      :user-assets="userAssets"
      :order-amount="finalAmount"
      @payment-change="handlePaymentMethodChange"
    />

    <!-- 支付确认 -->
    <PaymentConfirmation
      :final-amount="finalAmount"
      :savings="savings"
      @submit="handleSubmitPayment"
    />
  </div>
</template>
```

### 2. 事件处理
```typescript
// 服务类型选择
function handleServiceTypeSelect(serviceType: ServiceType) {
  console.log('选择服务类型:', serviceType);
}

// 优惠券选择
function handleCouponSelect(coupon: CouponInfo) {
  // 添加或移除优惠券
}

// 支付方式变化
function handlePaymentMethodChange(methods: PaymentMethod[]) {
  // 处理支付方式变化
}
```

## 兼容性

- Vue 3.x
- TypeScript 4.x
- Arco Design Vue 2.x
- 现代浏览器 (Chrome 88+, Firefox 85+, Safari 14+)

## 后续优化建议

1. **数据持久化**: 添加本地存储支持，保存用户选择状态
2. **性能优化**: 实现虚拟滚动，优化大量优惠券的渲染性能
3. **无障碍访问**: 添加ARIA标签，提升可访问性
4. **国际化**: 支持多语言切换
5. **主题定制**: 支持自定义主题色彩
