/** @format */

import proHttp from '@/http/proHttp';

// ==================== 通用类型定义 ====================

/**
 * 分页查询基础参数
 */
export interface PageParam {
  /** 页码，从1开始 */
  pageNo?: number;
  /** 每页条数，默认10 */
  pageSize?: number;
}

/**
 * 通用API响应结构
 */
export interface ApiResponse<T = any> {
  /** 响应码，0表示成功 */
  code: number;
  /** 响应数据 */
  data: T;
  /** 响应消息 */
  msg: string;
}

/**
 * 文件预签名地址响应数据
 */
export interface FilePresignedUrlRespVO {
  /** 文件配置编号 */
  configId: number;
  /** 文件上传 URL */
  uploadUrl: string;
  /** 文件访问 URL */
  url: string;
}

/**
 * 文件创建请求参数
 */
export interface FileCreateReqVO {
  /** 文件路径 */
  path: string;
  /** 文件名称 */
  name?: string;
  /** 文件大小 */
  size?: number;
  /** 文件类型 */
  type?: string;
}

// ==================== 文件上传相关接口 ====================

/**
 * 获取文件预签名地址
 * @param path 文件路径
 */
export const getFilePresignedUrl = (path: string) => {
  return proHttp.get('/infra/file/presigned-url', { params: { path } }) as Promise<
    ApiResponse<FilePresignedUrlRespVO>
  >;
};

/**
 * 创建文件记录
 * @param data 文件创建参数
 */
export const createFile = (data: FileCreateReqVO) => {
  return proHttp.post('/infra/file/create', data) as Promise<ApiResponse<number>>;
};

/**
 * 上传文件（后端代理模式）
 * @param file 文件对象
 */
export const uploadFile = (file: File) => {
  return proHttp.upload('/app-api/infra/file/upload', file) as Promise<ApiResponse<string>>;
};

// 用户App端上传文件（兼容旧版本）
export function uploadAppFile(params: { path?: string }) {
  return proHttp.post('/app-api/infra/file/upload', params);
}

// 管理后台上传文件（兼容旧版本）
export function uploadAdminFile(params: { path?: string }) {
  return proHttp.post('/admin-api/infra/file/upload', params);
}

// ==================== 字典数据相关接口 ====================

/**
 * 根据字典类型查询字典数据列表
 * @param type 字典类型
 */
export const getDictDataListByType = (type: string) => {
  return proHttp.get('/app-api/system/dict-data/type', { params: { type } }) as Promise<
    ApiResponse<any[]>
  >;
};

// 管理后台登录接口
export function login(params: {
  username: string;
  password: string;
  socialType?: string;
  socialCode?: string;
  socialState?: string;
  tenantName: string;
  rememberMe: boolean;
  captchaVerification?: string;
}) {
  return proHttp.post('/admin-api/system/auth/login', params);
}
