import { describe, it, expect, vi, beforeEach } from 'vitest';
import { getCertificateInfoClient, getCertificateInfoPageClient } from './index';
import proHttp from '@/http/proHttp';

// Mock proHttp
vi.mock('@/http/proHttp', () => ({
  default: {
    get: vi.fn(),
  },
}));

describe('Certificate Info API', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('getCertificateInfoClient', () => {
    it('should call the correct endpoint with id parameter', async () => {
      const mockResponse = {
        code: 0,
        data: {
          id: 1,
          dataNumber: 'TEST001',
          dataName: '测试数据',
          certificationUnit: '测试单位',
          createTime: '2025-01-01T00:00:00Z',
        },
        msg: 'success',
      };

      (proHttp.get as any).mockResolvedValue(mockResponse);

      const result = await getCertificateInfoClient(1);

      expect(proHttp.get).toHaveBeenCalledWith('/app-api/business/certificate-info/get', {
        params: { id: 1 },
      });
      expect(result).toEqual(mockResponse);
    });

    it('should handle API errors', async () => {
      const mockError = new Error('Network error');
      (proHttp.get as any).mockRejectedValue(mockError);

      await expect(getCertificateInfoClient(1)).rejects.toThrow('Network error');
    });
  });

  describe('getCertificateInfoPageClient', () => {
    it('should call the correct endpoint with pagination parameters', async () => {
      const mockResponse = {
        code: 0,
        data: {
          list: [
            {
              id: 1,
              dataNumber: 'TEST001',
              dataName: '测试数据1',
              createTime: '2025-01-01T00:00:00Z',
            },
            {
              id: 2,
              dataNumber: 'TEST002',
              dataName: '测试数据2',
              createTime: '2025-01-01T00:00:00Z',
            },
          ],
          total: 2,
        },
        msg: 'success',
      };

      (proHttp.get as any).mockResolvedValue(mockResponse);

      const params = {
        pageNo: '1',
        pageSize: '10',
        dataName: '测试',
      };

      const result = await getCertificateInfoPageClient(params);

      expect(proHttp.get).toHaveBeenCalledWith('/app-api/business/certificate-info/page', {
        params,
      });
      expect(result).toEqual(mockResponse);
    });

    it('should handle empty search parameters', async () => {
      const mockResponse = {
        code: 0,
        data: {
          list: [],
          total: 0,
        },
        msg: 'success',
      };

      (proHttp.get as any).mockResolvedValue(mockResponse);

      const params = {
        pageNo: '1',
        pageSize: '10',
      };

      const result = await getCertificateInfoPageClient(params);

      expect(proHttp.get).toHaveBeenCalledWith('/app-api/business/certificate-info/page', {
        params,
      });
      expect(result).toEqual(mockResponse);
    });
  });
});
