<template>
  <div class="pb-10">
    <div style="position: relative">
      <img
        class="banner-img"
        src="https://lf3-starry.byteimg.com/obj/starry/image/2sm15kt3yrf_shejiao.jpg"
        alt=""
        srcset=""
      />
      <div style="position: absolute; left: 19%; top: 34%">
        <div class="text-gray-700 text-5xl font-bold">产品大全</div>
        <div class="text-gray-700 text-2xl font-bold mt-4 mb-6">文化数据服务一站式解决方案</div>
      </div>
    </div>
    <div class="module" style="margin-top: -40px">
      <a-tabs default-active-key="0">
        <a-tab-pane key="0" title="全部">
          <div class="py-2 flex">
            <a-space wrap align="start">
              <a-card
                v-for="item in allProductList"
                :key="item.id"
                :style="{ width: '360px', height: '180px' }"
                :title="item.productName"
                hoverable
                @click="handleLink(item)"
              >
                <template #extra>
                  <a-link>了解更多</a-link>
                </template>
                {{ item.introduce }}
              </a-card>
            </a-space>
          </div>
        </a-tab-pane>
        <a-tab-pane v-for="(item, index) in productList" :key="index + 1" :title="item.productName">
          <div class="py-2 flex">
            <a-space wrap align="start">
              <a-card
                v-for="ele in item.childProductList"
                :key="ele.id"
                :style="{ width: '360px' }"
                :title="ele.productName"
                hoverable
                @click="handleLink(ele)"
              >
                <template #extra>
                  <a-link>了解更多</a-link>
                </template>
                {{ ele.introduce }}
              </a-card>
            </a-space>
          </div>
        </a-tab-pane>
      </a-tabs>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { ref, onMounted } from 'vue';
  import { getAllProductList, getProductList } from '@/http/api/global';
  import { useRouter } from 'vue-router';
  import { Product } from '@/http/api/global/interface';

  // 定义产品接口，包含在视图中使用的字段
  interface ProductItem extends Product {
    id: string | number; // 确保id属性存在
    productName: string;
    introduce?: string;
    upId?: string | number;
    childProductList?: ProductItem[];
  }

  const router = useRouter();

  const productList = ref<ProductItem[]>([]);
  const getProductListFn = function () {
    getProductList().then(res => {
      // 根据返回类型不同做处理
      if (res && 'list' in res && Array.isArray(res.list)) {
        // 如果返回的是 { list: Product[] } 格式
        productList.value = res.list as unknown as ProductItem[];
      } else if (Array.isArray(res)) {
        // 如果直接返回的是数组
        productList.value = res as unknown as ProductItem[];
      }
    });
  };

  const allProductList = ref<ProductItem[]>([]);
  const getAllProductListFn = function () {
    getAllProductList().then(res => {
      if (res && 'list' in res && Array.isArray(res.list)) {
        allProductList.value = res.list as unknown as ProductItem[];
      }
    });
  };

  getProductListFn();
  getAllProductListFn();

  const handleLink = (item: ProductItem) => {
    router.push('/products/detail?id=' + item.id + '&upId=' + (item.upId || ''));
  };

  onMounted(() => {
    // 初始化逻辑如有需要
  });
</script>
<style lang="scss" scoped>
  .module {
    width: 1200px;
    margin: auto;
    background-color: white;
    margin-top: -40px;
    z-index: 1;
    position: relative;
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0px 0px 3px rgba(26, 26, 26, 0.2);
  }

  .banner-img {
    height: 480px;
    width: 100vw;
  }
</style>
