# 开发环境变量
NODE_ENV='development'
VITE_BASE_URL='./'

# 新版软著后台API地址 - 开发环境
VITE_PRO_API_TARGET='http://localhost:48080'
# VITE_PRO_API_TARGET='https://software-reg.bmark.cn/api'
# VITE_PRO_API_TARGET='http://**************:49018'
# VITE_PRO_API_TARGET='https://manage.bs-cde.cn/api'

# 原版SOP后台API地址 - 开发环境
VITE_SOP_API_TARGET='https://blockchain.bs-cde.cn/api'
#VITE_SOP_API_TARGET='http://*************:41390/api'
#VITE_SOP_API_TARGET='http://************:8083'

# 是否移除代码中 console 语句
VITE_DROP_CONSOLE=false

# 文件上传模式：client（前端直连）或 server（后端代理）
VITE_UPLOAD_TYPE=server
