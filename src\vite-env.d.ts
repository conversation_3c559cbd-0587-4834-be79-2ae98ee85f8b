/// <reference types="vite/client" />
/// <reference types="vitest/globals" />
/// <reference types="vite-plugin-pwa/client" />

// 声明Vue文件的类型
declare module '*.vue' {
  import type { DefineComponent } from 'vue';
  // eslint-disable-next-line @typescript-eslint/no-explicit-any, @typescript-eslint/ban-types
  const component: DefineComponent<{}, {}, any>;
  export default component;
}

// 声明图片资源模块
declare module '*.png';
declare module '*.jpg';
declare module '*.jpeg';
declare module '*.svg';
declare module '*.gif';

// 声明JSON模块
declare module '*.json' {
  const value: any;
  export default value;
}

// 环境变量类型
interface ImportMetaEnv {
  readonly VITE_API_TARGET: string;
  readonly VITE_BASE_URL: string;
  readonly VITE_DROP_CONSOLE: string;
}

interface ImportMeta {
  readonly env: ImportMetaEnv;
}
