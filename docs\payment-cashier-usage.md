# 独立收银台使用文档

## 概述

文创链独立收银台是一个功能完整的支付解决方案，支持多种支付方式，包括在线支付（微信、支付宝）、积分支付、优惠券支付和余额支付。收银台采用响应式设计，完美适配PC端和移动端。

## 功能特性

### 🎯 核心功能
- **多种支付方式**: 支持微信支付、支付宝、积分、优惠券、余额等多种支付方式
- **组合支付**: 支持多种支付方式组合使用，灵活满足用户需求
- **实时计算**: 支付金额实时计算，优惠信息透明展示
- **安全支付**: 采用银行级SSL加密，确保支付安全
- **响应式设计**: 完美适配PC端、平板和移动端

### 📱 移动端优化
- **触摸优化**: 按钮和交互元素针对触摸操作优化
- **安全区域适配**: 支持iPhone X等设备的安全区域
- **键盘适配**: 智能处理移动端键盘弹起
- **横屏支持**: 支持横屏模式下的良好展示

### 🔒 安全特性
- **支付验证**: 多重支付信息验证
- **防重复提交**: 防止用户重复提交支付
- **状态轮询**: 实时查询支付状态
- **错误处理**: 完善的错误处理和用户提示

## 快速开始

### 1. 路由跳转

```typescript
// 跳转到收银台
router.push({
  name: 'Cashier',
  query: {
    orderId: 'ORDER_123456',    // 订单ID（必需）
    type: 'package',            // 订单类型（可选）
    amount: '99.00'             // 订单金额（可选）
  }
})
```

### 2. 支持的订单类型

- `package`: 套餐购买
- `evidence`: 数据存证
- `software`: 软件著作权
- `api`: API服务

### 3. 页面流程

1. **订单信息展示**: 显示订单详情、产品信息、金额等
2. **支付方式选择**: 用户选择合适的支付方式
3. **金额计算**: 实时计算优惠和最终支付金额
4. **支付确认**: 用户确认支付信息并提交
5. **支付处理**: 处理支付请求，显示支付状态
6. **结果展示**: 跳转到支付结果页面

## API接口说明

### 订单相关接口

```typescript
// 获取订单信息
GET /payment/order/{orderId}

// 响应示例
{
  "code": 200,
  "success": true,
  "data": {
    "orderId": "ORDER_123456",
    "orderType": "package",
    "productName": "专业版套餐",
    "productDescription": "包含1000次存证服务",
    "originalAmount": 99.00,
    "discountAmount": 0.00,
    "finalAmount": 99.00,
    "createTime": "2024-01-01T10:00:00Z",
    "expireTime": "2024-01-01T11:00:00Z",
    "status": "created"
  }
}
```

### 支付方式接口

```typescript
// 获取可用支付方式
GET /payment/methods?orderId={orderId}&amount={amount}

// 响应示例
{
  "code": 200,
  "success": true,
  "data": {
    "wechatEnabled": true,
    "alipayEnabled": true,
    "userAssets": {
      "balance": 50.00,
      "points": 1000,
      "pointsRate": 100,
      "availableCoupons": [...]
    },
    "paymentMethods": [...]
  }
}
```

### 支付创建接口

```typescript
// 创建支付订单
POST /payment/create

// 请求参数
{
  "orderId": "ORDER_123456",
  "paymentMethods": [
    {
      "type": "wechat",
      "amount": 49.00
    },
    {
      "type": "balance",
      "amount": 50.00
    }
  ],
  "totalAmount": 99.00
}

// 响应示例
{
  "code": 200,
  "success": true,
  "data": {
    "success": true,
    "paymentId": "PAY_123456",
    "orderId": "ORDER_123456",
    "qrCode": "https://...",  // 二维码支付链接
    "redirectUrl": "https://...",  // 跳转支付链接
    "paymentStatus": "processing"
  }
}
```

## 组件使用说明

### 主收银台组件 (Cashier.vue)

```vue
<template>
  <Cashier />
</template>

<script setup>
import Cashier from '@/view/payment/Cashier.vue'
</script>
```

### 订单信息组件 (OrderInfo.vue)

```vue
<template>
  <OrderInfo
    :order-info="orderInfo"
    :loading="loading"
    :error="error"
    @retry="handleRetry"
  />
</template>
```

**Props:**
- `orderInfo`: 订单信息对象
- `loading`: 加载状态
- `error`: 错误信息

**Events:**
- `retry`: 重试加载事件

### 支付方式组件 (PaymentMethods.vue)

```vue
<template>
  <PaymentMethods
    :loading="loading"
    @payment-change="handlePaymentChange"
    @amount-change="handleAmountChange"
  />
</template>
```

**Props:**
- `loading`: 加载状态

**Events:**
- `payment-change`: 支付方式变化事件
- `amount-change`: 支付金额变化事件

### 支付计算组件 (PaymentCalculation.vue)

```vue
<template>
  <PaymentCalculation
    :calculation="calculation"
    :loading="loading"
    :error="error"
    @recalculate="handleRecalculate"
  />
</template>
```

### 支付确认组件 (PaymentConfirmation.vue)

```vue
<template>
  <PaymentConfirmation
    :final-amount="finalAmount"
    :savings="savings"
    :submitting="submitting"
    @submit="handleSubmit"
    @cancel="handleCancel"
  />
</template>
```

## Hooks使用说明

### usePayment Hook

```typescript
import { usePayment } from '@/view/payment/hooks/usePayment'

const {
  // 状态
  isSubmitting,
  isPaymentProcessing,
  isPaymentSuccess,
  isPaymentFailed,
  
  // 方法
  initializePayment,
  submitPayment,
  cancelPayment,
  retryPayment,
  
  // Store
  paymentStore
} = usePayment()
```

### usePaymentCalculation Hook

```typescript
import { usePaymentCalculation } from '@/view/payment/hooks/usePaymentCalculation'

const {
  // 状态
  pointsToUse,
  balanceToUse,
  
  // 计算属性
  onlinePaymentAmount,
  totalSavings,
  paymentBreakdown,
  
  // 方法
  setPointsUsage,
  setBalanceUsage,
  smartAllocatePayment,
  resetCalculation
} = usePaymentCalculation()
```

### usePaymentValidation Hook

```typescript
import { usePaymentValidation } from '@/view/payment/hooks/usePaymentValidation'

const {
  // 状态
  validationErrors,
  isValidating,
  isPaymentValid,
  
  // 方法
  validatePayment,
  validatePaymentMethods,
  validateCoupons,
  clearValidationErrors
} = usePaymentValidation()
```

### useMobileAdaptation Hook

```typescript
import { useMobileAdaptation } from '@/view/payment/hooks/useMobileAdaptation'

const {
  // 设备检测
  isMobile,
  isTablet,
  isDesktop,
  deviceType,
  
  // 浏览器检测
  isWechat,
  isAlipay,
  isIOS,
  isAndroid,
  
  // 适配方法
  getAdaptiveFontSize,
  getAdaptiveSpacing,
  getAdaptiveButtonHeight
} = useMobileAdaptation()
```

## 状态管理

### Payment Store

```typescript
import { usePaymentStore } from '@/store/modules/payment'

const paymentStore = usePaymentStore()

// 状态
paymentStore.currentOrder          // 当前订单
paymentStore.paymentMethods        // 可用支付方式
paymentStore.selectedMethods       // 已选择的支付方式
paymentStore.paymentCalculation    // 支付计算结果
paymentStore.paymentStatus         // 支付状态

// 方法
await paymentStore.initPayment(orderId)           // 初始化支付
await paymentStore.createPayment()                // 创建支付
await paymentStore.checkPaymentStatus(paymentId) // 查询支付状态
paymentStore.selectPaymentMethod(method, amount) // 选择支付方式
paymentStore.resetPaymentState()                 // 重置状态
```

## 样式定制

### 主题色彩

收银台使用项目统一的主色调系统：

```scss
// 主品牌色
$primary-color: #165dff;
$primary-light: #4080ff;
$primary-dark: #0f58d3;

// 功能色
$success-color: #00b42a;
$warning-color: #ff7d00;
$error-color: #f53f3f;

// 文本色
$text-color: #1d2129;
$text-color-secondary: #4e5969;
$text-color-tertiary: #86909c;
```

### 移动端适配

```scss
// 移动端断点
$mobile-breakpoint: 768px;
$tablet-breakpoint: 1024px;

// 移动端混合器
@mixin mobile-only {
  @media (max-width: #{$mobile-breakpoint - 1px}) {
    @content;
  }
}

// 触摸优化
@mixin touch-optimized {
  min-height: 44px;
  min-width: 44px;
  touch-action: manipulation;
  -webkit-tap-highlight-color: transparent;
}
```

## 错误处理

### 常见错误类型

1. **订单错误**
   - 订单不存在
   - 订单已过期
   - 订单状态异常

2. **支付方式错误**
   - 余额不足
   - 积分不足
   - 优惠券不可用

3. **网络错误**
   - 请求超时
   - 网络连接失败
   - 服务器错误

### 错误处理示例

```typescript
try {
  await paymentStore.createPayment()
} catch (error) {
  if (error.code === 'INSUFFICIENT_BALANCE') {
    Message.error('余额不足，请选择其他支付方式')
  } else if (error.code === 'ORDER_EXPIRED') {
    Message.error('订单已过期，请重新下单')
  } else {
    Message.error(error.message || '支付失败，请重试')
  }
}
```

## 最佳实践

### 1. 支付流程优化

- 提供清晰的支付流程指示
- 实时显示支付金额计算
- 支持多种支付方式组合
- 提供智能支付方式推荐

### 2. 用户体验优化

- 响应式设计适配各种设备
- 加载状态和进度提示
- 友好的错误提示和重试机制
- 支付成功后的明确指引

### 3. 安全性考虑

- 支付信息加密传输
- 防重复提交保护
- 支付状态实时验证
- 异常情况处理

### 4. 性能优化

- 组件懒加载
- 接口请求优化
- 图片资源优化
- 缓存策略应用

## 常见问题

### Q: 如何自定义支付方式？

A: 可以通过修改 `PaymentMethods.vue` 组件和相关的支付方式子组件来添加新的支付方式。

### Q: 如何处理支付超时？

A: 系统会自动进行支付状态轮询，超时后会提示用户查询最新状态或重新支付。

### Q: 移动端支付如何优化？

A: 使用 `useMobileAdaptation` Hook 进行设备检测和适配，确保在移动端有良好的用户体验。

### Q: 如何集成新的第三方支付？

A: 需要在后端添加相应的支付接口，前端添加对应的支付方式组件和处理逻辑。

## 更新日志

### v1.0.0 (2024-01-01)
- 初始版本发布
- 支持微信、支付宝、积分、优惠券、余额支付
- 完整的移动端适配
- 响应式设计实现
