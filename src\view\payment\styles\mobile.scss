/** @format */

// 移动端支付页面样式

// 移动端基础变量
$mobile-header-height: 56px;
$mobile-safe-area-bottom: env(safe-area-inset-bottom, 0px);
$mobile-min-touch-size: 44px;
$mobile-horizontal-padding: 16px;
$mobile-vertical-padding: 12px;

// 移动端断点
$mobile-breakpoint: 768px;
$tablet-breakpoint: 1024px;

// 移动端混合器
@mixin mobile-only {
  @media (max-width: #{$mobile-breakpoint - 1px}) {
    @content;
  }
}

@mixin tablet-only {
  @media (min-width: #{$mobile-breakpoint}) and (max-width: #{$tablet-breakpoint - 1px}) {
    @content;
  }
}

@mixin mobile-and-tablet {
  @media (max-width: #{$tablet-breakpoint - 1px}) {
    @content;
  }
}

// 安全区域适配
@mixin safe-area-padding($property: padding, $direction: bottom) {
  #{$property}-#{$direction}: constant(safe-area-inset-#{$direction});
  #{$property}-#{$direction}: env(safe-area-inset-#{$direction});
}

// 触摸优化
@mixin touch-optimized {
  min-height: $mobile-min-touch-size;
  min-width: $mobile-min-touch-size;
  touch-action: manipulation;
  -webkit-tap-highlight-color: transparent;
}

// 移动端收银台样式
.cashier-container {
  @include mobile-only {
    background-color: #f7f8fa;
    
    // 头部适配
    .cashier-header {
      height: $mobile-header-height;
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      z-index: 1000;
      @include safe-area-padding(padding, top);
      
      .header-content {
        height: 100%;
        padding: 0 $mobile-horizontal-padding;
        
        .header-left,
        .header-right {
          flex: 0 0 auto;
          
          .arco-btn {
            @include touch-optimized;
            padding: 8px 12px;
            font-size: 14px;
          }
        }
        
        .header-center {
          .page-title {
            font-size: 18px;
            font-weight: 600;
          }
          
          .page-subtitle {
            font-size: 12px;
            margin-top: 2px;
          }
        }
      }
    }
    
    // 主内容区域适配
    .cashier-main {
      margin-top: $mobile-header-height;
      padding: $mobile-vertical-padding 0;
      @include safe-area-padding(padding, bottom);
      
      .main-content {
        display: block;
        padding: 0 $mobile-horizontal-padding;
        gap: $mobile-vertical-padding;
      }
    }
    
    // 内容区域适配
    .content-left,
    .content-right {
      width: 100%;
      gap: $mobile-vertical-padding;
    }
    
    .content-right {
      position: static;
      margin-top: $mobile-vertical-padding;
    }
  }
}

// 移动端卡片样式
.payment-card,
.order-card,
.calculation-card,
.confirmation-card {
  @include mobile-only {
    border-radius: 8px;
    margin-bottom: $mobile-vertical-padding;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);
    
    :deep(.arco-card-header) {
      padding: $mobile-vertical-padding $mobile-horizontal-padding;
      border-bottom: 1px solid #f2f3f5;
    }
    
    :deep(.arco-card-body) {
      padding: $mobile-horizontal-padding;
    }
  }
}

// 移动端支付方式样式
.payment-method {
  @include mobile-only {
    border-radius: 8px;
    margin-bottom: 8px;
    
    .method-header {
      padding: $mobile-vertical-padding;
      @include touch-optimized;
    }
    
    .method-content {
      gap: 10px;
      
      .method-icon {
        width: 36px;
        height: 36px;
        
        .icon {
          font-size: 18px;
        }
        
        .icon-img {
          width: 28px;
          height: 28px;
        }
      }
      
      .method-info {
        .method-name {
          font-size: 14px;
          line-height: 1.4;
        }
        
        .method-description {
          font-size: 12px;
          line-height: 1.3;
        }
      }
    }
    
    // 金额输入区域
    .amount-input-section,
    .points-input-section {
      padding: 0 $mobile-vertical-padding $mobile-vertical-padding;
      
      .input-group {
        margin-bottom: 10px;
        
        .input-wrapper {
          gap: 6px;
          
          .amount-input,
          .points-input {
            font-size: 14px;
          }
          
          .max-button {
            padding: 6px 8px;
            font-size: 12px;
            min-width: auto;
          }
        }
      }
      
      .amount-slider,
      .points-slider {
        margin-bottom: 10px;
        
        .slider-labels {
          font-size: 11px;
        }
      }
      
      .balance-info,
      .points-info {
        padding: 10px;
        border-radius: 6px;
        
        .info-row {
          margin-bottom: 4px;
          
          .info-label,
          .info-value {
            font-size: 12px;
          }
        }
      }
      
      .quick-amounts,
      .quick-points {
        flex-direction: column;
        align-items: flex-start;
        gap: 6px;
        
        .quick-label {
          font-size: 12px;
        }
        
        .quick-buttons {
          width: 100%;
          
          .arco-btn {
            height: 32px;
            font-size: 11px;
            padding: 0 8px;
            @include touch-optimized;
          }
        }
      }
    }
  }
}

// 移动端优惠券样式
.coupon-item {
  @include mobile-only {
    border-radius: 8px;
    margin-bottom: 8px;
    
    .coupon-card {
      padding: $mobile-vertical-padding;
      
      .coupon-left {
        width: 60px;
        margin-right: 10px;
        border-radius: 6px;
        
        .value-amount {
          font-size: 14px;
        }
        
        .value-discount {
          font-size: 12px;
        }
        
        .coupon-type {
          font-size: 9px;
        }
      }
      
      .coupon-details {
        .coupon-name {
          font-size: 13px;
          margin-bottom: 4px;
        }
        
        .coupon-description {
          font-size: 11px;
          margin-bottom: 6px;
        }
        
        .coupon-conditions {
          gap: 4px;
          
          .condition-item {
            font-size: 10px;
            padding: 1px 4px;
          }
        }
        
        .coupon-expire {
          font-size: 10px;
        }
      }
      
      .coupon-action {
        margin-left: 8px;
      }
    }
  }
}

// 移动端支付确认样式
.payment-confirmation-container {
  @include mobile-only {
    .confirmation-card {
      border-radius: 8px;
      
      :deep(.arco-card-body) {
        padding: $mobile-horizontal-padding;
      }
    }
    
    .confirmation-content {
      gap: 16px;
    }
    
    .payment-amount-section {
      padding: 16px;
      border-radius: 8px;
      
      .amount-display {
        .amount-label {
          font-size: 13px;
          margin-bottom: 6px;
        }
        
        .amount-value {
          font-size: 28px;
        }
      }
      
      .savings-display {
        margin-top: 6px;
        
        .savings-text {
          font-size: 12px;
        }
      }
    }
    
    .payment-summary {
      padding: $mobile-vertical-padding;
      border-radius: 6px;
      
      .summary-title {
        font-size: 13px;
        margin-bottom: 10px;
      }
      
      .method-summary {
        .method-name,
        .method-amount {
          font-size: 12px;
        }
      }
    }
    
    .payment-agreement {
      .agreement-checkbox {
        font-size: 12px;
        
        :deep(.arco-link) {
          font-size: 12px;
        }
      }
    }
    
    .payment-actions {
      gap: 10px;
      
      .pay-button {
        height: $mobile-min-touch-size;
        font-size: 15px;
        border-radius: 8px;
        @include touch-optimized;
      }
      
      .cancel-button {
        height: 36px;
        font-size: 13px;
        @include touch-optimized;
      }
    }
    
    .security-tips {
      padding: 10px;
      border-radius: 6px;
      
      .tips-content {
        .tips-text p {
          font-size: 11px;
        }
      }
    }
  }
}

// 移动端支付处理遮罩
.payment-processing-overlay {
  @include mobile-only {
    .processing-content {
      width: 90%;
      max-width: 320px;
      padding: 24px;
      border-radius: 12px;
      
      .processing-title {
        font-size: 16px;
        margin: 12px 0 6px 0;
      }
      
      .processing-text {
        font-size: 13px;
        margin-bottom: 20px;
      }
      
      .qr-code-section {
        margin: 20px 0;
        
        .qr-code-container {
          margin-bottom: 12px;
          
          .qr-code {
            width: 160px;
            height: 160px;
            border-radius: 8px;
          }
        }
        
        .qr-code-tip {
          font-size: 13px;
        }
      }
      
      .cancel-processing-button {
        margin-top: 12px;
        @include touch-optimized;
      }
    }
  }
}

// 移动端支付结果页面
.payment-result-container {
  @include mobile-only {
    .result-header {
      padding: 16px 0;
      @include safe-area-padding(padding, top);
      
      .header-content {
        padding: 0 $mobile-horizontal-padding;
        
        .page-title {
          font-size: 18px;
        }
      }
    }
    
    .result-main {
      padding: 20px 0;
      @include safe-area-padding(padding, bottom);
      
      .main-content {
        padding: 0 $mobile-horizontal-padding;
      }
    }
    
    .result-card {
      padding: 20px;
      border-radius: 8px;
      
      .result-icon {
        .success-icon,
        .error-icon,
        .warning-icon,
        .info-icon {
          font-size: 48px;
        }
      }
      
      .payment-details {
        gap: 16px;
        margin-top: 16px;
        
        .section-title {
          font-size: 14px;
          margin-bottom: 12px;
        }
        
        .order-section {
          :deep(.arco-descriptions-item-label) {
            font-size: 12px;
          }
          
          :deep(.arco-descriptions-item-value) {
            font-size: 12px;
          }
          
          .amount-text {
            font-size: 14px;
          }
        }
        
        .action-buttons {
          margin: 20px 0;
          
          :deep(.arco-space) {
            flex-direction: column;
            width: 100%;
            
            .arco-space-item {
              width: 100%;
              
              .arco-btn {
                width: 100%;
                height: $mobile-min-touch-size;
                font-size: 14px;
                @include touch-optimized;
              }
            }
          }
        }
        
        .tips-section {
          .tips-content p {
            font-size: 12px;
          }
        }
      }
    }
  }
}

// 键盘弹起时的样式调整
body.keyboard-open {
  @include mobile-only {
    .cashier-main {
      height: calc(100vh - var(--keyboard-height, 0px) - #{$mobile-header-height});
      overflow-y: auto;
    }
    
    .payment-processing-overlay {
      .processing-content {
        max-height: calc(100vh - var(--keyboard-height, 0px) - 40px);
        overflow-y: auto;
      }
    }
  }
}

// 横屏适配
@media (orientation: landscape) and (max-height: 500px) {
  .cashier-container {
    .cashier-header {
      height: 48px;
      
      .header-content {
        .header-center {
          .page-title {
            font-size: 16px;
          }
          
          .page-subtitle {
            display: none;
          }
        }
      }
    }
    
    .cashier-main {
      margin-top: 48px;
      padding: 8px 0;
    }
    
    .payment-processing-overlay {
      .processing-content {
        padding: 16px;
        max-height: 90vh;
        overflow-y: auto;
        
        .qr-code-section {
          .qr-code {
            width: 120px;
            height: 120px;
          }
        }
      }
    }
  }
}
