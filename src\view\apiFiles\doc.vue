<template>
  <div class="contentPage">
    <div class="apiTree">
      <a-tree
        block-node
        show-line
        :data="treeData"
        :field-names="{
          key: 'id',
          title: 'label',
        }"
        size="medium"
        @select="treeClick"
      />
    </div>
    <div v-show="apiDocType" class="apiDocDiv">
      <a-tabs :active-key="tabsType" @change="tabsCilck">
        <a-tab-pane :key="1" title="接口详情">
          <docView ref="docViewRef" />
        </a-tab-pane>
        <a-tab-pane :key="2" title="调试工具">
          <docDeBug ref="docViewRefNi" />
        </a-tab-pane>
      </a-tabs>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { ref, onBeforeMount } from 'vue';
  import { getDocMenus } from '@/http/api/apiFiles';
  import docView from './components/docInfoCom.vue';
  import docDeBug from './components/docDeBug.vue';
  import { useRouter } from 'vue-router';
  let apiDocData = ref<any>({});
  let tabsType = ref<any>(1);
  let apiDocType = ref<boolean>(false);
  let treeData = ref<any>([]);
  let hisItemS = ref<any>({});
  const docViewRef = ref();
  const docViewRefNi = ref();
  const router = useRouter();

  const apiDocId = ref(String(router.currentRoute.value.query.id)); // 选中的id
  const apiDocType1 = ref(Number(router.currentRoute.value.query.type)); // 选中的id

  const initPage = async () => {
    apiDocData.value = await getDocMenus();
    treeData.value = apiDocData.value.menuProjects;
    console.log(apiDocId.value);
    if (apiDocId.value && apiDocId.value != 'undefined') {
      hisItemS.value = {
        name: apiDocId.value,
        urlProd: '',
      };
      if (apiDocType1.value && apiDocType1.value == 2) {
        // 打开调试工具
        tabsType.value = 2;
        docViewRefNi.value.getApiInfo({
          name: apiDocId.value,
          appId: apiDocData.value.appId,
          privateKey: apiDocData.value.privateKeyIsv,
          gatewayUrl: apiDocData.value.gatewayUrl,
        });
      } else {
        tabsType.value = 1;
        docViewRef.value.getApiInfo(apiDocId.value, apiDocData.value.gatewayUrl);
      }
      apiDocType.value = true;
    }
  };
  const treeClick = async (item, hisItem) => {
    if (hisItem.node.name) {
      tabsType.value = 1;
      hisItemS.value = {
        name: hisItem.node.name,
        urlProd: hisItem.node.urlProd,
      };
      apiDocType.value = true;
      docViewRef.value.getApiInfo(hisItem.node.name, hisItem.node.urlProd);
    }
  };
  const tabsCilck = async a => {
    tabsType.value = a;
    if (tabsType.value == 1) {
      docViewRef.value.getApiInfo(hisItemS.value.name, hisItemS.value.urlProd);
    } else {
      docViewRefNi.value.getApiInfo({
        name: hisItemS.value.name,
        appId: apiDocData.value.appId,
        privateKey: apiDocData.value.privateKeyIsv,
        gatewayUrl: apiDocData.value.gatewayUrl,
      });
    }
  };
  onBeforeMount(async () => {
    initPage();
  });
</script>

<style lang="scss" scoped>
  .apiTree {
    width: 280px;
    padding: 10px;
    border: 1px black solid;
    display: inline-block;
  }

  .apiDocDiv {
    width: 960px;
    padding-left: 40px;
    display: inline-block;
  }

  .contentPage {
    width: 1300px;
    margin: 0 auto;
    display: flex;
    align-items: flex-start;
    min-height: 720px;
    padding-bottom: 200px;
  }
</style>
