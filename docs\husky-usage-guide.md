# Husky 使用指南

## 🚀 快速开始

### 1. 验证配置

```bash
# 测试 Husky 配置
npm run hooks:test

# 验证所有工具链
npm run validate
```

### 2. 日常使用

```bash
# 正常提交（会自动运行检查）
git add .
git commit -m "feat: 添加新功能"

# 推送（会运行完整验证）
git push

# 使用智能提交模板
git commit  # 不加 -m 参数，会自动生成模板
```

## 🪝 Git Hooks 说明

### pre-commit（提交前）
- 📝 代码检查和格式化（lint-staged）
- 🔍 TypeScript 类型检查
- 🧪 单元测试

### commit-msg（提交信息验证）
- ✅ 提交信息格式验证（commitlint）

### pre-push（推送前）
- 🔍 完整 ESLint 检查
- 🧪 完整测试套件
- 🏗️ 构建验证
- 🔒 主分支特殊检查

### prepare-commit-msg（提交模板）
- 📝 智能提交信息模板
- 🏷️ 自动提取分支 issue 号
- 📋 显示变更文件列表

### post-merge（合并后）
- 📦 自动安装依赖变更
- ⚙️ 配置变更提醒
- 🧹 缓存清理

## 📋 提交规范

### 格式
```
<type>(<scope>): <subject>

<body>

<footer>
```

### 提交类型
- `feat`: 新功能
- `fix`: 修复bug
- `docs`: 文档变更
- `style`: 代码格式
- `refactor`: 代码重构
- `test`: 测试相关
- `chore`: 构建工具变更

### 示例
```bash
feat(auth): 添加用户登录功能

实现了基于JWT的用户认证系统

Closes #123
```

## 🛠️ 常用命令

```bash
# 代码检查
npm run lint:check      # 检查不修复
npm run lint            # 检查并修复

# 格式化
npm run format:check    # 检查格式
npm run format          # 格式化代码

# 类型检查
npm run type-check

# 测试
npm run test:unit       # 单元测试
npm run test:coverage   # 覆盖率测试

# 构建
npm run build:dev       # 开发构建
npm run build:prod      # 生产构建

# 完整验证
npm run validate        # 类型检查 + 代码检查 + 测试 + 构建
```

## 🚨 跳过检查（紧急情况）

```bash
# 跳过 pre-commit
git commit --no-verify

# 跳过 pre-push
git push --no-verify
```

**注意**：仅在紧急情况下使用，建议后续补充相关检查。

## 🔧 故障排除

### 1. Hooks 不执行
```bash
npm run hooks:setup
```

### 2. 权限问题
```bash
chmod +x .husky/*
```

### 3. 依赖问题
```bash
npm run clean
npm install
```

### 4. 配置测试
```bash
npm run hooks:test
```

## 💡 最佳实践

1. **小而频繁的提交**：每个提交只做一件事
2. **清晰的提交信息**：描述变更内容和原因
3. **测试先行**：确保测试通过再提交
4. **代码审查**：推送前进行自我审查
5. **分支命名**：使用有意义的分支名称

## 📈 优化效果

- ✅ 自动化代码质量检查
- ✅ 统一的提交规范
- ✅ 智能的提交模板
- ✅ 自动化依赖管理
- ✅ 完整的验证流程

通过这套 Husky 配置，项目的代码质量和开发规范得到了显著提升！
