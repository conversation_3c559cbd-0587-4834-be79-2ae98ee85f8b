/** @format */

import { createAxiosInstance, HttpService, ResType } from './axios';
import { useUserStore } from '@/store/index';
import { Message } from '@arco-design/web-vue';

// 获取环境变量中的PRO API地址
const API_URL = import.meta.env.VITE_PRO_API_TARGET || '/pro';

// 创建PRO API的HTTP实例
const proInstance = createAxiosInstance({
  baseURL: API_URL,
  timeout: 10000,
  tenantId: '163', // PRO API需要设置租户ID
  tokenKey: 'Authorization', // PRO API使用Authorization header
  getToken: () => {
    const token = useUserStore().getToken;
    return token ? `Bearer ${token}` : null; // 只在token不为空时返回Bearer格式
  },
  customHeaders: {
    'Content-Type': 'application/json',
  },
});

// 创建PRO HTTP服务
const proHttpService = new HttpService(proInstance);

// 导出HTTP服务实例
export default proHttpService;
