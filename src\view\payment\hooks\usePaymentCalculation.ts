/** @format */

import { ref, computed, watch } from 'vue';
import { usePaymentStore } from '@/store/modules/payment';
import type { PaymentMethod, CouponInfo } from '@/http/api/payment/types';

/**
 * 支付计算Hook
 */
export function usePaymentCalculation() {
  const paymentStore = usePaymentStore();

  // 本地状态
  const pointsToUse = ref(0);
  const balanceToUse = ref(0);
  const isCalculating = ref(false);

  // 计算属性
  const originalAmount = computed(() => paymentStore.currentOrder?.finalAmount || 0);
  const userBalance = computed(() => paymentStore.userAssets?.balance || 0);
  const userPoints = computed(() => paymentStore.userAssets?.points || 0);
  const pointsRate = computed(() => paymentStore.userAssets?.pointsRate || 100); // 100积分=1元

  // 可用积分价值（元）
  const availablePointsValue = computed(() => {
    return Math.floor(userPoints.value / pointsRate.value * 100) / 100;
  });

  // 当前选择的优惠券总折扣
  const totalCouponDiscount = computed(() => {
    return paymentStore.selectedCoupons.reduce((total, coupon) => {
      if (coupon.type === 'amount') {
        return total + coupon.value;
      } else if (coupon.type === 'discount') {
        const discountAmount = originalAmount.value * (1 - coupon.value);
        return total + Math.min(discountAmount, coupon.maxDiscountAmount || discountAmount);
      }
      return total;
    }, 0);
  });

  // 应用优惠券后的金额
  const amountAfterCoupon = computed(() => {
    return Math.max(0, originalAmount.value - totalCouponDiscount.value);
  });

  // 积分可抵扣的最大金额
  const maxPointsDeduction = computed(() => {
    return Math.min(availablePointsValue.value, amountAfterCoupon.value);
  });

  // 余额可抵扣的最大金额
  const maxBalanceDeduction = computed(() => {
    const remainingAmount = amountAfterCoupon.value - (pointsToUse.value / pointsRate.value);
    return Math.min(userBalance.value, Math.max(0, remainingAmount));
  });

  // 最终需要在线支付的金额
  const onlinePaymentAmount = computed(() => {
    const pointsDeduction = pointsToUse.value / pointsRate.value;
    const totalDeduction = totalCouponDiscount.value + pointsDeduction + balanceToUse.value;
    return Math.max(0, originalAmount.value - totalDeduction);
  });

  // 总节省金额
  const totalSavings = computed(() => {
    return totalCouponDiscount.value + (pointsToUse.value / pointsRate.value) + balanceToUse.value;
  });

  // 支付方式分布
  const paymentBreakdown = computed(() => {
    return {
      original: originalAmount.value,
      couponDiscount: totalCouponDiscount.value,
      pointsUsed: pointsToUse.value,
      pointsValue: pointsToUse.value / pointsRate.value,
      balanceUsed: balanceToUse.value,
      onlinePayment: onlinePaymentAmount.value,
      totalSavings: totalSavings.value
    };
  });

  /**
   * 设置积分使用数量
   */
  function setPointsUsage(points: number) {
    const maxPoints = Math.floor(maxPointsDeduction.value * pointsRate.value);
    pointsToUse.value = Math.min(Math.max(0, points), maxPoints);
    updatePaymentCalculation();
  }

  /**
   * 设置余额使用金额
   */
  function setBalanceUsage(amount: number) {
    balanceToUse.value = Math.min(Math.max(0, amount), maxBalanceDeduction.value);
    updatePaymentCalculation();
  }

  /**
   * 使用全部积分
   */
  function useAllPoints() {
    const maxPoints = Math.floor(maxPointsDeduction.value * pointsRate.value);
    setPointsUsage(maxPoints);
  }

  /**
   * 使用全部余额
   */
  function useAllBalance() {
    setBalanceUsage(maxBalanceDeduction.value);
  }

  /**
   * 清空积分使用
   */
  function clearPointsUsage() {
    pointsToUse.value = 0;
    updatePaymentCalculation();
  }

  /**
   * 清空余额使用
   */
  function clearBalanceUsage() {
    balanceToUse.value = 0;
    updatePaymentCalculation();
  }

  /**
   * 智能分配支付方式
   * 优先使用优惠券 -> 积分 -> 余额 -> 在线支付
   */
  function smartAllocatePayment() {
    // 先清空当前设置
    pointsToUse.value = 0;
    balanceToUse.value = 0;

    let remainingAmount = amountAfterCoupon.value;

    // 1. 尽可能使用积分
    if (remainingAmount > 0 && userPoints.value > 0) {
      const maxPointsValue = Math.min(availablePointsValue.value, remainingAmount);
      pointsToUse.value = Math.floor(maxPointsValue * pointsRate.value);
      remainingAmount -= maxPointsValue;
    }

    // 2. 使用余额
    if (remainingAmount > 0 && userBalance.value > 0) {
      balanceToUse.value = Math.min(userBalance.value, remainingAmount);
      remainingAmount -= balanceToUse.value;
    }

    updatePaymentCalculation();
  }

  /**
   * 验证优惠券是否可用
   */
  function validateCoupon(coupon: CouponInfo): { valid: boolean; reason?: string } {
    // 检查最小订单金额
    if (originalAmount.value < coupon.minOrderAmount) {
      return {
        valid: false,
        reason: `订单金额需满${coupon.minOrderAmount}元`
      };
    }

    // 检查是否过期
    const expireTime = new Date(coupon.expireTime).getTime();
    const currentTime = new Date().getTime();
    
    if (currentTime > expireTime) {
      return {
        valid: false,
        reason: '优惠券已过期'
      };
    }

    return { valid: true };
  }

  /**
   * 计算优惠券折扣金额
   */
  function calculateCouponDiscount(coupon: CouponInfo, orderAmount: number): number {
    if (coupon.type === 'amount') {
      return Math.min(coupon.value, orderAmount);
    } else if (coupon.type === 'discount') {
      const discountAmount = orderAmount * (1 - coupon.value);
      return Math.min(discountAmount, coupon.maxDiscountAmount || discountAmount);
    }
    return 0;
  }

  /**
   * 更新支付计算
   */
  async function updatePaymentCalculation() {
    if (isCalculating.value) return;

    try {
      isCalculating.value = true;

      // 更新store中的选择
      const pointsMethod = paymentStore.paymentMethods.find(m => m.type === 'points');
      const balanceMethod = paymentStore.paymentMethods.find(m => m.type === 'balance');

      if (pointsToUse.value > 0 && pointsMethod) {
        paymentStore.selectPaymentMethod(pointsMethod, pointsToUse.value);
      } else {
        paymentStore.removePaymentMethod('points');
      }

      if (balanceToUse.value > 0 && balanceMethod) {
        paymentStore.selectPaymentMethod(balanceMethod, balanceToUse.value);
      } else {
        paymentStore.removePaymentMethod('balance');
      }

      // 触发store的计算
      await paymentStore.calculatePayment();

    } catch (error) {
      console.error('更新支付计算失败:', error);
    } finally {
      isCalculating.value = false;
    }
  }

  /**
   * 重置计算
   */
  function resetCalculation() {
    pointsToUse.value = 0;
    balanceToUse.value = 0;
    paymentStore.selectedCoupons = [];
    updatePaymentCalculation();
  }

  /**
   * 格式化金额显示
   */
  function formatAmount(amount: number): string {
    return `¥${amount.toFixed(2)}`;
  }

  /**
   * 格式化积分显示
   */
  function formatPoints(points: number): string {
    return `${points}积分`;
  }

  // 监听积分和余额变化，自动更新计算
  watch([pointsToUse, balanceToUse], () => {
    updatePaymentCalculation();
  });

  // 监听优惠券变化
  watch(() => paymentStore.selectedCoupons, () => {
    updatePaymentCalculation();
  }, { deep: true });

  return {
    // 状态
    pointsToUse,
    balanceToUse,
    isCalculating,

    // 计算属性
    originalAmount,
    userBalance,
    userPoints,
    pointsRate,
    availablePointsValue,
    totalCouponDiscount,
    amountAfterCoupon,
    maxPointsDeduction,
    maxBalanceDeduction,
    onlinePaymentAmount,
    totalSavings,
    paymentBreakdown,

    // 方法
    setPointsUsage,
    setBalanceUsage,
    useAllPoints,
    useAllBalance,
    clearPointsUsage,
    clearBalanceUsage,
    smartAllocatePayment,
    validateCoupon,
    calculateCouponDiscount,
    updatePaymentCalculation,
    resetCalculation,
    formatAmount,
    formatPoints
  };
}
