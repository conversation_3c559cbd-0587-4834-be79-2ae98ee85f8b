<template>
  <div class="service-type-selector-container">
    <a-card class="service-type-card" :bordered="false">
      <template #title>
        <div class="service-type-header">
          <icon-menu class="service-type-icon" />
          <span class="service-type-title">选择服务类型</span>
        </div>
      </template>

      <div v-if="loading" class="loading-container">
        <a-spin :size="24" />
        <p class="loading-text">正在加载服务类型...</p>
      </div>

      <div v-else class="service-type-content">
        <div class="service-type-grid">
          <div
            v-for="serviceType in serviceTypes"
            :key="serviceType.type"
            class="service-type-item"
            :class="{
              selected: selectedType === serviceType.type,
              disabled: !serviceType.enabled,
            }"
            @click="handleServiceTypeSelect(serviceType)"
          >
            <div class="service-type-card-inner">
              <div class="service-type-icon-wrapper">
                <component :is="serviceType.icon" class="service-icon" />
              </div>
              <div class="service-type-info">
                <h4 class="service-type-name">{{ serviceType.name }}</h4>
                <p class="service-type-description">{{ serviceType.description }}</p>
                <div class="service-type-price">
                  <span class="price-label">起步价：</span>
                  <span class="price-value">¥{{ formatAmount(serviceType.basePrice) }}</span>
                </div>
              </div>
              <div class="service-type-status">
                <a-tag v-if="serviceType.enabled" color="green" size="small"> 可用 </a-tag>
                <a-tag v-else color="red" size="small"> 暂停服务 </a-tag>
              </div>
            </div>
          </div>
        </div>

        <!-- 服务类型为空提示 -->
        <div v-if="!hasAnyServiceType" class="empty-service-types">
          <a-empty description="暂无可用的服务类型">
            <template #image>
              <icon-menu style="font-size: 64px; color: #c9cdd4" />
            </template>
          </a-empty>
        </div>
      </div>
    </a-card>
  </div>
</template>

<script setup lang="ts">
  import { computed, ref, onMounted } from 'vue';
  import {
    IconMenu,
    IconFile,
    IconCode,
    IconBook,
    IconSafe,
    IconCloud,
  } from '@arco-design/web-vue/es/icon';

  // 服务类型定义
  export interface ServiceType {
    type: string;
    name: string;
    description: string;
    icon: any;
    basePrice: number;
    enabled: boolean;
  }

  // Props
  interface Props {
    loading?: boolean;
    selectedType?: string;
  }

  const props = withDefaults(defineProps<Props>(), {
    loading: false,
    selectedType: '',
  });

  // Emits
  const emit = defineEmits(['select', 'change']);

  // 本地状态
  const serviceTypes = ref<ServiceType[]>([
    {
      type: 'data_evidence',
      name: '数据存证',
      description: '为您的数据提供区块链存证服务，确保数据完整性和时间戳',
      icon: IconSafe,
      basePrice: 1.0,
      enabled: true,
    },
    {
      type: 'software_copyright',
      name: '软件著作权',
      description: '软件著作权登记申请，保护您的软件知识产权',
      icon: IconCode,
      basePrice: 300.0,
      enabled: true,
    },
    {
      type: 'copyright_register',
      name: '版权登记',
      description: '作品版权登记服务，为您的创作提供法律保护',
      icon: IconBook,
      basePrice: 200.0,
      enabled: true,
    },
    {
      type: 'blockchain_evidence',
      name: '区块链存证',
      description: '基于区块链技术的电子数据存证服务',
      icon: IconCloud,
      basePrice: 5.0,
      enabled: true,
    },
    {
      type: 'api_service',
      name: 'API服务',
      description: '提供各类API接口调用服务',
      icon: IconFile,
      basePrice: 0.1,
      enabled: false,
    },
  ]);

  const selectedType = ref(props.selectedType);

  // 计算属性
  const hasAnyServiceType = computed(() => serviceTypes.value.length > 0);

  // 方法
  function handleServiceTypeSelect(serviceType: ServiceType) {
    if (!serviceType.enabled) return;

    if (selectedType.value === serviceType.type) {
      // 如果已选中，则取消选择
      selectedType.value = '';
    } else {
      selectedType.value = serviceType.type;
    }

    emit('select', serviceType);
    emit('change', selectedType.value);
  }

  function formatAmount(amount: number): string {
    return amount.toFixed(2);
  }

  // 生命周期
  onMounted(() => {
    // 这里可以添加从API获取服务类型的逻辑
    console.log('ServiceTypeSelector mounted');
  });
</script>

<style scoped lang="less">
  .service-type-selector-container {
    margin-bottom: 24px;

    .service-type-card {
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

      :deep(.arco-card-header) {
        border-bottom: 1px solid #f0f0f0;
        padding: 16px 20px;
      }

      :deep(.arco-card-body) {
        padding: 20px;
      }
    }

    .service-type-header {
      display: flex;
      align-items: center;
      gap: 8px;

      .service-type-icon {
        font-size: 18px;
        color: #1890ff;
      }

      .service-type-title {
        font-size: 16px;
        font-weight: 600;
        color: #262626;
      }
    }

    .loading-container {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 40px 20px;

      .loading-text {
        margin-top: 12px;
        color: #8c8c8c;
        font-size: 14px;
      }
    }

    .service-type-content {
      .service-type-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        gap: 16px;
      }

      .service-type-item {
        cursor: pointer;
        transition: all 0.3s ease;
        border-radius: 8px;
        border: 2px solid #f0f0f0;
        background: #fff;

        &:hover:not(.disabled) {
          border-color: #1890ff;
          box-shadow: 0 4px 12px rgba(24, 144, 255, 0.15);
        }

        &.selected {
          border-color: #1890ff;
          background: #f6ffed;
          box-shadow: 0 4px 12px rgba(24, 144, 255, 0.15);
        }

        &.disabled {
          cursor: not-allowed;
          opacity: 0.6;
          background: #fafafa;

          .service-type-card-inner {
            color: #bfbfbf;
          }
        }

        .service-type-card-inner {
          padding: 20px;
          display: flex;
          flex-direction: column;
          height: 100%;
        }

        .service-type-icon-wrapper {
          display: flex;
          justify-content: center;
          margin-bottom: 16px;

          .service-icon {
            font-size: 32px;
            color: #1890ff;
          }
        }

        .service-type-info {
          flex: 1;
          text-align: center;

          .service-type-name {
            font-size: 16px;
            font-weight: 600;
            color: #262626;
            margin: 0 0 8px 0;
          }

          .service-type-description {
            font-size: 14px;
            color: #8c8c8c;
            line-height: 1.5;
            margin: 0 0 12px 0;
          }

          .service-type-price {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 4px;

            .price-label {
              font-size: 14px;
              color: #8c8c8c;
            }

            .price-value {
              font-size: 16px;
              font-weight: 600;
              color: #f5222d;
            }
          }
        }

        .service-type-status {
          display: flex;
          justify-content: center;
          margin-top: 12px;
        }
      }
    }

    .empty-service-types {
      display: flex;
      justify-content: center;
      align-items: center;
      padding: 60px 20px;
    }
  }

  // 响应式设计
  @media (max-width: 768px) {
    .service-type-selector-container {
      .service-type-content {
        .service-type-grid {
          grid-template-columns: 1fr;
        }
      }
    }
  }
</style>
