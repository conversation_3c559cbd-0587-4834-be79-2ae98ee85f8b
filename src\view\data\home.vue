<template>
  <div class="page-container">
    <!-- Hero Banner Section -->
    <div class="hero-section">
      <div class="content-wrapper">
        <h1 class="hero-title">数据知识产权存证服务</h1>
        <p class="hero-description">
          数据知识产权，是指权利主体对于依法依规获取，经过一定规则处理形成的，具有实用价值、智力成果属性及非公开性的数据集合，享有的自主管控、加工使用、经营许可和获得收益等权益。数据知识产权登记服务依托我们的平台进行线上办理。
        </p>
        <div class="hero-buttons">
          <a-button type="primary" size="large" class="hero-button" @click="goToEvidence">
            <span>立即存证</span>
            <icon-arrow-right />
          </a-button>
          <a-button type="outline" size="large" class="hero-button" @click="goToRegister">
            <span>立即登记</span>
            <icon-arrow-right />
          </a-button>
        </div>
      </div>
    </div>

    <!-- Public Announcement Section -->
    <div class="section public-announcement">
      <div class="section-title">
        <h2>数据知识产权存证公示</h2>
      </div>
      <div class="announcement-list">
        <a-card v-for="item in announcementList" :key="item.id" class="announcement-card">
          <template #title>
            <div class="card-title">
              {{ item.title }}
            </div>
          </template>
          <template #extra>
            <a-button type="text" @click="viewDetails(item.id)"> 详情 </a-button>
          </template>
          <div class="card-content">
            <div class="card-item">
              <span class="label">申请编号：</span>
              <span class="value">{{ item.applicationNumber }}</span>
            </div>
            <div class="card-item">
              <span class="label">公示截止日期：</span>
              <span class="value">{{ item.endDate }}</span>
            </div>
            <div class="card-item">
              <span class="label">申请主体：</span>
              <span class="value">{{ item.applicant }}</span>
            </div>
            <div class="card-status">
              <a-tag color="blue">
                {{ item.status }}
              </a-tag>
            </div>
          </div>
        </a-card>
        <div class="view-more">
          <a-button type="text" @click="viewMoreAnnouncements">
            查看更多
            <icon-arrow-right />
          </a-button>
        </div>
      </div>
    </div>

    <!-- Platform Introduction Section -->
    <div class="section platform-intro">
      <div class="section-title">
        <h2>平台介绍</h2>
      </div>
      <div class="intro-content">
        <p>
          文创链数据知识产权登记平台是数据知识产权登记工作的重要工具，负责行政区域内数据知识产权的登记管理及数据知识产权登记平台的建设维护。
        </p>
        <p>
          平台集"数据存证—登记申请—公示公告—签发证书—备案管理"功能于一体，实现登记及时受理、进度随时查询、结果实时反馈、过程安全免费，更好地满足数据创新主体多元化的登记需求。
        </p>
      </div>
    </div>

    <!-- Attestation Process Section -->
    <div class="section attestation-process">
      <div class="section-title">
        <h2>存证流程</h2>
      </div>
      <div class="process-container">
        <a-row :gutter="24">
          <a-col :span="6">
            <div class="process-card">
              <div class="process-icon">
                <icon-check-circle />
                <div class="process-number">01</div>
              </div>
              <div class="process-content">
                <h3>确权存证</h3>
                <p>
                  由用户自主进行数据存证，将规范化后的数据按照标准格式计算哈希值，并将哈希值随数据索引信息上链存证，取得存证凭证。确权存证将作为审查存证、登记存证的依据。
                </p>
                <p class="process-steps">
                  业务流程：整理存证数据、存证数据上链、取得存证凭证等，实际应用中可根据需要增减或合并流程。
                </p>
              </div>
            </div>
          </a-col>
          <a-col :span="6">
            <div class="process-card">
              <div class="process-icon">
                <icon-apps />
                <div class="process-number">02</div>
              </div>
              <div class="process-content">
                <h3>审查存证</h3>
                <p>
                  由审查机构进行，包括数据合规审查存证、安全审查存证等，组织将数据资源审查结果文件进行存证。
                </p>
                <p class="process-steps">
                  业务流程：明确审查使用到的已存证信息或材料、准备合规审查结论存证数据、存证数据上链、取得存证凭证等，实际应用中可根据需要增减或合并流程。
                </p>
              </div>
            </div>
          </a-col>
          <a-col :span="6">
            <div class="process-card">
              <div class="process-icon">
                <icon-save />
                <div class="process-number">03</div>
              </div>
              <div class="process-content">
                <h3>登记存证</h3>
                <p>组织将完成数据资源登记的登记结果及登记文件等进行存证。</p>
                <p class="process-steps">
                  业务流程：明确数据资源登记使用到的已存证信息或材料，准备登记存证数据、存证数据传输、存证数据验证、关联数据资源、取得存证凭证，实际应用中可根据需要增减或合并流程。
                </p>
              </div>
            </div>
          </a-col>
          <a-col :span="6">
            <div class="process-card">
              <div class="process-icon">
                <icon-search />
                <div class="process-number">04</div>
              </div>
              <div class="process-content">
                <h3>查验溯源</h3>
                <p>组织或个人可通过存证平台查验登记存证信息。</p>
                <p class="process-steps">
                  业务流程：根据存证凭证信息进行确权存证信息查验、审查存证信息查验、登记存证信息查验、溯源检索，实际应用中可根据需要增减或合并流程。
                </p>
              </div>
            </div>
          </a-col>
        </a-row>
      </div>
    </div>

    <!-- Application Scenarios Section -->
    <div class="section application-scenarios">
      <div class="section-title">
        <h2>应用场景</h2>
      </div>
      <div class="scenarios-container">
        <a-row :gutter="24">
          <a-col :span="12">
            <div class="scenario-card">
              <div class="scenario-icon">
                <icon-file />
              </div>
              <div class="scenario-content">
                <h3>数据存证</h3>
                <p>
                  用于对企业用户或个人用户在生产生活中产生的数据进行获取并固定、存储，保障数据的产生、收集、存储、传输过程真实，保障数据自产生时起内容完整且未被篡改。
                </p>
                <a-button type="primary" @click="goToEvidence"> 立即使用 </a-button>
              </div>
            </div>
          </a-col>
          <a-col :span="12">
            <div class="scenario-card">
              <div class="scenario-icon">
                <icon-copyright />
              </div>
              <div class="scenario-content">
                <h3>数据知识产权登记</h3>
                <p>
                  数据知识产权登记证书将作为相应数据持有的证明，凭区块链存证保障数据持有权益，明确权属边界，有利于数据流通交易、收益分配和权益保护，赋能资产质押融资。
                </p>
                <a-button type="primary" @click="goToRegister"> 立即使用 </a-button>
              </div>
            </div>
          </a-col>
        </a-row>
      </div>
    </div>

    <!-- Services Management Section -->
    <div class="section services-management">
      <div class="service-container">
        <a-row :gutter="24">
          <a-col :span="8">
            <div class="service-col">
              <h3>存证管理</h3>
              <a-space direction="vertical">
                <a-button type="text" @click="navigateTo('/data/eviApply')"> 存证申请 </a-button>
                <a-button type="text" @click="navigateTo('/data/evidence/query')">
                  存证查询
                </a-button>
              </a-space>
            </div>
          </a-col>
          <a-col :span="8">
            <div class="service-col">
              <h3>登记管理</h3>
              <a-space direction="vertical">
                <a-button type="text" @click="navigateTo('/data/register/apply')">
                  登记申请
                </a-button>
                <a-button type="text" @click="navigateTo('/data/regPublic')"> 登记公示 </a-button>
                <a-button type="text" @click="navigateTo('/data/register/announcement')">
                  登记公告
                </a-button>
              </a-space>
            </div>
          </a-col>
          <a-col :span="8">
            <div class="service-col">
              <h3>关于平台</h3>
              <a-space direction="vertical">
                <a-button type="text" @click="navigateTo('/data/about/intro')"> 平台简介 </a-button>
                <a-button type="text" @click="navigateTo('/data/about/contact')">
                  联系我们
                </a-button>
              </a-space>
            </div>
          </a-col>
        </a-row>
      </div>
    </div>

    <!-- Contact Us Section -->
    <div class="section contact-us">
      <div class="section-title">
        <h2>联系我们</h2>
      </div>
      <div class="contact-content">
        <div class="contact-item">
          <div class="contact-label">存证、实名认证咨询:</div>
          <div class="contact-value">18081067661</div>
        </div>
        <div class="contact-item">
          <div class="contact-label">登记咨询:</div>
          <div class="contact-value">(028) 8605 8625（工作日接听）、18081067661</div>
        </div>
        <div class="contact-item">
          <div class="contact-label">登记审查:</div>
          <div class="contact-value">(028) 8605 8625（工作日接听）</div>
        </div>
        <div class="contact-item">
          <div class="contact-label">技术支持:</div>
          <div class="contact-value">18081067661</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref, onMounted } from 'vue';
  import { useRouter } from 'vue-router';
  import {
    IconArrowRight,
    IconFile,
    IconCopyright,
    IconCheckCircle,
    IconSearch,
    IconSave,
    IconApps,
  } from '@arco-design/web-vue/es/icon';

  const router = useRouter();

  // 示例数据
  const announcementList = ref([
    {
      id: '1',
      title: '有色金属工业-铝冶炼行业排污许可数字链组数据',
      applicationNumber: 'DIPR2025040700118',
      endDate: '2025-04-24',
      applicant: '应辉环境科技服务（烟台）有限公司',
      status: '公示中',
    },
    {
      id: '2',
      title: '牛类养殖周期与财务关联数据集',
      applicationNumber: 'DIPR2025032700077',
      endDate: '2025-04-24',
      applicant: '山东动脉智能科技股份有限公司',
      status: '公示中',
    },
    {
      id: '3',
      title: '铁合金、电解锰工业行业排污许可数字链组数据',
      applicationNumber: 'DIPR2025040700119',
      endDate: '2025-04-24',
      applicant: '应辉环境科技服务（烟台）有限公司',
      status: '公示中',
    },
    {
      id: '4',
      title: '石墨及其他非金属矿物制品制造行业排污许可数字链组数据',
      applicationNumber: 'DIPR2025033000084',
      endDate: '2025-04-24',
      applicant: '应辉环境科技服务（烟台）有限公司',
      status: '公示中',
    },
  ]);

  // 页面跳转方法
  const navigateTo = (path: string) => {
    router.push(path);
  };

  const goToEvidence = () => {
    router.push('/data/eviApply');
  };

  const goToRegister = () => {
    // router.push('/data/register/apply');
    // window.open('https://sddip.com/', '_blank');
    window.open('https://ipr.scipsc.com/home', '_blank');
  };

  const viewDetails = (id: string) => {
    router.push(`/data/evidence/detail/${id}`);
  };

  const viewMoreAnnouncements = () => {
    router.push('/data/regPublic');
  };

  onMounted(() => {
    // 页面加载时可以调用API获取实际数据
    // getAnnouncementList()
  });
</script>

<style lang="scss" scoped>
  .page-container {
    width: 100%;
  }

  .hero-section {
    background-color: #f7f8fa;
    padding: 80px 0;
    background-image:
      linear-gradient(to right, rgba(230, 240, 255, 0.8), rgba(240, 245, 255, 0.8)),
      url('/src/assets/banner-bg.jpg');
    background-size: cover;
    background-position: center;
  }

  .content-wrapper {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    text-align: center;
  }

  .hero-title {
    font-size: 42px;
    font-weight: bold;
    color: #1d2129;
    margin-bottom: 20px;
  }

  .hero-description {
    font-size: 16px;
    color: #4e5969;
    max-width: 800px;
    margin: 0 auto 40px;
    line-height: 1.8;
  }

  .hero-buttons {
    display: flex;
    justify-content: center;
    gap: 20px;
  }

  .hero-button {
    width: 160px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 20px;
  }

  .section {
    padding: 60px 0;
    max-width: 1200px;
    margin: 0 auto;
  }

  .section-title {
    text-align: center;
    margin-bottom: 40px;

    h2 {
      font-size: 32px;
      font-weight: bold;
      color: #1d2129;
      position: relative;
      display: inline-block;
    }

    h2::after {
      content: '';
      position: absolute;
      bottom: -15px;
      left: 50%;
      transform: translateX(-50%);
      width: 60px;
      height: 3px;
      background-color: #165dff;
    }
  }

  .announcement-list {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    grid-gap: 24px;
  }

  .announcement-card {
    border-radius: 8px;
    overflow: hidden;
    transition: box-shadow 0.3s;

    &:hover {
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
    }
  }

  .card-title {
    font-size: 16px;
    font-weight: 500;
    color: #1d2129;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .card-content {
    padding: 8px 0;
  }

  .card-item {
    margin-bottom: 8px;
    font-size: 14px;
    display: flex;
  }

  .label {
    color: #86909c;
    flex-shrink: 0;
  }

  .value {
    color: #4e5969;
  }

  .card-status {
    margin-top: 16px;
  }

  .view-more {
    grid-column: span 2;
    text-align: center;
    margin-top: 20px;
  }

  .intro-content {
    max-width: 900px;
    margin: 0 auto;
    text-align: center;

    p {
      margin-bottom: 20px;
      line-height: 1.8;
      color: #4e5969;
      font-size: 16px;
    }
  }

  /* 存证流程样式 */
  .process-container {
    margin-top: 40px;
  }

  .process-card {
    background-color: #f7f8fa;
    border-radius: 8px;
    height: 100%;
    padding: 24px;
    transition:
      transform 0.3s,
      box-shadow 0.3s;

    &:hover {
      transform: translateY(-5px);
      box-shadow: 0 10px 20px rgba(0, 0, 0, 0.05);
    }
  }

  .process-icon {
    position: relative;
    font-size: 36px;
    color: #165dff;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
  }

  .process-number {
    position: absolute;
    right: 0;
    top: 0;
    font-size: 14px;
    background-color: #165dff;
    color: white;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
  }

  .process-content {
    h3 {
      font-size: 18px;
      font-weight: 600;
      color: #1d2129;
      margin-bottom: 12px;
    }

    p {
      font-size: 14px;
      color: #4e5969;
      line-height: 1.6;
      margin-bottom: 12px;
    }

    .process-steps {
      font-size: 13px;
      color: #86909c;
      border-top: 1px dashed #e5e6eb;
      padding-top: 12px;
    }
  }

  .scenarios-container {
    margin-top: 40px;
  }

  .scenario-card {
    background-color: #f7f8fa;
    border-radius: 8px;
    display: flex;
    padding: 30px;
    transition:
      transform 0.3s,
      box-shadow 0.3s;

    &:hover {
      transform: translateY(-5px);
      box-shadow: 0 10px 20px rgba(0, 0, 0, 0.05);
    }
  }

  .scenario-icon {
    font-size: 48px;
    color: #165dff;
    margin-right: 20px;
    flex-shrink: 0;
  }

  .scenario-content {
    h3 {
      font-size: 20px;
      font-weight: 600;
      color: #1d2129;
      margin-bottom: 12px;
    }

    p {
      font-size: 14px;
      color: #4e5969;
      line-height: 1.6;
      margin-bottom: 20px;
    }
  }

  .services-management {
    background-color: #f7f8fa;
    padding: 60px 0;
  }

  .service-container {
    max-width: 1000px;
    margin: 0 auto;
  }

  .service-col {
    text-align: center;
    padding: 20px;

    h3 {
      font-size: 18px;
      font-weight: 600;
      color: #1d2129;
      margin-bottom: 20px;
    }
  }

  .contact-us {
    padding: 60px 0;
  }

  .contact-content {
    max-width: 800px;
    margin: 0 auto;
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    grid-gap: 24px;
  }

  .contact-item {
    display: flex;
    flex-direction: column;
    gap: 8px;
  }

  .contact-label {
    font-size: 14px;
    font-weight: 600;
    color: #1d2129;
  }

  .contact-value {
    font-size: 16px;
    color: #4e5969;
  }

  @media (max-width: 768px) {
    .announcement-list {
      grid-template-columns: 1fr;
    }

    .view-more {
      grid-column: span 1;
    }

    .contact-content {
      grid-template-columns: 1fr;
    }
  }

  @media (max-width: 992px) {
    .process-container .arco-col {
      width: 50%;
      margin-bottom: 20px;
    }
  }

  @media (max-width: 576px) {
    .process-container .arco-col {
      width: 100%;
    }
  }
</style>
