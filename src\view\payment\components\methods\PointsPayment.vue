<template>
  <div class="points-payment-container">
    <div
      class="payment-method"
      :class="{
        selected: isSelected,
        disabled: !method.enabled || userPoints <= 0,
      }"
    >
      <div class="method-header" @click="handleToggle">
        <div class="method-content">
          <div class="method-icon">
            <icon-star class="icon" />
          </div>

          <div class="method-info">
            <div class="method-name">{{ method.name }}</div>
            <div class="method-description">
              可用积分：{{ formatPoints(userPoints) }}（约¥{{ formatAmount(pointsValue) }}）
            </div>
          </div>

          <div class="method-status">
            <a-switch
              :model-value="isSelected"
              :disabled="!method.enabled || userPoints <= 0"
              size="small"
              @change="handleToggle"
            />
          </div>
        </div>
      </div>

      <!-- 积分输入区域 -->
      <div v-if="isSelected" class="points-input-section">
        <div class="points-controls">
          <div class="input-group">
            <label class="input-label">使用积分</label>
            <div class="input-wrapper">
              <a-input-number
                :model-value="points"
                :min="0"
                :max="maxPoints"
                :precision="0"
                :step="pointsRate"
                placeholder="请输入使用积分"
                class="points-input"
                @update:model-value="handlePointsChange"
              >
                <template #suffix>积分</template>
              </a-input-number>
              <a-button type="text" size="small" class="max-button" @click="useMaxPoints">
                全部
              </a-button>
            </div>
          </div>

          <div class="points-slider">
            <a-slider
              :model-value="sliderValue"
              :max="100"
              :step="1"
              :show-tooltip="false"
              @update:model-value="handleSliderChange"
            />
            <div class="slider-labels">
              <span>0积分</span>
              <span>{{ formatPoints(maxPoints) }}</span>
            </div>
          </div>
        </div>

        <!-- 积分信息 -->
        <div class="points-info">
          <div class="info-row">
            <span class="info-label">当前积分：</span>
            <span class="info-value">{{ formatPoints(userPoints) }}</span>
          </div>
          <div class="info-row">
            <span class="info-label">使用积分：</span>
            <span class="info-value highlight">{{ formatPoints(points) }}</span>
          </div>
          <div class="info-row">
            <span class="info-label">抵扣金额：</span>
            <span class="info-value highlight">¥{{ formatAmount(pointsValue) }}</span>
          </div>
          <div class="info-row">
            <span class="info-label">剩余积分：</span>
            <span class="info-value">{{ formatPoints(userPoints - points) }}</span>
          </div>
        </div>

        <!-- 积分兑换说明 -->
        <div class="points-rate-info">
          <a-alert type="info" size="small" :show-icon="false">
            <template #icon>
              <icon-info-circle />
            </template>
            <div class="rate-content">
              <p>兑换比例：{{ pointsRate }}积分 = ¥1.00</p>
              <p>积分抵扣无手续费，优先使用即将过期的积分</p>
            </div>
          </a-alert>
        </div>

        <!-- 积分不足提示 -->
        <div v-if="points > userPoints" class="insufficient-warning">
          <a-alert type="warning" size="small" :show-icon="false">
            <template #icon>
              <icon-exclamation-circle />
            </template>
            积分不足，请调整使用数量或选择其他支付方式
          </a-alert>
        </div>

        <!-- 快捷积分选择 -->
        <div v-if="quickPointsAmounts.length > 0" class="quick-points">
          <div class="quick-label">快捷选择：</div>
          <div class="quick-buttons">
            <a-button
              v-for="quickPoints in quickPointsAmounts"
              :key="quickPoints"
              size="small"
              type="outline"
              :class="{ active: points === quickPoints }"
              @click="handleQuickPointsSelect(quickPoints)"
            >
              {{ formatPoints(quickPoints) }}
            </a-button>
          </div>
        </div>
      </div>

      <!-- 不可用提示 -->
      <div v-if="!method.enabled || userPoints <= 0" class="disabled-reason">
        <a-tag color="gray" size="small">
          {{ getDisabledReason() }}
        </a-tag>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { computed, ref, watch } from 'vue';
  import { IconStar, IconInfoCircle, IconExclamationCircle } from '@arco-design/web-vue/es/icon';
  import type { PaymentMethod } from '@/http/api/payment/types';

  // Props
  interface Props {
    method: PaymentMethod;
    userPoints: number;
    pointsRate: number; // 多少积分等于1元
    maxPoints: number;
    points: number;
  }

  const props = defineProps<Props>();

  // Emits
  const emit = defineEmits(['update:points', 'toggle']);

  // 本地状态
  const isSelected = ref(props.points > 0);

  // 计算属性
  const pointsValue = computed(() => {
    return props.points / props.pointsRate;
  });

  const sliderValue = computed(() => {
    if (props.maxPoints <= 0) return 0;
    return (props.points / props.maxPoints) * 100;
  });

  const quickPointsAmounts = computed(() => {
    const amounts: number[] = [];
    const max = Math.min(props.userPoints, props.maxPoints);

    // 基于积分兑换比例生成快捷选项
    const baseAmounts = [
      props.pointsRate * 1, // 1元对应积分
      props.pointsRate * 5, // 5元对应积分
      props.pointsRate * 10, // 10元对应积分
      props.pointsRate * 20, // 20元对应积分
      props.pointsRate * 50, // 50元对应积分
    ];

    baseAmounts.forEach(amount => {
      if (amount <= max && amount > 0) {
        amounts.push(amount);
      }
    });

    // 添加最大可用积分
    if (max > 0 && !amounts.includes(max)) {
      amounts.push(max);
    }

    return amounts.filter(amount => amount <= max);
  });

  // 方法
  function handleToggle() {
    if (!props.method.enabled || props.userPoints <= 0) return;

    const newSelected = !isSelected.value;
    isSelected.value = newSelected;

    if (newSelected) {
      // 启用时设置默认积分
      const defaultPoints = Math.min(props.userPoints, props.maxPoints);
      emit('update:points', defaultPoints);
    } else {
      // 禁用时清零
      emit('update:points', 0);
    }

    emit('toggle', newSelected);
  }

  function handlePointsChange(value: number | undefined) {
    const points = Math.max(0, Math.min(value || 0, props.maxPoints, props.userPoints));
    emit('update:points', points);

    // 如果积分为0，自动取消选择
    if (points === 0) {
      isSelected.value = false;
      emit('toggle', false);
    } else if (!isSelected.value) {
      isSelected.value = true;
      emit('toggle', true);
    }
  }

  function handleSliderChange(value: number | [number, number]) {
    const numValue = Array.isArray(value) ? value[0] : value;
    const points = Math.floor((numValue / 100) * props.maxPoints);
    handlePointsChange(points);
  }

  function useMaxPoints() {
    const maxPoints = Math.min(props.userPoints, props.maxPoints);
    handlePointsChange(maxPoints);
  }

  function handleQuickPointsSelect(points: number) {
    handlePointsChange(points);
  }

  function formatPoints(points: number): string {
    return points.toLocaleString();
  }

  function formatAmount(amount: number): string {
    return amount.toFixed(2);
  }

  function getDisabledReason(): string {
    if (!props.method.enabled) {
      return '积分支付暂不可用';
    }
    if (props.userPoints <= 0) {
      return '积分不足';
    }
    return '暂不可用';
  }

  // 监听外部积分变化
  watch(
    () => props.points,
    newPoints => {
      isSelected.value = newPoints > 0;
    }
  );
</script>

<style scoped lang="scss">
  .points-payment-container {
    width: 100%;
  }

  .payment-method {
    border: 1px solid #e5e6eb;
    border-radius: 8px;
    background-color: #ffffff;
    transition: all 0.3s ease;

    &:hover:not(.disabled) {
      border-color: $primary-color;
      box-shadow: 0 2px 8px rgba(22, 93, 255, 0.1);
    }

    &.selected {
      border-color: $primary-color;
      background-color: #f0f8ff;
    }

    &.disabled {
      background-color: #f7f8fa;
      border-color: #e5e6eb;
      opacity: 0.6;

      .method-content {
        opacity: 0.5;
      }
    }
  }

  .method-header {
    padding: 16px;
    cursor: pointer;
  }

  .method-content {
    display: flex;
    align-items: center;
    gap: 12px;
  }

  .method-icon {
    flex-shrink: 0;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 6px;
    background-color: #fff7e6;

    .icon {
      font-size: 20px;
      color: $warning-color;
    }
  }

  .method-info {
    flex: 1;

    .method-name {
      font-size: 15px;
      font-weight: 500;
      color: $text-color;
      margin-bottom: 4px;
    }

    .method-description {
      font-size: 13px;
      color: $text-color-secondary;
    }
  }

  .method-status {
    flex-shrink: 0;
  }

  .points-input-section {
    padding: 0 16px 16px;
    border-top: 1px solid #e8f3ff;
  }

  .points-controls {
    margin-bottom: 16px;
  }

  .input-group {
    margin-bottom: 12px;

    .input-label {
      display: block;
      font-size: 13px;
      color: $text-color-secondary;
      margin-bottom: 8px;
    }

    .input-wrapper {
      display: flex;
      gap: 8px;
      align-items: center;

      .points-input {
        flex: 1;
      }

      .max-button {
        color: $primary-color;
        font-size: 12px;
        padding: 4px 8px;

        &:hover {
          background-color: #e8f3ff;
        }
      }
    }
  }

  .points-slider {
    .slider-labels {
      display: flex;
      justify-content: space-between;
      margin-top: 4px;
      font-size: 12px;
      color: $text-color-tertiary;
    }
  }

  .points-info {
    background-color: #fff7e6;
    border-radius: 6px;
    padding: 12px;
    margin-bottom: 12px;

    .info-row {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 6px;

      &:last-child {
        margin-bottom: 0;
      }

      .info-label {
        font-size: 13px;
        color: $text-color-secondary;
      }

      .info-value {
        font-size: 13px;
        color: $text-color;
        font-weight: 500;

        &.highlight {
          color: $warning-color;
          font-weight: 600;
        }
      }
    }
  }

  .points-rate-info {
    margin-bottom: 12px;

    .rate-content {
      p {
        margin: 0 0 4px 0;
        font-size: 12px;
        color: $text-color-secondary;

        &:last-child {
          margin-bottom: 0;
        }
      }
    }

    :deep(.arco-alert) {
      padding: 8px 12px;
      background-color: #f0f8ff;
      border-color: #d1e7ff;
    }
  }

  .insufficient-warning {
    margin-bottom: 12px;

    :deep(.arco-alert) {
      padding: 8px 12px;
    }
  }

  .quick-points {
    display: flex;
    align-items: center;
    gap: 8px;
    flex-wrap: wrap;

    .quick-label {
      font-size: 13px;
      color: $text-color-secondary;
      white-space: nowrap;
    }

    .quick-buttons {
      display: flex;
      gap: 6px;
      flex-wrap: wrap;

      .arco-btn {
        font-size: 12px;
        height: 28px;
        padding: 0 8px;

        &.active {
          background-color: $warning-color;
          border-color: $warning-color;
          color: white;
        }
      }
    }
  }

  .disabled-reason {
    padding: 8px 16px;
    border-top: 1px solid #f2f3f5;
  }

  // 响应式设计
  @media (max-width: 768px) {
    .method-header {
      padding: 12px;
    }

    .method-content {
      gap: 10px;
    }

    .method-icon {
      width: 36px;
      height: 36px;

      .icon {
        font-size: 18px;
      }
    }

    .points-input-section {
      padding: 0 12px 12px;
    }

    .points-info {
      padding: 10px;
    }

    .quick-points {
      flex-direction: column;
      align-items: flex-start;
      gap: 6px;

      .quick-buttons {
        width: 100%;
      }
    }
  }
</style>
