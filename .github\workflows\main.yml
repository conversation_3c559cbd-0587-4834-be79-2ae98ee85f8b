name: CI/CD

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

jobs:
  build:
    runs-on: ubuntu-latest
    
    strategy:
      matrix:
        node-version: [18.x]
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Use Node.js ${{ matrix.node-version }}
      uses: actions/setup-node@v3
      with:
        node-version: ${{ matrix.node-version }}
        cache: 'npm'
    
    - name: Setup PNPM
      uses: pnpm/action-setup@v2
      with:
        version: 8.7.0
        run_install: false
    
    - name: Get pnpm store directory
      shell: bash
      run: |
        echo "STORE_PATH=$(pnpm store path --silent)" >> $GITHUB_ENV
    
    - uses: actions/cache@v3
      name: Setup pnpm cache
      with:
        path: ${{ env.STORE_PATH }}
        key: ${{ runner.os }}-pnpm-store-${{ hashFiles('**/pnpm-lock.yaml') }}
        restore-keys: |
          ${{ runner.os }}-pnpm-store-
    
    - name: Install dependencies
      run: pnpm install
    
    - name: Lint
      run: pnpm run lint
    
    - name: Run tests
      run: pnpm run test:unit
    
    - name: Build
      run: pnpm run build
    
    - name: Upload build artifacts
      uses: actions/upload-artifact@v3
      with:
        name: build-output
        path: dist

  deploy:
    needs: build
    if: github.event_name == 'push' && github.ref == 'refs/heads/main'
    runs-on: ubuntu-latest
    
    steps:
    - name: Download build artifacts
      uses: actions/download-artifact@v3
      with:
        name: build-output
        path: dist
    
    # 这里可以添加部署步骤，例如:
    # - 部署到服务器
    # - 或者部署到云存储
    # - 或者部署到容器平台
    
    - name: Deploy to Server
      # 这里使用SFTP或其他部署方式
      run: |
        echo "部署到服务器的步骤将在这里执行"
        # 实际部署命令需要根据具体服务器配置 