import http from "@/http/sopHttp";
// 获取所有订单列表
export async function getOrderList(data: any) {
	return http.get("/portal/setmeal/pageInfo", data)
}
export async function getItemInfo(data: any) {
	return http.get("/portal/setmeal/getMealItem", data)
}
// 获取订单详情
export async function getDetails(data: any) {
	return http.get("/portal/setmeal/getDetails", data)
}
export async function buyMeal(params: any) {
	return http.postwww("/portal/setmeal/buyMeal", params)
}