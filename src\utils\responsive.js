/**
 * 响应式工具函数
 */

// 屏幕尺寸断点
const breakpoints = {
  xs: 480, // 移动设备 - 小屏幕
  sm: 640, // 移动设备 - 中等屏幕
  md: 768, // 平板设备 - 小屏幕
  lg: 1024, // 平板设备 - 大屏幕/桌面设备 - 小屏幕
  xl: 1280, // 桌面设备 - 中等屏幕
  xxl: 1440, // 桌面设备 - 大屏幕
};

/**
 * 检测当前屏幕尺寸
 * @returns {Object} 包含各种屏幕尺寸判断的对象
 */
export const useScreenSize = () => {
  const getScreenSize = () => {
    const width = window.innerWidth;
    return {
      width,
      isXs: width < breakpoints.sm,
      isSm: width >= breakpoints.sm && width < breakpoints.md,
      isMd: width >= breakpoints.md && width < breakpoints.lg,
      isLg: width >= breakpoints.lg && width < breakpoints.xl,
      isXl: width >= breakpoints.xl && width < breakpoints.xxl,
      isXxl: width >= breakpoints.xxl,
      isMobile: width < breakpoints.md,
      isTablet: width >= breakpoints.md && width < breakpoints.lg,
      isDesktop: width >= breakpoints.lg,
    };
  };

  return getScreenSize();
};

/**
 * 监听屏幕尺寸变化
 * @param {Function} callback 屏幕尺寸变化时的回调函数
 * @returns {Function} 取消监听的函数
 */
export const useScreenSizeListener = callback => {
  if (typeof window === 'undefined') return () => {};

  const handleResize = () => {
    callback(useScreenSize());
  };

  window.addEventListener('resize', handleResize);

  // 返回取消监听的函数
  return () => {
    window.removeEventListener('resize', handleResize);
  };
};

/**
 * 根据屏幕尺寸返回不同的值
 * @param {Object} options 不同屏幕尺寸对应的值
 * @returns {any} 当前屏幕尺寸对应的值
 */
export const responsiveValue = options => {
  const { xs, sm, md, lg, xl, xxl } = options;
  const screen = useScreenSize();

  if (screen.isXxl && xxl !== undefined) return xxl;
  if (screen.isXl && xl !== undefined) return xl;
  if (screen.isLg && lg !== undefined) return lg;
  if (screen.isMd && md !== undefined) return md;
  if (screen.isSm && sm !== undefined) return sm;
  return xs;
};
