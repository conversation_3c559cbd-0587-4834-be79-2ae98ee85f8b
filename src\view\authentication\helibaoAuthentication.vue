<template>
  <div class="p-4" style="width: 1200px; margin: 0 auto; padding: 0 100px; min-height: 720px">
    <a-form :model="formData" @submit="submitFun">
      <a-form-item
        label="开户名"
        field="accountName"
        :rules="[{ required: true, message: '开户名' }]"
      >
        <a-input v-model="formData.accountName" placeholder="开户名" />
      </a-form-item>
      <a-form-item
        label="银行卡号"
        field="bankNo"
        :rules="[{ required: true, message: '银行卡号' }]"
      >
        <a-input v-model="formData.bankNo" placeholder="银行卡号" />
      </a-form-item>
      <a-form-item
        label="证件号码"
        field="idCardNo"
        :rules="[{ required: true, message: '证件号码' }]"
      >
        <a-input v-model="formData.idCardNo" placeholder="证件号码" />
      </a-form-item>
      <a-form-item
        label="预留手机号"
        field="legalMobile"
        :rules="[{ required: true, message: '预留手机号' }]"
      >
        <a-input v-model="formData.legalMobile" placeholder="预留手机号" />
      </a-form-item>
      <a-form-item
        label="持卡人名"
        field="legalName"
        :rules="[{ required: true, message: '持卡人名' }]"
      >
        <a-input v-model="formData.legalName" placeholder="持卡人名" />
      </a-form-item>
      <a-form-item label="签约名" field="signName" :rules="[{ required: true, message: '签约名' }]">
        <a-input v-model="formData.signName" placeholder="签约名" />
      </a-form-item>
      <a-form-item
        label="联系地址"
        field="address"
        :rules="[{ required: true, message: '联系地址' }]"
      >
        <a-input v-model="formData.address" placeholder="联系地址" />
      </a-form-item>
      <a-form-item label="联行号" field="bankCode" :rules="[{ required: true, message: '联行号' }]">
        <a-input v-model="bankName" placeholder="联行号" />
      </a-form-item>
      <div class="pageBtns">
        <a-button type="primary" html-type="submit"> 确认信息，开始认证 </a-button>
      </div>
    </a-form>
  </div>
</template>

<script lang="ts" setup>
  import { ref, reactive, onMounted } from 'vue';
  import { Message } from '@arco-design/web-vue';
  import {
    heliMerchantRegister,
    portalUserInfo,
    getBranchBankList,
  } from '@/http/api/authentication/helibao';

  const formData = reactive<any>({
    accountName: '',
    bankNo: '',
    idCardNo: '',
    legalMobile: '',
    legalName: '',
    signName: '',
    address: '',
    bankCode: '',
  });
  const bankName = ref<any>('');

  const submitFun = async () => {
    let heliRes = await heliMerchantRegister({ ...formData });
    console.log(heliRes);
  };

  // 初始化页面
  const getPageInit = async () => {
    let pageDataRes: any = await portalUserInfo();
    console.log(pageDataRes);
    formData.accountName = pageDataRes.bankAcctName;
    formData.bankNo = pageDataRes.bankAcctNum;
    formData.idCardNo = pageDataRes.idCardNo;
    formData.legalMobile = pageDataRes.legalPhone;
    formData.legalName = pageDataRes.legalName;
    formData.signName = pageDataRes.registerName;
    formData.address = pageDataRes.businessAddress;
    formData.bankCode = pageDataRes.bankCode;
    bankName.value = pageDataRes.branchBankName;
  };

  onMounted(async () => {
    getPageInit();
  });
</script>

<style lang="scss" scoped>
  .pageBtns {
    padding: 50px 200px;
    display: flex;
    justify-content: space-around;
  }
</style>
