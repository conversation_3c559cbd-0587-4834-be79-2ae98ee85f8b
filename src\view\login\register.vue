<template>
  <div class="login-wrapper bg-white" :style="{ backgroundImage: `url(${loginBg})` }">
    <div class="login-container">
      <div class="text-4xl font-semibold">注册到</div>
      <div class="text-4xl font-semibold mt-1">文创链开放平台</div>
      <div class="mt-6 mb-6">
        <a-space>
          <span class="text-gray-500">已有账号?</span>
          <span class="cursor-pointer" @click="pageJump('login')">去登录</span>
        </a-space>
      </div>

      <!-- 注册方式切换 -->
      <div class="mb-6">
        <a-radio-group v-model="registerType" type="button" size="large">
          <a-radio value="mobile"> 手机号注册 </a-radio>
          <a-radio value="account"> 账号注册 </a-radio>
        </a-radio-group>
      </div>

      <a-form ref="formRef" size="large" :model="form" @submit-success="handleSubmit">
        <!-- 手机号注册方式 -->
        <template v-if="registerType === 'mobile'">
          <a-form-item
            field="mobile"
            :hide-label="true"
            :hide-asterisk="true"
            :rules="[
              { required: true, message: '请输入手机号' },
              { match: /^1[3-9]\d{9}$/, message: '请输入正确的手机号格式' },
            ]"
            :validate-trigger="['change', 'input']"
          >
            <a-input
              v-model="form.mobile"
              :style="{ width: '320px' }"
              placeholder="请输入手机号"
              allow-clear
            />
          </a-form-item>

          <a-form-item
            field="smsCode"
            :hide-label="true"
            :hide-asterisk="true"
            :rules="[
              { required: true, message: '请输入短信验证码' },
              {
                match: /^\d{4,6}$/,
                message: '验证码应为4-6位数字',
              },
            ]"
          >
            <div class="flex items-center gap-2">
              <a-input
                v-model="form.smsCode"
                :style="{ width: '220px' }"
                placeholder="请输入短信验证码"
                allow-clear
                maxlength="6"
                @input="handleSmsCodeInput"
              />
              <a-button
                v-if="countdown === 60"
                type="primary"
                :disabled="!form.mobile || !/^1[3-9]\d{9}$/.test(form.mobile)"
                @click="sendSmsCode"
              >
                发送验证码
              </a-button>
              <a-button v-else type="primary" disabled> {{ countdown }}s </a-button>
            </div>
          </a-form-item>
        </template>

        <!-- 账号注册方式 -->
        <template v-else>
          <a-form-item
            field="username"
            :hide-label="true"
            :hide-asterisk="true"
            :rules="[
              { required: true, message: '请输入账号，4-30个字符' },
              {
                match: /^[a-zA-Z0-9_-]{4,30}$/,
                message: '账号应为4-30位字母、数字、下划线或短横线',
              },
            ]"
            :validate-trigger="['change', 'input']"
          >
            <a-input
              v-model="form.username"
              :style="{ width: '320px' }"
              placeholder="请输入账号，4-30个字符"
              allow-clear
            />
          </a-form-item>

          <a-form-item
            field="password"
            :hide-label="true"
            :hide-asterisk="true"
            :rules="[{ required: true, message: '请输入密码' }, { validator: validatePassword }]"
            :validate-trigger="['change', 'input']"
          >
            <a-input-password
              v-model="form.password"
              :style="{ width: '320px' }"
              placeholder="请输入密码"
              allow-clear
            />
          </a-form-item>

          <a-form-item
            field="passwordAgain"
            :hide-label="true"
            :hide-asterisk="true"
            :rules="[
              { required: true, message: '请再次输入密码' },
              { validator: validatePasswordAgain },
            ]"
            :validate-trigger="['change', 'input']"
          >
            <a-input-password
              v-model="form.passwordAgain"
              :style="{ width: '320px' }"
              placeholder="请再次输入密码"
              allow-clear
            />
          </a-form-item>

          <a-form-item
            field="mobile"
            :hide-label="true"
            :hide-asterisk="true"
            :rules="[
              { required: true, message: '请输入手机号' },
              { match: /^1[3-9]\d{9}$/, message: '请输入正确的手机号格式' },
            ]"
            :validate-trigger="['change', 'input']"
          >
            <a-input
              v-model="form.mobile"
              :style="{ width: '320px' }"
              placeholder="请输入手机号"
              allow-clear
            />
          </a-form-item>

          <a-form-item
            field="smsCode"
            :hide-label="true"
            :hide-asterisk="true"
            :rules="[
              { required: true, message: '请输入短信验证码' },
              {
                match: /^\d{4,6}$/,
                message: '验证码应为4-6位数字',
              },
            ]"
          >
            <div class="flex items-center gap-2">
              <a-input
                v-model="form.smsCode"
                :style="{ width: '220px' }"
                placeholder="请输入短信验证码"
                allow-clear
                maxlength="6"
                @input="handleSmsCodeInput"
              />
              <a-button
                v-if="countdown === 60"
                type="primary"
                :disabled="!form.mobile || !/^1[3-9]\d{9}$/.test(form.mobile)"
                @click="sendSmsCode"
              >
                发送验证码
              </a-button>
              <a-button v-else type="primary" disabled> {{ countdown }}s </a-button>
            </div>
          </a-form-item>
        </template>

        <a-form-item :hide-label="true" :hide-asterisk="true">
          <a-button type="primary" long html-type="submit"> 注 册 </a-button>
        </a-form-item>
      </a-form>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { getAssetsFile } from '@/utils/tool';
  import { ref, reactive, watch } from 'vue';
  import { Message } from '@arco-design/web-vue';
  import md5 from 'js-md5';
  import JsEncrypt from 'jsencrypt';
  import {
    memberRegister,
    memberSendSmsCode,
    type MemberRegisterParams,
    type MemberSendSmsCodeParams,
  } from '@/http/api/login';
  import { getPublicKey } from '@/http/api/global/index';
  import { useRouter } from 'vue-router';
  let imgUrl = getAssetsFile('logo-3.png');
  let loginBg = getAssetsFile('login-bg.png');
  const router = useRouter();

  // 密码验证函数
  const validatePassword = (value: string, cb: (error?: string) => void) => {
    if (!value) {
      cb();
      return;
    }

    // 检查长度
    if (value.length < 4 || value.length > 16) {
      cb('密码长度应为4-16位字符');
      return;
    }

    // 检查是否包含空格
    if (/\s/.test(value)) {
      cb('密码不能包含空格');
      return;
    }

    // 统计包含的字符类型
    let typeCount = 0;

    // 检查是否包含数字
    if (/[0-9]/.test(value)) {
      typeCount++;
    }

    // 检查是否包含小写字母
    if (/[a-z]/.test(value)) {
      typeCount++;
    }

    // 检查是否包含大写字母
    if (/[A-Z]/.test(value)) {
      typeCount++;
    }

    // 检查是否包含特殊字符（除空格外）
    if (/[^0-9a-zA-Z\s]/.test(value)) {
      typeCount++;
    }

    // 至少包含2种类型
    if (typeCount < 2) {
      cb('密码4-16位字符，且必须同时包含数字、大小写字母和除空格外的特殊字符中的任意2种');
      return;
    }

    cb();
  };

  // 确认密码验证函数
  const validatePasswordAgain = (value: string, cb: (error?: string) => void) => {
    if (value !== form.password) {
      cb('两次密码输入不一致');
    } else {
      cb();
    }
  };

  // 注册方式：mobile-手机号注册，account-账号注册
  const registerType = ref('mobile');

  // 登录框表单绑定
  const form = reactive({
    password: '',
    passwordAgain: '',
    smsCode: '',
    username: '',
    mobile: '',
    type: 2,
  });
  // 图形验证码
  const code = reactive({
    src: '/api/code',
    value: '',
    len: 4,
    type: 'image',
  });
  const countdown = ref(60); // 验证码倒计时

  // 处理验证码输入，只允许数字
  const handleSmsCodeInput = (value: string) => {
    // 只保留数字字符
    const numericValue = value.replace(/\D/g, '');
    form.smsCode = numericValue;
  };

  // 发送验证码
  const sendSmsCode = () => {
    if (!form.mobile || !/^1[3-9]\d{9}$/.test(form.mobile)) {
      Message.error('请输入正确的手机号');
      return;
    }

    // 调试：检查环境变量和console状态
    // console.error('Environment check:', {
    //   NODE_ENV: import.meta.env.NODE_ENV,
    //   MODE: import.meta.env.MODE,
    //   PROD: import.meta.env.PROD,
    //   VITE_DROP_CONSOLE: import.meta.env.VITE_DROP_CONSOLE,
    //   consoleLogExists: typeof console.log === 'function',
    //   consoleLogContent: console.log.toString(),
    // });

    let params = {
      mobile: form.mobile,
      scene: '5', // 会员用户-手机号注册场景
    };

    // 调试：输出请求参数
    // console.error('📱 SMS API Request params:', params);
    // console.error('📱 About to call memberSendSmsCode...');

    // 使用会员短信验证码接口
    memberSendSmsCode(params)
      .then((res: any) => {
        // 调试：输出响应数据
        // console.log('📱 SMS API Response:', res);
        // console.error('📱 SMS API Response (backup):', res);

        // 优化判断逻辑：code为0表示成功，或者data为true表示成功
        if (res.code === 0 || res.data === true) {
          Message.success('验证码发送成功！请注意查收');
          setTime();
        } else {
          // 失败情况：显示后台返回的错误信息，如果没有则显示默认信息
          const errorMsg = res.msg || res.message || '发送失败，请重试';
          Message.error(errorMsg);
        }
      })
      .catch((error: any) => {
        // 调试：输出错误信息
        // console.error('📱 SMS API Error:', error);
        // console.error('📱 Error details:', {
        //   message: error.message,
        //   response: error.response,
        //   status: error.response?.status,
        //   data: error.response?.data,
        // });

        // 网络错误或其他异常
        const errorMsg =
          error.msg || error.message || error.response?.data?.msg || '网络异常，请重试';
        Message.error(errorMsg);
      });
  };

  // 页面跳转
  const pageJump = (d: string) => {
    router.replace('/' + d);
  };

  // 清空表单数据
  const clearForm = () => {
    form.password = '';
    form.passwordAgain = '';
    form.smsCode = '';
    form.username = '';
    form.mobile = '';
  };

  // 监听注册方式切换，清空表单
  watch(registerType, () => {
    clearForm();
  });

  // 提交方法
  const handleSubmit = (val: object) => {
    let params = {
      ...form,
      registerType: registerType.value,
    };

    // 根据注册方式进行不同的验证
    if (registerType.value === 'mobile') {
      // 手机号注册验证
      if (!params.mobile || !/^1[3-9]\d{9}$/.test(params.mobile)) {
        Message.error('请输入正确的手机号格式！');
        return;
      }
      if (!params.smsCode) {
        Message.error('请输入短信验证码！');
        return;
      }
      if (!/^\d{4,6}$/.test(params.smsCode)) {
        Message.error('验证码应为4-6位数字！');
        return;
      }
    } else {
      // 账号注册验证
      let usernameReg = /^[a-zA-Z0-9_-]{3,20}$/;
      if (!usernameReg.test(params.username)) {
        Message.error('账号应为3-20位字母、数字、下划线或短横线！');
        return;
      }
      if (params.password != params.passwordAgain) {
        Message.error('两次密码不一致！');
        return;
      }
      if (!params.mobile || !/^1[3-9]\d{9}$/.test(params.mobile)) {
        Message.error('请输入正确的手机号格式！');
        return;
      }
      if (!params.smsCode) {
        Message.error('请输入短信验证码！');
        return;
      }
      if (!/^\d{4,6}$/.test(params.smsCode)) {
        Message.error('验证码应为4-6位数字！');
        return;
      }
      // 密码加密
      // params.password = md5(params.password);
    }

    // 调试：输出注册参数
    console.error('📝 Register params:', {
      registerType: registerType.value,
      mobile: params.mobile,
      smsCode: params.smsCode,
      username: params.username,
      hasPassword: !!params.password,
    });

    // 使用统一的会员注册接口（基于接口文档）
    const executeRegister = async () => {
      try {
        // 构建注册参数（基于接口文档）
        const registerParams: MemberRegisterParams = {
          registerType: registerType.value === 'mobile' ? '1' : '2',
          code: params.smsCode, // 注意：接口文档中参数名是 code，不是 smsCode
        };

        if (registerType.value === 'mobile') {
          // 手机号注册
          registerParams.mobile = params.mobile;
        } else {
          // 账号注册
          registerParams.username = params.username;
          registerParams.password = params.password;
          registerParams.mobile = params.mobile;
        }

        if (import.meta.env.DEV) {
          console.log('📝 Calling memberRegister with:', registerParams);
        }

        const result = await memberRegister(registerParams);

        // 注册成功
        if (result.code === 0) {
          Message.success('会员注册成功！');

          // 如果返回了登录信息，可以直接登录
          if (result.data?.accessToken) {
            // 这里可以保存token并跳转到首页
            // userStore.setToken(result.data.accessToken);
            // router.replace('/home');
          } else {
            // 否则跳转到登录页
            router.replace('/login');
          }
        } else {
          // 业务错误
          const errorMsg = result.msg || '注册失败，请重试';
          Message.error(errorMsg);
        }
      } catch (error: any) {
        // 参数验证错误或网络错误
        if (import.meta.env.DEV) {
          console.error('📝 Register API Error:', error);
        }

        let errorMsg = '注册失败，请重试';

        if (error.message) {
          // API函数抛出的参数验证错误
          errorMsg = error.message;
        } else if (error.msg) {
          // 后台返回的业务错误
          errorMsg = error.msg;
        } else if (error.response?.data?.msg) {
          // HTTP错误响应中的错误信息
          errorMsg = error.response.data.msg;
        }

        Message.error(errorMsg);
      }
    };

    // 执行注册
    executeRegister();
  };
  // 倒计时
  const setTime = () => {
    if (countdown.value == 1) {
      countdown.value = 60;
      return;
    } else {
      countdown.value--;
    }
    setTimeout(() => {
      setTime();
    }, 1000);
  };
  // 生成随机数
  // const randomLenNum = (len: number, date: boolean = true) => {
  //   let random = ''
  //   random = Math.ceil(Math.random() * 100000000000000).toString().substr(0, len || 4)
  //   if (date) random = random + Date.now()
  //   return random
  // }
  // 刷新图形验证码
  // const refreshCode = () => {
  //   form.code = "";
  //   form.randomStr = randomLenNum(code.len, true);
  //   code.type === "text" ?
  //     (code.value = randomLenNum(code.len)) :
  //     (code.src = `/api/code?randomStr=${form.randomStr}`);
  // }
  // refreshCode()

  const getPublicKey2 = () => {
    console.log(1);
    getPublicKey().then(res => {
      console.log(1, res);
    });
  };
</script>

<style lang="scss" scoped>
  .login-wrapper {
    height: 100vh;
    display: flex;
    flex-direction: column;
    background-size: cover;
    background-position: 100%;
    position: relative;
  }

  .login-container {
    position: absolute;
    top: 22%;
    left: 6%;
    min-height: 500px;
  }
</style>
