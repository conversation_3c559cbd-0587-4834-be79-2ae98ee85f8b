import http from '@/http/sopHttp';

export async function getRecommendOrnewestQueryPage(data: any) {
  const params = { ...data };
  return http.get('/worksCopyrightPut/getRecommendOrnewestQueryPage', params);
}

export async function addAttentionRecord(data: any) {
  const params = { ...data };
  return http.get('/attentionRecord/put', params);
}

export async function getRecommendOrnewest(data: any) {
  const params = { ...data };
  return http.get('/worksCopyrightPut/getRecommendOrnewest', params);
}
// 查询商品详情
export async function getWorksPublicity(data: any) {
  return http.get('/worksCopyrightPut/getWorksPublicity/' + data);
}
// 删除我的关注
export async function deleteAttentionRecord(data: any) {
  return http.delete('/attentionRecord/' + data);
}
// 添加历史浏览
export async function addBrowseRecord(data: any) {
  const params = { ...data };
  return http.get('/browseRecord/put', params);
}
