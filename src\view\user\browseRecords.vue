<template>
  <div class="p-4 bg-grey">
    <a-page-header
      :show-back="false"
      :style="{ background: 'var(--color-bg-2)' }"
      title="历史浏览"
      :subtitle="'共' + searchParams.total + '条'"
    >
      <template v-if="list.length > 0" #extra>
        <a-popconfirm
          content="确认清空历史浏览?"
          ok-text="确认"
          cancel-text="取消"
          @ok="deleteAllBrowseRecordFn"
        >
          <a-button type="text"> 清空全部 </a-button>
        </a-popconfirm>
      </template>
    </a-page-header>
    <div v-if="list.length > 0" class="bg-white px-4">
      <a-row>
        <a-col v-for="(item, index) in list" :key="index" :span="8">
          <!-- <router-link :to="'/shop/detail?id=' + item.worksPutId"> -->
          <a-card hoverable class="mb-6 mx-2" @click="handleLink(item.worksPutId)">
            <template #actions>
              <span
                v-if="item.attentionRecordId"
                class="icon-hover"
                @click.stop="deleteAttentionRecordFn(item)"
              >
                <Icon-Star-Fill />
              </span>
              <span v-else class="icon-hover" @click.stop="addAttentionRecordFun(item)">
                <Icon-Star />
              </span>
              <span class="icon-hover" @click.stop="loginfo">
                <a-popconfirm
                  content="确认删除此条历史浏览?"
                  ok-text="确认"
                  cancel-text="取消"
                  @ok="deleteBrowseRecordFn(item.browseRecordId)"
                >
                  <icon-delete />
                </a-popconfirm>
              </span>
              <span class="icon-hover" @click.stop="shareInternal(item)">
                <IconShareInternal />
              </span>
            </template>
            <template #cover>
              <div
                :style="{
                  height: '278px',
                  overflow: 'hidden',
                }"
              >
                <img
                  :style="{ width: '100%', transform: 'translateY(-20px)' }"
                  alt="dessert"
                  :src="item.cover"
                />
              </div>
            </template>
            <a-card-meta :title="item.worksName">
              <template #avatar>
                <div :style="{ display: 'flex', alignItems: 'center', color: '#1D2129' }">
                  <a-avatar
                    class="mr-2"
                    :style="{ backgroundColor: '#ffffff', width: '22px', height: '22px' }"
                  >
                    <img alt="avatar" :src="priceIcon" />
                  </a-avatar>
                  <a-typography-text>
                    <div>
                      <span class="text-xl">{{ item.priceStart }}</span>
                      <span>.</span>
                      <span class="text-base">{{ item.priceEnd }}</span>
                    </div>
                  </a-typography-text>
                </div>
              </template>
              <template #description>
                <div>{{ item.worksName }}</div>
                <div class="text-sm text-gray-500">浏览日期：{{ item.browseDate }}</div>
              </template>
            </a-card-meta>
          </a-card>
          <!-- </router-link> -->
        </a-col>
      </a-row>
      <div class="py-6 flex justify-center">
        <a-pagination
          size="large"
          :total="searchParams.total"
          :current="searchParams.current"
          :page-size="searchParams.size"
          simple
          @change="getIndexData"
        />
      </div>
    </div>
    <div v-else class="bg-white py-10 flex flex-col">
      <a-empty />
      <router-link to="/shop/search" class="text-center">
        <a-button type="text" @click="handShopleLink"> 去逛逛 </a-button>
      </router-link>
    </div>
  </div>
  <!-- 分享弹窗 -->
  <a-modal :visible="modalVisible" ok-text="复制链接" @ok="modalOk" @cancel="modalVisible = false">
    <template #title> 分享链接 </template>
    <div>{{ shareData.title }}</div>
    <a-link>{{ shareData.url }}</a-link>
  </a-modal>
</template>

<script lang="ts" setup>
  import { getAssetsFile, IntegerToFloat, copyToClip } from '@/utils/tool';
  import { ref, reactive, onMounted } from 'vue';
  import {
    IconStar,
    IconDelete,
    IconShareInternal,
    IconStarFill,
  } from '@arco-design/web-vue/es/icon';
  import {
    browseRecordRecord,
    deleteBrowseRecord,
    addAttentionRecord,
    deleteAttentionRecord,
    deleteAllBrowseRecord,
  } from '@/http/api/user';
  import { Message } from '@arco-design/web-vue';
  import { useRouter } from 'vue-router';

  // 定义浏览记录接口
  interface BrowseItem {
    id: string | number;
    worksPutId: number;
    attentionRecordId: number | null;
    browseRecordId: number;
    cover: string;
    worksName: string;
    browseDate: string;
    price: string | number;
    priceStart: string;
    priceEnd: string;
  }

  // 分享数据接口
  interface ShareData {
    title: string;
    url: string;
  }

  const router = useRouter();

  const priceIcon = getAssetsFile('icons/price-icon.png');

  // 关注列表
  const list = ref<BrowseItem[]>([]);
  const searchParams = ref({
    total: 0,
    current: 1,
    size: 9,
  });
  // 跳转 /shop/search
  const handleLink = (id: number) => {
    router.push({ path: `/shop/detail`, query: { id: id } });
  };
  // 跳转
  const handShopleLink = () => {
    router.push({ path: `/shop/search` });
  };
  const loginfo = () => {
    console.log(1);
  };
  // 查询浏览列表
  const getIndexData = (current?: number) => {
    let params = {
      current: current || searchParams.value.current,
      size: searchParams.value.size,
    };
    browseRecordRecord(params).then((res: any) => {
      let arr = res.records.map(el => {
        el.price = IntegerToFloat(el.price);
        el.priceStart = el.price.toString().split('.')[0];
        el.priceEnd = el.price.toString().split('.')[1];
        return el;
      });
      list.value = arr;
      if (current !== undefined) {
        searchParams.value.current = current;
      }
      searchParams.value.total = res.total;
    });
  };
  getIndexData();

  // 删除单个历史浏览
  const deleteBrowseRecordFn = (id: number) => {
    deleteBrowseRecord(id).then((res: any) => {
      // message.success(res: any)
      getIndexData();
    });
  };
  // 清空历史浏览
  const deleteAllBrowseRecordFn = () => {
    deleteAllBrowseRecord().then(() => {
      getIndexData();
    });
  };

  // 添加关注
  const addAttentionRecordFun = (item: BrowseItem) => {
    let params = {
      worksPutId: item.worksPutId,
    };
    addAttentionRecord(params).then((res: any) => {
      Message.success('关注成功');
      getIndexData();
      // console.log(res)
      // if (res.code == 0) {
      // 	Message.success('关注成功')
      // 	item.attentionRecordId = res.attentionRecordId
      // }
    });
  };
  // 取消关注
  const deleteAttentionRecordFn = (item: BrowseItem) => {
    deleteAttentionRecord(item.attentionRecordId as number).then((res: any) => {
      console.log(res);
      if (res.code == 0) {
        Message.success('取消成功');
        item.attentionRecordId = null;
      }
    });
  };
  // 分享
  const modalVisible = ref(false); // 显示分享弹窗
  const shareData = ref<ShareData>({
    title: '',
    url: '',
  });
  // 分享地址
  const shareInternal = (item: BrowseItem) => {
    shareData.value.url = `${window.location.origin}/#/shop/detail?id=${item.worksPutId}`;
    shareData.value.title = `【国家文化大数据体系·巴蜀文化专业中心】${item.worksName}`;
    modalVisible.value = true;
  };
  // 复制文本
  const modalOk = () => {
    copyToClip(`${shareData.value.title} ${shareData.value.url}`);
    modalVisible.value = false;
    Message.success('复制成功，快去分享吧！');
  };
</script>
<style lang="scss" scoped>
  .icon-hover {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    transition: all 0.1s;
  }

  .icon-hover:hover {
    background-color: rgb(var(--gray-2));
  }
</style>
